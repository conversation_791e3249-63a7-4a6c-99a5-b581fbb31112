# MSMT Provider Portal

A React-based web application for healthcare providers to manage their practice, appointments, and client interactions within the MSMT (Medical Service Management Technology) platform.

## 🚀 Features

- **Provider Profile Management**: Complete profile setup including personal information, education, and professional details
- **Appointment Management**: View, schedule, and manage patient appointments with real-time updates
- **Availability Settings**: Configure working hours and availability across different time zones
- **Partner Network**: Connect and collaborate with other healthcare providers
- **Wallet & Payments**: Track earnings and manage bank account information for payments
- **Real-time Notifications**: Stay updated with appointment changes and system notifications
- **Responsive Design**: Optimized for desktop and mobile devices

## 🛠 Tech Stack

- **Frontend**: React 19 + TypeScript + Vite
- **UI Framework**: Chakra UI v3 with custom theming
- **State Management**: Zustand + TanStack Query (React Query)
- **Routing**: React Router v7 with view transitions
- **Forms**: TanStack Form with Arktype validation
- **HTTP Client**: Axios with network-aware request handling
- **Date Handling**: date-fns
- **Charts**: Recharts for analytics
- **Icons**: React Icons
- **Animations**: Motion (Framer Motion)

## 📋 Prerequisites

- Node.js 18+ or Bun
- Modern web browser with ES2022 support

## 🚀 Getting Started

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd msmt-provider-portal
   ```

2. **Install dependencies**

   ```bash
   bun install
   # or
   npm install
   ```

3. **Environment Setup**

   ```bash
   cp .env.dev .env
   ```

   Configure the following environment variables:

   ```env
   VITE_BASE_SERVICE_URL=<your-api-base-url>
   VITE_BOOKING_SERVICE_URL=<your-booking-service-url>
   VITE_APPT_SESSION_URL=<your-appointment-session-url>
   VITE_LIST_REQUEST_TOKEN=<your-list-request-token>
   VITE_OTP_RESEND_TIMEOUT_SECS=60
   ```

4. **Start development server**

   ```bash
   bun dev
   # or
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173`

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components
│   └── layout/         # Layout components
├── pages/              # Page components
│   ├── appointments/   # Appointment management
│   ├── setup/          # Provider profile setup
│   ├── notifications/  # Notification center
│   └── ...
├── stores/             # Zustand state stores
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── schemas/            # Type definitions and validation
├── config/             # App configuration
└── system/             # Design system and theming
```

## 🔧 Available Scripts

- `bun dev` - Start development server
- `bun build` - Build for production
- `bun preview` - Preview production build
- `bun lint` - Run ESLint

## 🎨 Design System

The application uses a custom design system built on Chakra UI v3 with:

- Custom color tokens and semantic colors
- Responsive breakpoints
- Component variants and sizes
<!-- - Dark/light theme support via next-themes -->

## 🌐 Key Features

### Provider Profile Setup

Multi-step profile completion including:

- Personal information (phone, address, marital status)
- Professional details and education
- Service rates and availability
- Bank account information

### Appointment Management

- Real-time appointment scheduling
- Time zone-aware scheduling
- Appointment status tracking
- Communication mode selection (video, phone, etc.)

### Availability Management

- Weekly schedule configuration
- Time slot management with toggle availability
- Time zone conversion utilities
- Holiday and break scheduling

### Notifications

- Real-time notification system
- Mark as read functionality
- Mobile-responsive notification list
- Notification categorization

## 🔒 Authentication

The application uses JWT-based authentication with:

- Secure token storage using secure-ls
- Automatic token refresh
- OTP verification for sensitive operations
- Session management

## 📱 Mobile Support

- Responsive design for all screen sizes
- Touch-friendly interface
- Mobile-specific navigation
- Progressive Web App (PWA) capabilities

## 🚀 Deployment

The application is configured for deployment on Vercel with:

- Automatic SPA routing via `vercel.json`
- Environment variable configuration
- Production optimizations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is proprietary software for MSMT platform.
Powered by [Nexenno](https://nexenno.com/). Please contact us for any questions or concerns.
