{"name": "msmt-provider", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/charts": "^3.17.0", "@chakra-ui/react": "^3.24.0", "@emotion/react": "^11.14.0", "@tanstack/react-form": "^1.11.1", "@tanstack/react-query": "^5.76.0", "arktype": "^2.1.20", "axios": "^1.9.0", "date-fns": "^4.1.0", "eventemitter3": "^5.0.1", "immer": "^10.1.1", "lodash.capitalize": "^4.2.1", "lodash.groupby": "^4.6.0", "lodash.isequal": "^4.5.0", "lodash.omit": "^4.5.0", "lodash.omitby": "^4.6.0", "lodash.pick": "^4.4.0", "lodash.take": "^4.1.1", "lodash.tolower": "^4.1.2", "motion": "^12.11.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-icons": "^5.5.0", "react-router": "^7.6.0", "react-use": "^17.6.0", "recharts": "^2.15.3", "secure-ls": "^2.0.0", "use-debounce": "^10.0.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/lodash.capitalize": "^4.2.9", "@types/lodash.groupby": "^4.6.9", "@types/lodash.isequal": "^4.5.8", "@types/lodash.omit": "^4.5.9", "@types/lodash.omitby": "^4.6.9", "@types/lodash.pick": "^4.4.9", "@types/lodash.take": "^4.1.9", "@types/lodash.tolower": "^4.1.9", "@types/node": "^22.15.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}}