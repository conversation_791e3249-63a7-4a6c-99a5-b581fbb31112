/* eslint-disable @typescript-eslint/no-explicit-any */
import EventEmitter from "eventemitter3";

export interface EventNames {
  "profile:refetch": any;
}

const eventEmitter = new EventEmitter<EventNames>();

type EventNameType = keyof EventNames;
export type FnArgType<T> = EventEmitter.ArgumentMap<EventNames>[Extract<T, EventNameType>];
export type FnType<T> = (...args: FnArgType<T>) => void;

export const AppEvent = {
  on: <T extends EventNameType>(event: T, fn: FnType<T>, context?: any) => eventEmitter.on(event, fn, context),
  once: <T extends EventNameType>(event: T, fn: FnType<T>, context?: any) => eventEmitter.once(event, fn, context),
  off: <T extends EventNameType>(event: T, fn: FnType<T>, context?: any, once?: boolean) => eventEmitter.off(event, fn, context, once),
  emit: <T extends EventNameType>(event: T, ...args: FnArgType<T>) => eventEmitter.emit(event, ...args),
};

Object.freeze(AppEvent);

export default AppEvent;
