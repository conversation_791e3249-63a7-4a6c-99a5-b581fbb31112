import { QueryClient, UseQueryOptions } from "@tanstack/react-query";

export function ensureQueryData(queryClient: QueryClient) {
  // Return a function that takes a query options object and returns the data from the cache or fetches it if not present
  return async <D = unknown>(options: UseQueryOptions<D, Error>) => {
    // If the query is not in the cache, fetch it first before returning the data
    if (!queryClient.getQueryState(options.queryKey)) {
      return await queryClient.ensureQueryData(options);
    }
    // Return the data from the cache
    return queryClient.getQueryData<D>(options.queryKey);
  };
}
