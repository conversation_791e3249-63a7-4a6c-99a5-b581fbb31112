import { ThemingConfig } from "@chakra-ui/react";

type ColorConfig = NonNullable<ThemingConfig["tokens"]>["colors"];

// TODO: get primary, secondary and accent colors hues and map them.
export default {
  // bkg: { value: "#F3F5F9" },
  black: { value: "#001933" },
  white: { value: "#fff" },
  input: { value: "#F6F8F9" },

  stroke: {
    divider: { value: "#DADCDD" },
    checkbox: { value: "#808D97" },
    error: {
      DEFAULT: { value: "#DD2418" },
      50: { value: "#FFF5F5" },
    },
  },

  grey: {
    DEFAULT: { value: "#667085" },
    25: { value: "#FCFCFD" },
    50: { value: "#F9FAFB" },
    100: { value: "#F2F4F7" },
    /// Modified
    200: { value: "#354959" },
    300: { value: "#808D97" },
    400: { value: "#F2F3F5" },
    /// End modified
    500: { value: "#667085" },
    600: { value: "#475467" },
    700: { value: "#344054" },
    800: { value: "#1D2939" },
    900: { value: "#101828" },
  },

  // stroke: {
  //   default: "#EAECF0",
  //   200: "#E2E4E9",
  // },

  // warning: {
  //   25: "#FFFCF5",
  //   400: "#FDB022",
  //   600: "#DC6803",
  // },

  // success: {
  //   25: "#F6FEF9",
  //   500: "#12B76A",
  // },

  // error: {
  //   25: "#FFFBFA",
  //   50: "#f1cccc",
  //   500: "#F04438",
  // },

  primary: {
    DEFAULT: { value: "#007BFF" },
    25: { value: "#F2F8FF" },
    50: { value: "#F2F8FF" },
    100: { value: "#bef3e1" },
    ////////////////
    200: { value: "#007BFF" },
    ///
    300: { value: "#71e2c2" },
    400: { value: "#4edab5" },
    500: { value: "#00B75B" },
    600: { value: "#2b9681" },
    700: { value: "#1d6b5e" },
    800: { value: "#1A3838" },
    900: { value: "#001711" },
  },

  secondary: {
    DEFAULT: { value: "#F2F8FF" },
    50: { value: "#eef9e8" },
    55: { value: "#E2F2DA" },
    100: { value: "#d3eac7" },
    200: { value: "#b6dda4" },
    300: { value: "#9ace80" },
    400: { value: "#7ec05c" },
    500: { value: "#64a743" },
    600: { value: "#4e8233" },
    700: { value: "#375d24" },
    800: { value: "#203815" },
    900: { value: "#081402" },
  },

  accent: {
    default: { value: "#DE7D1E" },
    50: { value: "#ffe9e1" },
    100: { value: "#ffc3b1" },
    200: { value: "#fe9d80" },
    300: { value: "#fd774f" },
    400: { value: "#fc501e" },
    500: { value: "#e33705" },
    600: { value: "#b02a02" },
    700: { value: "#7f1d01" },
    800: { value: "#4d1000" },
    900: { value: "#1f0300" },
  },

  // misc: {
  //   cream: "#FAF3D4",
  // },
} as ColorConfig;
