import { defineSemanticTokens } from "@chakra-ui/react";

// type ColorConfig = NonNullable<ThemingConfig["semanticTokens"]>["colors"];

// TODO: Add Semantic color tokens with their dark-theme value as well.
const colors = defineSemanticTokens.colors({
  input: { value: "#F6F8F9" },
  bkg: { value: "#FFFFFF" },
  bkg1: { value: "#F3F5F9" },
  bkg3: { value: "#F2F3F5" },

  menu: {
    bkg: {
      value: "#001933",
    },
    stroke: { value: "#0B3D72" },
  },

  bkg2: {
    value: "{colors.primary.50}",
  },

  focusRing: {
    value: {
      base: "{colors.primary}",
      _dark: "{colors.primary}",
    },
  },

  text: {
    DEFAULT: { value: "#00050A" },
    1: { value: "#00050A" },
    2: { value: "#354959" },
    3: { value: "#808D97" },
  },

  stat: {
    DEFAULT: { value: "#F2F3F5" },
    1: { value: "#F2F3F5" },
    2: { value: "#F6FBF5" },
    3: { value: "#FFF4F4" },
  },

  divider: { value: "grey.300" },

  actions: {
    canceled: { value: "#DD2418" },
    completed: { value: "#0AA571" },
    pending: { value: "#DF9900" },
    upcoming: { value: "#0073C4" },
    stale: { value: "#FF6600" },
    default: { value: "{colors.text.3}" },
    lightGreen: { value: "#F6FBF5" },
    lightRed: { value: "#FFF4F4" },
  },
});

export default colors;
