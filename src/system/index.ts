import {
  createSystem,
  defaultConfig,
  defineConfig,
  ThemingConfig,
  // extendBaseTheme,
  // theme as chakraTheme,
} from "@chakra-ui/react";

// Override default foundations
import colors from "./foundations/colors";
import fonts from "./foundations/fonts";
import semantic_colors from "./foundations/semantic-colors";

// Override default recipes
import buttonRecipe from "./recipes/button.recipe";
import { inputRecipe } from "./recipes/input.recipe";

// Override default styles
// import styles from "./styles";
import breakpoints from "./breakpoints";

// Export Recipes
export * from "./recipes/nav.recipe";

const theme: ThemingConfig = {
  breakpoints,
  recipes: {
    button: buttonRecipe,
    input: inputRecipe,
    textarea: inputRecipe,
  },
  tokens: {
    colors,
    fonts,
    shadows: {
      outline: { value: `0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #EBF1FF` },
      darkOutline: { value: `outline` },
      errOutline: { value: `0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #FFECEB` },
      darkErrOutline: { value: `errOutline` },
      error: { value: "errorAlpha" },
      xsm: { value: "0px 1px 2px 0px #5258660F" },
    },
  },

  semanticTokens: {
    colors: semantic_colors,
    // shadows: {
    //   focusRing: {
    //     value: {
    //       base: "{colors.primary.DEFAULT}",
    //       _dark: "{colors.primary.DEFAULT}",
    //     },
    //   },
    // },
  },
};

const varRoot = ":where(:root, :host)";

const config = defineConfig({
  cssVarsRoot: varRoot,
  cssVarsPrefix: "msmt",
  theme,

  // conditions: {
  //   light: `${varRoot} &, .light &`,
  // },

  preflight: { scope: varRoot },

  globalCss: {
    [varRoot]: defaultConfig.globalCss?.html ?? {},
    "html, body": {
      bg: "bkg",
      color: "initial",
      transition: "all .5s ease-in",
    },
    "*:is(:focus, [data-focus])": {
      focusRingColor: "primary",
      focusRingOffset: "2px",
    },
  },
});

const system = createSystem(defaultConfig, config);

export default system;
