import { defineRecipe } from "@chakra-ui/react";

export const inputRecipe = defineRecipe({
  base: {
    minH: "48px",
    rounded: "4px",
    fontWeight: "500",
  },
  variants: {
    variant: {
      subtle: {
        bg: "input",
        focusRingWidth: "1px",
        focusRingColor: "primary",

        _focus: {
          bg: "primary.50",
        },
      },
    },
  },
  defaultVariants: {
    variant: "subtle",
  },
});
