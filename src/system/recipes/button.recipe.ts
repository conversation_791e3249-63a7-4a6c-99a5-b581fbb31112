import { defineRecipe } from "@chakra-ui/react";

const buttonRecipe = defineRecipe({
  className: "msmt-button",
  base: {
    rounded: "full",
    "--before-bg": "black",
  },
  defaultVariants: {
    size: "md",
    variant: "solid",
  },
  variants: {
    variant: {
      solid: {
        bg: "primary",
        color: "white",
        border: "1px solid",
        borderColor: "primary",
        boxSizing: "border-box",
        overflow: "hidden",
        transition: "all 300ms ease-in-out",
      },
      outline: {
        bg: "transparent",
        color: "black",
        border: "1px solid",
        borderColor: "black",
        overflow: "hidden",
        transition: "all 300ms ease-in-out",
        _hover: {
          color: "white",
        },
      },
      plain: {
        bg: "transparent",
        color: "black",
        // border: "1px solid",
        // borderColor: "transparent",
        overflow: "hidden",
        transition: "all 300ms ease-in-out",
        _hover: {
          color: "white",
        },
      },
      subtle: {
        bg: "secondary",
        color: "black",
        border: "1px solid {colors.seconday}",
        // borderColor: "secondary",
        boxSizing: "border-box",
        overflow: "hidden",
        transition: "all 300ms ease-in-out",
        _hover: {
          color: "{colors.white} !important",
        },
      },
      icon: {
        rounded: "sm",
        maxW: "fit-content",
      },
    },

    size: {
      lg: {
        px: "18px",
        py: "12px",
        fontWeight: 600,
        fontSize: "18px",
        gap: "8px",
        minH: "48px",
        maxH: "48px",
      },
      md: {
        px: "16px",
        py: "12px",
        fontWeight: 600,
        fontSize: "14px",
        gap: "4px",
        minH: "38px",
        maxH: "38px",
      },
      sm: {
        px: "12px",
        py: "8px",
        fontWeight: 600,
        fontSize: "14px",
        gap: "4px",
        minH: "30px",
        maxH: "30px",
      },
      ism: {
        px: "12px",
        py: "8px",
        w: "14px",
      },
    },
  },

  compoundVariants: [
    {
      size: ["lg", "md", "sm"],
      variant: ["solid", "outline", "subtle", "plain"],
      css: {
        focusRingColor: "primary",
        focusRingOffset: "2px",
        focusRingWidth: "2px",

        _hover: {
          bg: "white",
          borderColor: "var(--before-bg, black)",
          transition: "all 300ms ease-in-out",

          "& > *": {
            zIndex: 2,
          },

          "&::before": {
            opacity: 1,
            transform: "scaleX(1)",
            transition: "all 300ms ease-in-out",
            willChange: "auto",
          },
        },

        _before: {
          content: "''",
          position: "absolute",
          left: 0,
          w: "100%",
          h: "100%",
          bg: "var(--before-bg, black)",
          // rounded: "full",
          transformOrigin: "left",
          transform: "scaleX(0)",
          opacity: 0,
          transition: "all 300ms ease-in-out",
        },
      },
    },
  ],
});

export default buttonRecipe;
