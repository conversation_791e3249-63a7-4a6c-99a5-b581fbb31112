import { defineRecipe } from "@chakra-ui/react";

export const navItemRecipe = defineRecipe({
  base: {
    display: "flex",
    gap: "4px",
    alignItems: "center",
    textAlign: "center",
    minH: "40px",

    "&.active": {
      bg: "primary",
      color: "white",
      transition: "all 100ms ease-in-out",

      "&  > svg": {
        color: "white !important",
        transition: "all 100ms ease-in-out",
      },
    },
  },

  variants: {
    variant: {
      solid: {
        px: "16px",
        py: "8px",
        bg: "white",
        color: "text.2",
        rounded: "full",
        fontSize: "14px",
        fontWeight: 500,
        border: "1px solid",
        borderColor: "transparent",
        transition: "all 100ms ease-in-out",

        _hover: {
          color: "primary",
          bg: "primary.50",
          borderColor: "primary",
          textDecor: "none",
          transition: "all 100ms ease-in-out",

          "& > svg": {
            color: "primary !important",
            transition: "all 100ms ease-in-out",
          },
        },
        focusRingColor: "primary",
        focusRingOffset: "2px",
        focusRingWidth: "2px",
      },
      plain: {
        fontSize: "16px",
        fontWeight: 400,
        color: "text.2",
        px: "16px",
        py: "4px",
        transition: "all 100ms ease-in-out",
      },
    },
  },
});
