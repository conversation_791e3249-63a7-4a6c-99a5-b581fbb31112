export const isProd = import.meta.env.PROD;
export const isDevMode = import.meta.env.DEV;

const configs = {
  BASE_URL: import.meta.env.VITE_BASE_SERVICE_URL,
  BOOKING_SERVICE_URL: import.meta.env.VITE_BOOKING_SERVICE_URL,
  ENABLE_LOGS: (import.meta.env.REACT_APP_ENABLE_LOGS ?? "false") as string,
  LIST_REQUEST_TOKEN: import.meta.env.VITE_LIST_REQUEST_TOKEN,

  APPT_SESSION_URL: import.meta.env.VITE_APPT_SESSION_URL,

  AUTH_KEY: "provider-auth",
  OTP_RESEND_TIMEOUT_SECS: parseInt(import.meta.env.VITE_OTP_RESEND_TIMEOUT_SECS ?? "60"),
  paths: {},
};

export default configs;

// const log = (window as any).log || console.log;
