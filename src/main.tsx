import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import { Provider, Toaster } from "@/components";
import { AuthContextProvider } from "@/contexts";
import { QueryCache, QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { isProd } from "@/config";
import when from "@/utils/when";
import { AppError, errorStore } from "@/stores/error.store";
import ErrorContextProvider from "@/contexts/error.context";

export const query_client = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 10, // 10 minutes
      // refetchOnWindowFocus: false,
      // refetchOnReconnect: false,
      // refetchOnMount: false,
      throwOnError: false,
      retryOnMount: false,
      retry: false,
    },
  },
  queryCache: new QueryCache({
    onError: (error, query) => {
      const err = error as unknown as AppError;
      console.log("Global Error Occurred", { error, query });
      const actions = errorStore.getState().actions!;
      actions!.setError({
        status: err?.status || err?.code,
        message: err.msg || err.error,
        action: {
          type: (query.queryKey as string[])[0],
          payload: undefined,
        },
        showUser: true,
      });
    },

    // onSuccess(data, query) {
    //   console.log("Global Query Success", { data, query });
    //   if ((query.queryKey ?? []).includes(USER_PROFILE_DATA_QUERY_KEY)) {
    //     const user = (data as UserRo)?.data;
    //     console.log("User Profile Data Query Success", data);
    //     useAuthStore.getState().set_account_data(user);
    //   }
    // },
  }),
});

// override the console.log function
console.log = when(!isProd, console.log, () => {});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <QueryClientProvider client={query_client}>
      <AuthContextProvider>
        <Provider>
          <ErrorContextProvider>
            <App />

            <Toaster />
          </ErrorContextProvider>
        </Provider>
      </AuthContextProvider>
    </QueryClientProvider>
  </StrictMode>
);
