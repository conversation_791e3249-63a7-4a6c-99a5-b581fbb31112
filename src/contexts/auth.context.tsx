import { UserDataRo } from "@/interfaces";
import { startViewTransition } from "@/libs";
import { useAuthStore, useErrorStore } from "@/stores";
import { useQueryClient } from "@tanstack/react-query";
import { createContext, useCallback } from "react";
import { replace } from "react-router";

interface IAuthContext {
  is_signedin: boolean;

  logout(): void;
  user?: UserDataRo;
}

// eslint-disable-next-line react-refresh/only-export-components
export const AuthContext = createContext<IAuthContext | null>(null);

export const AuthContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const qc = useQueryClient();

  const auth = useAuthStore((store) => store.auth);
  const user = useAuthStore((store) => store.user);
  const is_signedin = !!auth?.is_signed_in && !!auth?.token;
  const logout = useAuthStore((store) => store.logout);
  const { actions } = useErrorStore((state) => state);

  const handleLogout = useCallback(() => {
    logout();
    actions?.clearError("both");
    qc.getQueryCache().clear();
    // user_is_in_app.current = false;
    startViewTransition(() => replace("/signin"));
  }, [qc, logout, actions]);

  //   useAuthStore.subscribe(({ auth }) => {
  //     console.log("Auth state changed", auth);
  //     const is_signedin = !!auth?.is_signed_in && !!auth?.token;

  //     if (is_signedin && !user_is_in_app.current) {
  //       user_is_in_app.current = true;
  //       //   startViewTransition(() => replace("/"));
  //       //   (window.location as any) = "/";
  //     }
  //   });

  return (
    <AuthContext.Provider value={{ is_signedin, logout: handleLogout, user }}>
      {children}
    </AuthContext.Provider>
  );
};
