import { createContext, PropsWith<PERSON>hildren, useCallback, useEffect, useMemo, useRef } from "react";
import toLower from "lodash.tolower";
import { toaster, upsertToast } from "@/components";
import { useAuth } from "@/hooks";
import { useErrorStore } from "@/stores";

type IErrorContext = object;

const ErrorContext = createContext<Partial<IErrorContext>>({});

const ErrorContextProvider = (props: PropsWithChildren) => {
  const toast_id = useRef<string | undefined>(undefined);
  const { logout, is_signedin } = useAuth();
  const { next, previous, actions } = useErrorStore((state) => state);
  //   const transform = useErrorTransforms(next);

  const is_same_msg_as_previous = useMemo(() => {
    return next?.message?.toLowerCase() === previous?.message?.toLowerCase();
  }, [next, previous]);

  const is_unauthorized = useMemo(
    () => (next?.message && toLower(next.message).includes("unauthorized")) || (next?.status && next?.status === 401),
    [next]
  );

  const autoLogOut = useCallback(() => {
    if (is_unauthorized) logout();
  }, [is_unauthorized, logout]);

  const has_message = useMemo(() => !!next?.message && next?.message !== "", [next?.message]);

  //   console.log("Transformed Error state", { transform, next });

  useEffect(() => {
    if (next.id !== null && has_message && next.showUser) {
      autoLogOut();

      console.log("Next Error", next);

      if (is_same_msg_as_previous && !!toast_id.current) {
        toaster.dismiss(toast_id.current);
      }

      if (!is_signedin && !!is_unauthorized) return;

      toast_id.current = upsertToast({
        // id: next.id
        id: toast_id.current,
        type: "error",
        title: next?.title ? next.title : next?.message,
        description: next?.title ? next?.message : undefined,
        meta: { closable: true },
        onStatusChange(e) {
          if (e.status === "unmounted") {
            actions?.clearError("both");
          }
        },
      });
    }
  }, [next, autoLogOut, is_same_msg_as_previous, is_signedin, is_unauthorized, actions, has_message]);

  return <ErrorContext.Provider value={{}}>{props.children}</ErrorContext.Provider>;
};

// function useErrorTransforms(error: ErrorType) {
//   type ErrorTransformType = { link?: { content: string; to: string }; message: string };
//   const msg = toLower(error?.message ?? "unexpected error occurred");
//   const map: Record<string, ErrorTransformType> = {
//     "account not found": {
//       message: capitalize(msg),
//       link: { content: "Create an account?", to: "/signup" },
//     },
//     default: { message: capitalize(msg) },
//   };

//   const transform_key = Object.keys(map).find((key) => msg.includes(key));
//   return map[transform_key ?? "default"];
// }

export default ErrorContextProvider;
