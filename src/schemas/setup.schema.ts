import { type } from "arktype";
import { passwordSchema } from "./auth.schema";

export const changePasswordSchema = type({
  new_password: passwordSchema("new_password"),
  old_password: "string",
  //   confirm_password: passwordSchema("confirm_password"),
});

// .narrow((val, ctx) => {
//   if (val.new_password !== val.confirm_password) {
//     ctx.error({
//       path: ["confirm_password"],
//       message: "Passwords do not match",
//     });
//     return false;
//   }
//   return true;
// });

export const addServiceRateSchema = type({
  service_offer_id: type("string > 0").configure({ message: "Please select a sevice" }),
  amount: type("string.integer").configure({ message: "Please enter a valid amount" }),
});

export const updateServiceRateSchema = type({
  sr: type({
    service_offer_id: "string",
    service_offer_name: "string",
    amount: type("string.integer").configure({ message: "Please enter a valid amount" }),
  }).array(),
});

export const updateExclusiveServiceSchema = type({
  /**	Array of string of exclusive service id. Submit empty to delete all added services */
  // data: type("string[] > 0").configure({ message: "Please select at least one exclusive service" }),
  data: "string[]",
  /**	A single amount to charge for all the selected services */
  amount: type("string.integer").configure({ message: "Please enter a valid amount" }),
});

export const addAvailabilitySchema = type({
  av: type({
    start_time: type("string.integer").configure({ message: "Please select a start time" }),
    end_time: type("string.integer").configure({ message: "Please select an end time" }),
    av_day: "0 <= string.integer <= 6",
    "schedule_id?": "string",
  }).array(),
  time_zone: "string",
});

export const updateBankInfoSchema = type({
  account_number: type("string > 0").configure({ message: "Please enter a valid account number" }),
  account_name: "string",
  bank_code: type("string > 0").configure({ message: "Please select a bank" }),
  confirmed: type("boolean").configure({ message: "Please confirm your account details" }),
}).narrow((val, ctx) => {
  if (val.account_number.length > 9 && !!val.bank_code && val.account_name.length < 1) {
    ctx.error({
      path: ["account_number"],
      message: "Please enter a valid account number to continue",
    });
    return false;
  }

  if (!val.confirmed) {
    ctx.error({
      path: ["confirmed"],
      message: "Please confirm your account details",
    });
    return false;
  }

  return true;
});

export type AddServiceRateDto = typeof addServiceRateSchema.infer;
export type UpdateServiceRateDto = typeof updateServiceRateSchema.infer;
export type AddAvailabilityDto = typeof addAvailabilitySchema.infer;
export type UpdateBankInfoDto = typeof updateBankInfoSchema.infer;
export type UpdateExclusiveServiceDto = typeof updateExclusiveServiceSchema.infer;
