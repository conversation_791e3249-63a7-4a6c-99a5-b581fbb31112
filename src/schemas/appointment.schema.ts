import { type } from "arktype";

export const addDiagnosisSchema = type({
  icds: type({
    appointment_id: "string",
    code9: "string",
    code10: "string",
    description: "string",
    "data_id?": "string",
  }).array(),
});

export const genericAssessmentFormSchema = type({
  data: type({
    type: "string",
    label: "string",
    field_name: "string",

    /** Some values may be number formatted, so we need to store the raw value */
    "value_text?": "string | string[]",
    value: "string | string[]",
  }).array(),
});

export const addMedicalPrescriptionSchema = type({
  "temp_id?": "string",
  appointment_id: "string",
  med_name: "string",
  med_dosage: "string",
  med_freq: "string",
  med_type: "string",
  start_date: "string",
  end_date: "string",
  "data_id?": "string",
});

export const substanceAbuseFormSchema = type({
  appointment_id: "string",
  data: type({
    "temp_id?": "string",
    substance_abuse: "string",
    last_use_date: "string",
    last_frequency_use: "string",
    money_spent: "string",
    abuse_treatment: "string",

    // if substance_abuse === "alcohol"
    "any_alcohol?": "string",
    "smoke_hashish?": "string",
    "anything_high?": "string",
    "ridden_car?": "string",
    "alcohol_relax?": "string",
    "alcohol_alone?": "string",
    "forget_drugs?": "string",
    "friends_family?": "string",
    "gotten_trouble?": "string",

    // if substance_abuse !== "alcohol"
    "large_amount?": "string",
    "persistent_desire?": "string",
    "great_deal?": "string",
    "craving_desire?": "string",
    "recurrent_resulting?": "string",
    "continues_substance?": "string",
    "occupational_activities?": "string",
    "physically_hazardous?": "string",
    "psychological_problem?": "string",
    "tolerance_defined?": "string",
    "markedly_increased?": "string",
    "markedly_diminished?": "string",
    "characteristic_withdrawal?": "string",
    "characteristic_withdrawal_answer?": "string",
    "exiting_withdrawal?": "string",
    "closely_related_substance?": "string",
  }).array(),
});

export const referUserSchema = type({
  service_offer_id: type("string > 0").configure({ message: "Please select a service" }),
  provider_id: type("string > 0").configure({ message: "Please select a provider" }),
  comment: type("string > 0").configure({ message: "Please enter a comment" }),
});

export type AddDiagnosisDto = typeof addDiagnosisSchema.infer;
export type GenericAssessmentFormDto = typeof genericAssessmentFormSchema.infer;
export type AddMedicalPrescriptionDto = typeof addMedicalPrescriptionSchema.infer;
export type SubstanceAbuseFormDto = typeof substanceAbuseFormSchema.infer;
export type ReferUserDto = typeof referUserSchema.infer;
