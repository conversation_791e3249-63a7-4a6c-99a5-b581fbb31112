import { type } from "arktype";

export const completeProfile_personalInfoSchema = type({
  phone_number: type("0 < string <= 12").configure({ message: "Please enter a valid phone number" }),
  phone_prefix: "string",
  residence_address: type("string > 0").configure({ message: "Please enter your address" }),
  residence_country: type("string > 0").configure({ message: "Please select your country" }),
  residence_state: type("string > 0").configure({ message: "Please select your state" }),
  "martial_status?": "string",
  service_cat_id: type("string > 0").configure({ message: "Please select your specialty area" }),
  service_start_year: type("string > 0").configure({ message: "Please enter your service start year" }),
  preferred_lan: type("string[] > 0").configure({ message: "Please select at least one preferred language" }),
  "religion?": "string",

  avatar_file: "File | null",

  // contact_person: {
  //   name: type("string").configure({ message: "Please enter the name of your contact person" }),
  //   email: type("string.email").configure({
  //     message: "Please enter a valid email address",
  //   }),
  //   phone_number: type("0 < string <= 12").configure({ message: "Please enter a valid phone number" }),
  //   phone_prefix: "string",
  //   job_title: type("string > 0").configure({ message: "Please enter your job title" }),
  // },
}).narrow((val, ctx) => {
  if (!val.avatar_file) {
    ctx.error({
      path: ["avatar_file"],
      message: "Please upload a profile picture",
    });
    return false;
  }

  return true;
});

export const updatePersonalInfoSchema = type({
  name: "string",
  religion: "string",
  phone_number: type("0 < string <= 12").configure({ message: "Please enter a valid phone number" }),
  phone_prefix: "string",
  residence_country: "string",
  gender: "string",
  residence_address: "string",
  preferred_lan: "string[]",
});

export const addEducationSchema = type({
  level: type("string > 0").configure({ message: "Please select your education level" }),
  name: type("string > 0").configure({ message: "Please enter your education name" }),
  location: type("string > 0").configure({ message: "Please enter your education location" }),
  degree: type("string > 0").configure({ message: "Please enter your education degree" }),
  year: type("string > 0").configure({ message: "Please enter your education year" }),
});

export const addPublicationSchema = type({
  name: type("string > 0").configure({ message: "Please enter publication name" }),
  year: type("string > 0").configure({ message: "Please enter publication year" }),
});

export const addCertificationSchema = type({
  country: type("string > 0").configure({ message: "Please select certification country" }),
  cert_name: type("string > 0").configure({ message: "Please enter certification name" }),
  cert_year: type("string > 0").configure({ message: "Please enter certification year" }),
  cert_number: type("string > 0").configure({ message: "Please enter certification number" }),
  "state?": "string",
}).narrow((val, ctx) => {
  if (val.country === "united states" && !val.state) {
    ctx.error({
      path: ["state"],
      message: "Please enter certification state",
    });
    return false;
  }

  return true;
});

export const addAssociationSchema = type({
  name: type("string > 0").configure({ message: "Please enter association name" }),
  country: type("string > 0").configure({ message: "Please select association country" }),
  state: type("string > 0").configure({ message: "Please select association state" }),
  member_id: type("string > 0").configure({ message: "Please enter association member id" }),
});

export const completeProfile_serviceRateDtoSchema = type({
  service_offer_id: type("string > 0").configure({ message: "Please select a sevice" }),
  amount: type("string > 0").configure({ message: "Please enter your service rate" }),
  "service_offer_name?": "string",
});

export const completeProfile_availabilityDtoSchema = type({
  start_time: type("string.integer").configure({ message: "Please select a start time" }),
  end_time: type("string.integer").configure({ message: "Please select an end time" }),
  av_day: "0 <= string.integer <= 6",
  // time_zone: "string",
  "schedule_id?": "string",
});

export const completeProfile_associationSchema = type({
  assoc: addAssociationSchema.array(),
});

export const completeProfile_certificationSchema = type({
  cert: addCertificationSchema.array(),
});

export const completeProfile_educationSchema = type({
  edu: addEducationSchema.array(),
});

export const completeProfile_serviceRateSchema = type({
  sr: completeProfile_serviceRateDtoSchema.array(),
});

export const completeProfile_availabilitySchema = type({
  av: completeProfile_availabilityDtoSchema.array(),

  /**
   * Value of Date.getTimezoneOffset()
   */
  time_zone: "string",
}).narrow((val, ctx) => {
  if (!val.av || val.av.length < 1) {
    ctx.error({
      path: ["av"],
      message: "Please add at least one availability",
    });
    return false;
  }

  return true;
});

// export const completeProfileSchema = type({
//   personal: "object",
//   education: completeProfile_educationSchema.array(),
//   service_rate: "object",
//   availability: "object",
// });

export const addTrainingSchema = type({
  "name?": "string",
  training_id: type("string > 0").configure({ message: "Please select a professional training" }),
});

export const uploadDocumentSchema = type({
  doc_name: type("string > 0").configure({ message: "Please select a document type" }),
  file: "File | null",
}).narrow((val, ctx) => {
  if (!val.file) {
    ctx.error({
      path: ["file"],
      message: "Please upload a document",
    });
    return false;
  }

  return true;
});

export type AddTrainingDto = typeof addTrainingSchema.infer;
export type UploadDocumentDto = typeof uploadDocumentSchema.infer;
export type AddEducationDto = typeof addEducationSchema.infer;
export type AddPublicationDto = typeof addPublicationSchema.infer;
export type AddCertificationDto = typeof addCertificationSchema.infer;
export type AddAssociationDto = typeof addAssociationSchema.infer;
export type UpdatePersonalInfoDto = typeof updatePersonalInfoSchema.infer;

export type CompleteProfile_EducationSchema = typeof completeProfile_educationSchema.infer;
export type CompleteProfile_ServiceRateDto = typeof completeProfile_serviceRateSchema.infer;
export type CompleteProfile_AvailabilityDto = typeof completeProfile_availabilitySchema.infer;
export type CompleteProfile_PersonalInfoDto = typeof completeProfile_personalInfoSchema.infer;
export type CompleteProfile_AssociationDto = typeof completeProfile_associationSchema.infer;
export type CompleteProfile_CertificationDto = typeof completeProfile_certificationSchema.infer;
