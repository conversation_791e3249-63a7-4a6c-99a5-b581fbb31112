import { type } from "arktype";

// Form validation schemas

export const passwordSchema = (fieldname: string) =>
  type("string").narrow((val, ctx) => {
    console.log("Password schema ctx", ctx);
    if (val.length < 8) {
      ctx.error({
        path: [fieldname],
        message: "Password must be at least 8 characters",
      });
      return false;
    }

    if (!/[A-Z]/.test(val)) {
      ctx.error({
        path: [fieldname],
        message: "Password must contain at least one uppercase letter",
      });
      return false;
    }

    if (!/[a-z]/.test(val)) {
      ctx.error({
        path: [fieldname],
        message: "Password must contain at least one lowercase letter",
      });
      return false;
    }

    if (!/[0-9]/.test(val)) {
      ctx.error({
        path: [fieldname],
        message: "Password must contain at least one number",
      });
      return false;
    }

    // if (!/[!@#$%^&*(),.?":{}|<>]/.test(val)) {
    //   ctx.error({
    //     path: [fieldname],
    //     message: "Password must contain at least one special character",
    //   });
    //   return false;
    // }

    return true;
  });

export const loginSchema = type({
  email: type("string.email").configure({
    message: "Please enter a valid email address",
  }),
  password: type("string > 0").configure({
    message: "Please enter your password",
  }),
});

export const forgotPasswordSchema = type({
  email: type("string.email").configure({
    message: "Please enter a valid email address",
  }),
});

export const verifyEmailSchema = type({
  email: type("string.email").configure({
    message: "Please enter a valid email address",
  }),
  otp: "string[] == 5",
});

export const setPasswordSchema = type({
  email: type("string.email").configure({
    message: "Please enter a valid email address",
  }),
  otp: "string",
  password: passwordSchema("password"),
  confirm_password: passwordSchema("confirm_password"),
}).narrow((val, ctx) => {
  if (val.password !== val.confirm_password) {
    ctx.error({
      path: ["confirm_password"],
      message: "Passwords do not match",
    });
    return false;
  }

  return true;
});

export const resendOtpSchema = type({
  email: type("string.email").configure({
    message: "Please enter a valid email address",
  }),
  request_type: '"otp"',
});

export const registerAccountSchema = type({
  first_name: type("string > 0").configure({
    message: "Please enter your first name",
  }),
  last_name: type("string > 0").configure({
    message: "Please enter your last name",
  }),
  email: type("string.email").configure({
    message: "Please enter a valid email address",
  }),
  gender: type("string > 0").configure({
    message: "Please select your gender",
  }),
  password: passwordSchema("password"),

  service_cat_id: type("string > 0").configure({
    message: "Please select your specialty area",
  }),
  acceptTerms: "boolean",
});

export type LoginDto = typeof loginSchema.infer;
export type VerifyEmailDto = typeof verifyEmailSchema.infer;
export type ResendOtpDto = typeof resendOtpSchema.infer;
export type SetPasswordDto = typeof setPasswordSchema.infer;
export type ForgotPasswordDto = typeof forgotPasswordSchema.infer;
export type RegisterAccountDto = typeof registerAccountSchema.infer;
