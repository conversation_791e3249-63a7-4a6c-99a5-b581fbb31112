/* eslint-disable @typescript-eslint/no-explicit-any */
import { format, parse, parseISO, isValid } from "date-fns";

/**
 * Converts a date string in "YYYY-MM" format to full month name and year
 * @param yearMonth - Date string in "YYYY-MM" format (e.g., "2025-04")
 * @param formatStr - Optional format string (default: "MMMM yyyy")
 * @returns Formatted date string (e.g., "April 2025")
 */
export function yearMonthToFullName(yearMonth: string, formatStr: string = "MMMM yyyy"): string {
  try {
    // Parse the YYYY-MM string to a Date object
    const date = parse(yearMonth, "yyyy-MM", new Date());
    // Format the date to full month name and year
    return format(date, formatStr);
  } catch (error) {
    console.error("Invalid date format. Expected YYYY-MM", error);
    return yearMonth; // Return original string if parsing fails
  }
}

export const plusSomeSeconds = (date: Date | null, seconds: number) => {
  const givenDate = date ? new Date(date) : new Date();
  const workingDate = new Date(givenDate);
  workingDate.setSeconds(workingDate.getSeconds() + seconds);

  return workingDate;
};

export const lessThanSomeHoursAgo = (date: Date | string, hoursAgo: number) => {
  const givenDate = date ? new Date(date) : new Date();
  const workingDate = new Date();
  workingDate.setHours(workingDate.getHours() - hoursAgo);
  const timeDifference = workingDate.getTime() - givenDate.getTime();
  return timeDifference < 0;
};

export const lessThanSomeMinutesAgo = (date: Date | string, duration: number) => {
  const givenDate = date ? new Date(date) : new Date();
  const workingDate = new Date();
  workingDate.setMinutes(workingDate.getMinutes() - duration);
  const timeDifference = workingDate.getTime() - givenDate.getTime();
  return timeDifference < 0;
};

export const diffOfTwoDates = (date1: Date | string, date2: Date | string) => {
  const givenDate = date1 ? new Date(date1) : new Date();
  const workingDate = date2 ? new Date(date2) : new Date();
  const timeDifference = Math.abs(workingDate.getTime() - givenDate.getTime());
  return timeDifference / 1000;
};

/**
 * Converts numeric hour values (0-23) to formatted time strings,
 * adjusting for the user's timezone if specified
 * @param timeObj - Object containing start_time and end_time as numbers (0-23)
 * @param timezone - Optional timezone offset in minutes (from Date.getTimezoneOffset())
 * @returns Object with formatted start time, end time, and combined range string
 */
export function formatTimeRange(
  timeObj: { start_time: number; end_time: number },
  timezone?: string | number
): {
  formattedStartTime: string;
  formattedEndTime: string;
  formattedRange: string;
} {
  const { start_time, end_time } = timeObj;

  // Format start and end times
  const formattedStartTime = formatHourToTimeString(start_time, timezone);
  const formattedEndTime = formatHourToTimeString(end_time, timezone);

  // Create the combined range string
  const formattedRange = `${formattedStartTime} - ${formattedEndTime}`;

  return {
    formattedStartTime,
    formattedEndTime,
    formattedRange,
  };
}

/**
 * Adjusts a numeric hour value (0-23) based on timezone difference
 * @param hour - Hour as a number (0-23)
 * @param timezone - Optional timezone offset in minutes (from Date.getTimezoneOffset())
 * @returns Adjusted hour as a number (0-23)
 */
export function adjustHourForTimezone(hour: number, timezone: string | number = 0): number {
  // If no timezone provided, return the original hour
  if (timezone === undefined) {
    return hour;
  }

  // Convert timezone string to number if needed
  const timezoneOffset = typeof timezone === "string" ? parseInt(timezone, 10) : timezone;

  // Get local timezone offset in hours (getTimezoneOffset returns minutes)
  const localOffset = new Date().getTimezoneOffset() / 60;

  // Calculate the difference between local and specified timezone (in hours)
  const offsetDiff = timezoneOffset / 60 - localOffset;

  // Adjust time based on timezone difference and ensure it's in 0-23 range
  const result = (hour + offsetDiff + 24) % 24;

  console.log("timezone ajustment", { timezone, hour, result, offsetDiff });

  return result;
}

//converting UTC to localtime
export const getLocalTimeFromUTC = (dateString?: string, timeNumber?: number) => {
  const dbDate = dateString
    ? new Date(dateString)
    : timeNumber
    ? new Date("2025-01-01T" + String(timeNumber).padStart(2, "0") + ":00:00.000Z")
    : new Date();

  const timeZone = new Date().getTimezoneOffset();
  const timeOffsetInMS = timeZone * 60000;

  dbDate.setTime(dbDate.getTime() - timeOffsetInMS);

  // const date = new Date();

  const conTime = dbDate.toISOString();
  const timeOut = conTime.substring(11, 16);
  const dateOut = conTime.substring(0, 10);

  const hr = parseInt(timeOut.substring(0, 2)).toString();

  // console.log("local time", { conTime, timeOut, dateOut, hr });
  return {
    time: timeOut,
    date: dateOut,
    hr,
    dateObj: conTime,
  };
};

/**
 * Converts a numeric hour value (0-23) to a formatted time string,
 * adjusting for the user's timezone if specified
 * @param hour - Hour as a number (0-23)
 * @param timezone - Optional timezone offset in minutes (from Date.getTimezoneOffset())
 * @returns Formatted time string (e.g., "10:00 AM")
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function formatHourToTimeString(hour: number, _timezone?: string | number): string {
  // Apply timezone adjustment if provided
  // const adjustedHour = adjustHourForTimezone(hour, timezone);

  const adjustedHour = parseInt(getLocalTimeFromUTC(undefined, hour)?.hr ?? "0");

  // Format the time
  const period = adjustedHour < 12 ? "AM" : "PM";
  const displayHour = adjustedHour === 0 ? 12 : adjustedHour > 12 ? adjustedHour - 12 : adjustedHour;
  const formattedHour = Math.floor(displayHour).toString().padStart(2, "0");

  return `${formattedHour}:00 ${period}`;
}

/**
 * Checks if an appointment can be joined based on its scheduled time
 * An appointment can be joined if:
 * 1. It's scheduled for today
 * 2. The current time is within the appointment window (start time to start time + 1 hour)
 *
 * @param appointmentDateTime - ISO string of the appointment date and time
 * @returns Boolean indicating if the appointment can be joined
 */
export function canJoinAppointment(appointmentDateTime: string): boolean {
  // Return false if the appointment date is invalid
  if (!appointmentDateTime || !isValid(parseISO(appointmentDateTime))) {
    return false;
  }

  const now = new Date();
  const appointmentDate = parseISO(appointmentDateTime);

  // Check if the appointment is scheduled for today
  const isSameDay =
    now.getFullYear() === appointmentDate.getFullYear() &&
    now.getMonth() === appointmentDate.getMonth() &&
    now.getDate() === appointmentDate.getDate();

  if (!isSameDay) {
    return false;
  }

  // Get the appointment start hour and calculate end hour (1 hour later)
  const appointmentHour = appointmentDate.getHours();
  const appointmentEndHour = (appointmentHour + 1) % 24;

  // Get current hour
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();

  // Check if current time is within the appointment window
  if (appointmentHour === appointmentEndHour) {
    // Edge case: appointment spans midnight
    return true;
  } else if (appointmentHour < appointmentEndHour) {
    // Normal case: appointment within same day
    return (
      (currentHour > appointmentHour || (currentHour === appointmentHour && currentMinute >= 0)) &&
      (currentHour < appointmentEndHour || (currentHour === appointmentEndHour && currentMinute === 0))
    );
  } else {
    // Edge case: appointment spans midnight
    return currentHour >= appointmentHour || currentHour < appointmentEndHour;
  }
}

/**
 * Generates a list of years from the current year to a specified number of years back
 * @param yearsBack - Number of years to go back from current year (default: 50)
 * @param startYear - Optional specific year to start from instead of current year
 * @returns Array of year strings in YYYY format, sorted in descending order (newest first)
 */
export function getYearsList(yearsBack: number = 50, startYear?: number): string[] {
  // Get the current year or use the provided start year
  const currentYear = startYear || new Date().getFullYear();

  // Calculate the earliest year
  const earliestYear = currentYear - yearsBack;

  // Create an array to hold the years
  const years: string[] = [];

  // Generate the list of years
  for (let year = currentYear; year >= earliestYear; year--) {
    years.push(year.toString());
  }

  return years;
}

/**
 * Generates a list of years as select options from the current year to a specified number of years back
 * @param yearsBack - Number of years to go back from current year (default: 50)
 * @param startYear - Optional specific year to start from instead of current year
 * @returns Array of objects with label and value properties for use in select components
 */
export function getYearsOptions(yearsBack: number = 50, startYear?: number): Array<{ label: string; value: string }> {
  const years = getYearsList(yearsBack, startYear);

  return years.map((year) => ({
    label: year,
    value: year,
  }));
}
