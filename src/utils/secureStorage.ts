import SecureLS from "secure-ls";
import { StateStorage } from "zustand/middleware";

const secureStorage = new SecureLS({ encodingType: "aes" });

export const zustandSecureStoragePrototype: StateStorage = {
  getItem(name) {
    return secureStorage.get(name);
  },
  removeItem(name) {
    return secureStorage.remove(name);
  },
  setItem(name, value) {
    return secureStorage.set(name, value);
  },
};

export default secureStorage;
