/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Compares two arrays of objects and returns the added, removed, and modified items
 * @param originalArray The original array of objects
 * @param newArray The new array of objects
 * @param idKey The key to use as unique identifier for objects (default: 'id')
 * @returns Object containing added, removed, and modified items
 */
export function getArrayChanges<T extends Record<string, any>>(
  originalArray: T[],
  newArray: T[],
  idKey: keyof T = "id"
): {
  added: T[];
  removed: T[];
  modified: Array<{ original: T; new: T; changes: Partial<T> }>;
} {
  const result = {
    added: [] as T[],
    removed: [] as T[],
    modified: [] as Array<{ original: T; new: T; changes: Partial<T> }>,
  };

  // Create maps for faster lookups
  const originalMap = new Map(originalArray.map((item) => [item[idKey], item]));
  const newMap = new Map(newArray.map((item) => [item[idKey], item]));

  // Find added items (in new but not in original)
  newArray.forEach((newItem) => {
    if (!originalMap.has(newItem[idKey])) {
      result.added.push(newItem);
    }
  });

  // Find removed items (in original but not in new)
  originalArray.forEach((originalItem) => {
    if (!newMap.has(originalItem[idKey])) {
      result.removed.push(originalItem);
    }
  });

  // Find modified items (in both but with differences)
  originalArray.forEach((originalItem) => {
    const id = originalItem[idKey];
    const newItem = newMap.get(id);

    if (newItem) {
      const changes = getChangedFields(originalItem, newItem);
      if (Object.keys(changes).length > 0) {
        result.modified.push({
          original: originalItem,
          new: newItem,
          changes,
        });
      }
    }
  });

  return result;
}

export function getChangedFields<T extends object>(original: T, new_value: T) {
  return Object.entries(new_value)
    .filter(([k, v]) => v !== original[k as keyof T])
    .reduce((acc, [k, v]) => ({ ...acc, [k]: v }), {} as T);
}
