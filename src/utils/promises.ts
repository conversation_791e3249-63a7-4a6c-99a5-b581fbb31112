import { AppError } from "@/stores/error.store";

type Result<T, E> = Success<T> | Failure<E>;

interface Success<T> {
  ok: true;
  data: T;
}

interface Failure<E> {
  ok: false;
  error: E;
}

export const wait = <T>(val: T, timeout: number) =>
  new Promise((res) => {
    setTimeout(() => {
      res(val);
    }, timeout);
  });

export async function tryCatch<T, E = AppError>(
  promise: Promise<T>
): Promise<Result<T, E>> {
  try {
    return { ok: true, data: await promise };
  } catch (e) {
    const error = e as E;
    console.log("App Error", error);
    return { ok: false, error };
  }
}
