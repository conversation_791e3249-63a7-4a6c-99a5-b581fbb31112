/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";

// import configs from "config";
import ls from "@/utils/secureStorage";
// import { AuthState } from "@/stores";
import configs from "@/config";

// const configs = {
//   AUTH_TOKEN_KEY: "slklds",
//   BASE_URL: "https://stagingapp.zeno.ng/niseelo-user-service",
// };

// axios.defaults.withCredentials = true;

axios.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  function (error: AxiosError<unknown>) {
    const errorData = {
      ...(error?.response?.data as object),
    };
    return Promise.reject(errorData);
  }
);

type AuthState = {
  is_signed_in?: boolean;
  token?: string;
};

const makeRequest = <D, R>(config: AxiosRequestConfig<D>) => {
  const { headers, ...xconfig } = config;
  const getHeaders = () => {
    try {
      const store: Partial<{ state: { auth: AuthState } }> = JSON.parse(ls.get(configs.AUTH_KEY) ?? "{}");
      const auth = store.state?.auth;

      if (auth?.is_signed_in && auth?.token)
        return {
          authorization: `Bearer ${auth?.token}`,
        };

      return {};
    } catch (error) {
      console.error(error);
    }
  };

  const val = {
    baseURL: configs.BASE_URL,
    headers: { ...(getHeaders() as object), ...headers },
    // withCredentials: true,
    ...xconfig,
  };

  return axios.request<R>(val);
};

export const get = <T>(url: string, headers?: AxiosRequestConfig<any>["headers"]) =>
  makeRequest<unknown, T>({ url, method: "GET", headers }).then((r: AxiosResponse<T, unknown>) => r.data);

export const post = <T, D>(url: string, data: D, headers?: AxiosRequestConfig<any>["headers"]) =>
  makeRequest<unknown, T>({ url, data, method: "POST", headers }).then((r: AxiosResponse<T, unknown>) => r.data);

export const put = <T, D>(url: string, data: D, headers?: AxiosRequestConfig<any>["headers"]) =>
  makeRequest<unknown, T>({ url, data, method: "PUT", headers }).then((r: AxiosResponse<T, unknown>) => r.data);

export const destroy = <T, D = unknown>(url: string, data?: D, headers?: AxiosRequestConfig<any>["headers"]) =>
  makeRequest<unknown, T>({ url, data, method: "DELETE", headers }).then((r: AxiosResponse<T, unknown>) => r.data);

export default makeRequest;
