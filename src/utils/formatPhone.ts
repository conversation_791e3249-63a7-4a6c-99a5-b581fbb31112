// Sanitize phone number (remove all non-digit characters)
export const sanitizePhone = (phoneNumberString: string): string => {
  return `${phoneNumberString}`.replace(/\D/g, "");
};

export const formatePhone = (phoneNumberString: string, countryCode?: string) => {
  // Limit to 10-12 digits
  const cleaned = `${phoneNumberString}`.replace(/\D/g, "");
  const limitedNumber = cleaned.slice(0, 12);
  const cc = countryCode ? countryCode.toLowerCase() : "ng";

  // US format (10 digits)
  if (cc === "us" && limitedNumber.length === 10) {
    return `(${limitedNumber.slice(0, 3)}) ${limitedNumber.slice(3, 6)}-${limitedNumber.slice(6, 10)}`;
  }

  // UK format (11 digits)
  else if (cc === "gb" && limitedNumber.length === 11) {
    return `${limitedNumber.slice(0, 4)} ${limitedNumber.slice(4, 7)} ${limitedNumber.slice(7, 11)}`;
  }

  return limitedNumber.replace(/(\d{3})(\d{3})(\d{4})/, "$1 $2 $3").trim();
};

export const formatePhoneBot = (phoneNumberString: string) => {
  const cleaned = `${phoneNumberString}`.replace(/\D/g, "");
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    return `${match[1]}-${match[2]}-${match[3]}`;
  }
  return null;
};
