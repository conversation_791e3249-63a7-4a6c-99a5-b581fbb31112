export const toQueryString = (params: { [key: string]: string | number }) => {
  // Object.keys(params)
  //   .map((key) => !!params[key] && `${key}=${params[key]}`)
  //   .filter(Boolean) // Boolean can filter out falsy values like null, undefined or false
  //   .join("&");

  const santized_params = Object.entries(params)
    .filter(([, value]) => !!value)
    .map(([key, value]) => [key, value.toString()]);

  return new URLSearchParams(santized_params).toString();
};
