export function getStatusColor(status: string) {
  if (typeof status !== "string" || status === undefined) return "actions.default";

  const key = status?.toLowerCase();
  const map: Record<string, string> = {
    default: "actions.default",
    upcoming: "actions.upcoming",
    pending: "actions.pending",
    completed: "actions.completed",
    canceled: "actions.canceled",
    inactive: "actions.canceled",
    ended: "actions.pending",

    // provider statuses
    active: "actions.completed",
    rejected: "actions.canceled",
    suspended: "actions.canceled",
    declined: "actions.canceled",

    // wallet statuses
    failed: "actions.canceled",
    successful: "actions.completed",
    abandoned: "actions.upcoming",

    credit: "actions.completed",
    deduction: "actions.upcoming",

    approved: "actions.completed",
  };

  const color = map[key] || map.default;
  return color;
}
