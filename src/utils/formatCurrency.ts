/**
 * Formats a number as currency with comma separators based on locale
 * @param value - The number to format
 * @param locale - The locale to use for formatting (default: 'en-NG' for Nigerian format)
 * @param currency - The currency code (default: 'NGN' for Nigerian Naira)
 * @param options - Additional Intl.NumberFormatOptions to customize formatting
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number,
  locale: string = 'en-NG',
  currency: string = 'NGN',
  options: Partial<Intl.NumberFormatOptions> = {}
): string {
  // Handle invalid input
  if (value === null || value === undefined || isNaN(value)) {
    return '';
  }

  try {
    // Create formatter with the specified locale and currency
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
      ...options
    });

    // Return the formatted string
    return formatter.format(value);
  } catch (error) {
    console.error('Error formatting currency:', error);
    // Fallback formatting if Intl.NumberFormat fails
    return `${currency} ${value.toLocaleString()}`;
  }
}

/**
 * Formats a number with comma separators (no currency symbol)
 * @param value - The number to format
 * @param locale - The locale to use for formatting (default: 'en-NG')
 * @param options - Additional Intl.NumberFormatOptions to customize formatting
 * @returns Formatted number string with comma separators
 */
export function formatNumber(
  value: number,
  locale: string = 'en-NG',
  options: Partial<Intl.NumberFormatOptions> = {}
): string {
  // Handle invalid input
  if (value === null || value === undefined || isNaN(value)) {
    return '';
  }

  try {
    // Create formatter with the specified locale
    const formatter = new Intl.NumberFormat(locale, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
      ...options
    });

    // Return the formatted string
    return formatter.format(value);
  } catch (error) {
    console.error('Error formatting number:', error);
    // Fallback formatting if Intl.NumberFormat fails
    return value.toLocaleString();
  }
}

/**
 * Formats an input value as currency while typing
 * Removes non-numeric characters and formats with commas
 * @param inputValue - The string value from an input field
 * @param locale - The locale to use for formatting (default: 'en-NG')
 * @returns Formatted number string with comma separators (no currency symbol)
 */
export function formatCurrencyInput(
  inputValue: string,
  locale: string = 'en-NG'
): string {
  // Remove all non-numeric characters except decimal point
  const numericValue = inputValue.replace(/[^\d.]/g, '');
  
  // Parse as float and handle NaN
  const value = parseFloat(numericValue);
  if (isNaN(value)) {
    return '';
  }
  
  // Format with commas but no currency symbol
  return formatNumber(value, locale);
}