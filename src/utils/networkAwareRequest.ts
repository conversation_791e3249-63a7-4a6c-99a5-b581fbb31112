/* eslint-disable @typescript-eslint/no-explicit-any */

import { AxiosRequestConfig, AxiosResponse } from "axios";
import { upsertToast } from "@/components/ui/toaster";
import makeRequest from "./makeRequest";
import { animate } from "motion";

// Queue for storing requests made while offline
const requestQueue: Array<{
  config: AxiosRequestConfig;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}> = [];

// Track online status
let isOnline = navigator.onLine;

// Listen for online/offline events
window.addEventListener("online", () => {
  isOnline = true;
  processQueue();
});

window.addEventListener("offline", () => {
  isOnline = false;
});

let toastId: string | undefined;

// Process the queued requests when back online
function processQueue() {
  if (!isOnline || requestQueue.length === 0) return;

  // Show processing notification with animation
  toastId = upsertToast({
    id: toastId,
    type: "loading",
    title: "Processing offline requests",
    description: `Processing ${requestQueue.length} pending requests...`,
  });

  // Animate any loading indicators
  const loadingElements = document.querySelectorAll("[data-loading]");
  if (loadingElements.length) {
    animate(
      loadingElements,
      { opacity: [0.5, 1] } as any,
      { duration: 0.5, repeat: Infinity, easing: "ease-in-out" } as any
    );
  }

  // Process all queued requests
  const promises = requestQueue.map(({ config, resolve, reject }) => {
    return makeRequest(config)
      .then((response) => {
        resolve(response);
        return { success: true };
      })
      .catch((error) => {
        reject(error);
        return { success: false, error };
      });
  });

  // Clear the queue
  requestQueue.length = 0;

  // Update toast when all requests are processed
  Promise.all(promises).then((results) => {
    const successCount = results.filter((r) => r.success).length;

    if (toastId) {
      toastId = upsertToast({
        id: toastId,

        type: successCount === promises.length ? "success" : "warning",
        title: "Offline requests processed",
        description: `${successCount}/${promises.length} requests completed successfully`,
        meta: { closable: true },
      });
    }
  });
}

// Network-aware request function
export function networkAwareRequest<D, R>(
  config: AxiosRequestConfig<D>
): Promise<AxiosResponse<R>> {
  if (isOnline) {
    return makeRequest<D, R>(config);
  } else {
    // Queue the request if offline
    return new Promise((resolve, reject) => {
      requestQueue.push({ config, resolve, reject });

      // Notify user that request is queued
      toastId = upsertToast({
        id: toastId,
        type: "info",
        title: "You're offline",
        description: "Request queued and will be sent when you're back online",
        meta: { closable: true },
      });
    });
  }
}

// Network-aware versions of the API methods
export const networkAwareGet = <T>(url: string) =>
  networkAwareRequest<unknown, T>({ url, method: "GET" }).then(
    (r: AxiosResponse<T, unknown>) => r.data
  );

export const networkAwarePost = <T, D>(
  url: string,
  data: D,
  headers?: AxiosRequestConfig<any>["headers"]
) =>
  networkAwareRequest<unknown, T>({ url, data, method: "POST", headers }).then(
    (r: AxiosResponse<T, unknown>) => r.data
  );

export const networkAwarePut = <T, D>(
  url: string,
  data: D,
  headers?: AxiosRequestConfig<any>["headers"]
) =>
  networkAwareRequest<unknown, T>({ url, data, method: "PUT", headers }).then(
    (r: AxiosResponse<T, unknown>) => r.data
  );

export const networkAwareDestroy = <T, D = unknown>(
  url: string,
  data?: D,
  headers?: AxiosRequestConfig<any>["headers"]
) =>
  networkAwareRequest<unknown, T>({
    url,
    data,
    method: "DELETE",
    headers,
  }).then((r: AxiosResponse<T, unknown>) => r.data);

export default networkAwareRequest;
