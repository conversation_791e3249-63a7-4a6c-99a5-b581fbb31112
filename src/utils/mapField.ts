import { IListFilter } from "@/interfaces";
import { format, lastDayOfMonth, lastDayOfWeek, parse, startOfMonth, startOfWeek } from "date-fns";

export type MapForUnion = "appointment" | "wallet" | "provider" | "member" | "notification" | "partner";
export type MapKeyLiteral = `${MapForUnion}-${string}` | MapForUnion;

type MapType = Record<MapKeyLiteral, Record<string | number, string | undefined>>;
export function mapField(value: string | number | object, kind: MapKeyLiteral | "range" = "appointment") {
  const map: MapType = {
    appointment: {
      0: "Pending",
      1: "Upcoming",
      2: "Live",
      3: "Completed",
      4: "Canceled",
      "2.5": "Ended",
    },
    wallet: {
      1: "Successful",
      2: "Failed",
      3: "Abandoned",
    },
    "wallet-transaction_type": {
      1: "Credit",
      2: "Deduction",
    },
    provider: {
      0: "Pending",
      1: "Active",
      2: "Suspended",
      3: "Declined",
    },
    member: {
      1: "Active",
      2: "Suspended",
      3: "Declined",
    },
    notification: {
      0: "unread",
      1: "read",
    },
    partner: {
      0: "Pending",
      1: "Active",
      2: "Suspended",
      3: "Declined",
    },
    "provider-doc": {
      0: "Pending",
      1: "Approved",
      2: "Declined",
    },
  };

  if (typeof value === "object") {
    if (kind === "range") return mapRangeValue(value);
    return `${JSON.stringify(value)}`;
  }

  const kind_t = kind as MapKeyLiteral;
  const new_value = map[kind_t]?.[value];
  return new_value ? new_value : value;
}

function mapRangeValue(value: NonNullable<IListFilter["range"]>) {
  const { start_date, end_date } = value;

  const dates = [start_date, end_date].filter(Boolean).map((v) => v && formatRangeDate(v));
  if (dates.length > 1) return dates.join(" - ");
  return dates[0] || "";
}

// yyyy-MM-dd
export function formatRangeDate(value: string): string {
  try {
    const date = parse(value, "yyyy-MM-dd", new Date());
    return format(date, `MMM dd, yyyy`);
  } catch (error) {
    console.error("Invalid date format. Expected YYYY-MM-DD", error);
    return value; // Return original string if parsing fails
  }
}

export function getDateRange(value: string) {
  const fmt = (date: Date) => format(date, "yyyy-MM-dd");

  const map: Record<string, { start_date: string; end_date: string }> = {
    today: { start_date: fmt(new Date()), end_date: fmt(new Date()) },
    this_week: { start_date: fmt(startOfWeek(new Date())), end_date: fmt(lastDayOfWeek(new Date())) },
    this_month: { start_date: fmt(startOfMonth(new Date())), end_date: fmt(lastDayOfMonth(new Date())) },
  };

  return map[value] || value;
}
