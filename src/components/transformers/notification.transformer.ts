import { NotificationListDataRo } from "@/interfaces";
import { mapField } from "@/utils";
import { format, isToday, isValid, parseISO } from "date-fns";

export function toNotificationTableData(data: Partial<NotificationListDataRo>[]) {
  function transform_data(o: Partial<NotificationListDataRo>) {
    const { createdAt, notification_id, status, body, subject } = o;
    const datetime = () => {
      if (!createdAt || (createdAt && !isValid(parseISO(createdAt)))) return "N/A";
      const date = parseISO(createdAt);
      const day = isToday(date) ? "Today" : format(date, "EEE");
      return `${day} • ${format(date, "hh:mm aa")}`;
    };

    const read = status === 1;

    return {
      read,
      title: subject,
      datetime: datetime(),
      message: body || o?.message,
      id: notification_id || o?.id,
      status: typeof status === "number" ? mapField(status, "notification") : status || "unread",
    };
  }

  return data.map(transform_data);
}

export type NotificationTableDataType = ReturnType<typeof toNotificationTableData>[number];
