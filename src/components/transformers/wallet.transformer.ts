import { WalletListDataRo } from "@/interfaces";
import { mapField } from "@/utils";
import { format, isToday, isValid, parseISO } from "date-fns";

export function toWalletTableData(data: Partial<WalletListDataRo>[]) {
  function transform_data(o: Partial<WalletListDataRo>) {
    const { createdAt, transaction_id, amount, status, transaction_type, description } = o;
    const datetime = () => {
      if (!createdAt || (createdAt && !isValid(parseISO(createdAt)))) return "N/A";
      const date = parseISO(createdAt);
      const day = isToday(date) ? "Today" : format(date, "dd / MMM / yyyy");
      return `${day} • ${format(date, "hh:mm aa")}`;
    };

    return {
      datetime: datetime(),
      unit: amount ?? o?.unit,
      id: transaction_id || o?.id,
      description: description,
      type: transaction_type === 1 ? "Credit" : "Deduction",
      status: typeof status === "number" ? mapField(status, "wallet") : status || "Pending",
    };
  }

  return data.map(transform_data);
}

export type WalletTableDataType = ReturnType<typeof toWalletTableData>[number];
