import { PartnerListDataRo } from "@/interfaces";
import { mapField } from "@/utils";
import { format, isToday, isValid, parseISO } from "date-fns";

export function toPartnerTableData(data: Partial<PartnerListDataRo>[]) {
  function transform_data(o: Partial<PartnerListDataRo>) {
    const { org_data, status, min_amount, max_amount, org_id } = o;

    return {
      min_amount,
      max_amount,
      id: org_id,
      name: org_data?.name,
      avatar: org_data?.avatar,
      datetime: datetime(o?.createdAt),
      description: org_data?.biz_description,
      services: o?.service_data,
      status: typeof status === "number" ? mapField(status, "partner") : status || "Pending",
    };
  }

  return data.map(transform_data);
}

export type PartnerTableDataType = ReturnType<typeof toPartnerTableData>[number];

const datetime = (iso: string | undefined) => {
  if (!iso || (iso && !isValid(parseISO(iso)))) return "N/A";
  const date = parseISO(iso);
  const day = isToday(date) ? "Today" : format(date, "dd / MMM / yyyy");
  return `${day} • ${format(date, "hh:mm aa")}`;
};
