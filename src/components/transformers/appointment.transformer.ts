import { AppointmentListDataRo } from "@/interfaces";
import { joinNames, mapField } from "@/utils";
import { format, isToday, isValid, parseISO } from "date-fns";

export function toAppointmentTableData(data: Partial<AppointmentListDataRo>[]) {
  function transform_data(o: Partial<AppointmentListDataRo>) {
    const { appointment_id, member_data, provider_data, status, service_offer_name, org_payer_id, org_provider_id } = o;

    const booking_type = org_payer_id ? "organisation" : org_provider_id ? "direct" : "direct";

    return {
      // ...o,
      id: appointment_id || o?.id,
      amount: o?.amount,
      comm_mode: o?.comm_mode,
      tier: o?.tier_name,
      datetime: datetime(o?.appt_schedule),
      provider: provider_data?.name || o?.consultant,
      schedule_datetime: o?.appt_schedule,
      member: joinNames(member_data?.first_name, member_data?.last_name, o?.member),
      service: service_offer_name || o?.bookedby,
      specialty: provider_data?.specialty,
      provider_rating: provider_data?.rating || 0,
      avatar: provider_data?.avatar,
      rating_data: o?.rating_data,
      referred: o?.referral_indata?.status === 1 ? "Yes" : "No",
      booking_type,
      booking_ref: o?.booking_ref,
      status: typeof status === "number" ? mapField(status) : status || "Pending",
    };
  }

  return data.map(transform_data);
}

export type AppointmentTableDataType = ReturnType<typeof toAppointmentTableData>[number];

const datetime = (iso: string | undefined) => {
  if (!iso || (iso && !isValid(parseISO(iso)))) return "N/A";
  const date = parseISO(iso);
  const day = isToday(date) ? "Today" : format(date, "dd / MMM / yyyy");
  return `${day} • ${format(date, "hh:mm aa")}`;
};
