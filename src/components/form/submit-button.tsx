import Button, { ButtonProps } from "../ui/button";
import { useFormContext } from "./context";

export function SubmitButton(props: ButtonProps) {
  const { loading, disabled, ...xprops } = props;
  const form = useFormContext();

  return (
    <form.Subscribe
      selector={(state) => [state.canSubmit, state.isSubmitting]}
      children={([canSubmit, isSubmitting]) => (
        <Button
          type="submit"
          disabled={!canSubmit || isSubmitting || loading || disabled}
          loading={isSubmitting || loading}
          {...xprops}
        />
      )}
    />
  );
}
