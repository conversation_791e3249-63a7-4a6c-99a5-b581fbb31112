import { createFormHook } from "@tanstack/react-form";
import { fieldContext, formContext } from "./context";
import { SubmitButton } from "./submit-button";
// import { Textarea } from "@chakra-ui/react";
import { PropsWithChildren } from "react";
import { TextField } from "./text-field";
import { Select } from "./select";
import { PasswordField } from "./password-field";
import { LabelledSwitch } from "./labelled-switch";
import { PinInput } from "./pin-input";
import { AvatarUpload } from "./avatar-upload";
import { PhoneNumberField } from "./phone-number-field";
import { FileUploadField } from "./file-upload-field";
import { Textarea } from "./textarea";

// Allow us to bind components to the form to keep type safety but reduce production boilerplate
// Define this once to have a generator of consistent form instances throughout your app
export const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    TextField,
    Select,
    PasswordField,
    PhoneNumberField,
    LabelledSwitch,
    PinInput,
    Textarea,
    FileUploadField,
    AvatarUpload,
  },
  formComponents: {
    SubmitButton,
  },
  fieldContext,
  formContext,
});

export type AppFormType = Parameters<ReturnType<typeof withForm>>[0]["form"];
export type WithAppFormProps<T = Omit<PropsWithChildren, "children">> = T;
