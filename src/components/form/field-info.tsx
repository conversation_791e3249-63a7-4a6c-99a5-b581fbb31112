import { Field } from "@chakra-ui/react";
import { AnyField<PERSON>pi } from "@tanstack/react-form";
import { ArkError } from "arktype";

export function FieldErrorInfo({ field }: { field: AnyFieldApi }) {
  const error = field.state.meta.errors.at(0) as ArkError;

  if ((field.state.meta.isTouched || field.state.meta.isPristine) && error?.message) {
    return <Field.ErrorText>{error?.message || ""}</Field.ErrorText>;
  }

  return null;
}
