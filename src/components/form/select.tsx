import {
  Select as ChakraSelect,
  Portal,
  createListCollection,
  Field,
  useSelectContext,
  Stack,
  Span,
  HStack,
  Spinner,
  Box,
} from "@chakra-ui/react";
import { RefObject, useMemo, useState } from "react";
import { FieldErrorInfo } from "../form/field-info";
import { AnyFieldApi } from "@tanstack/react-form";
import { TextField } from "./text-field";
import { useDebouncedCallback } from "use-debounce";

export interface SelectItem {
  label: string;
  value: string;
  description?: string;
  valueText?: string;
}

export interface SelectProps extends Omit<ChakraSelect.RootProps, "collection"> {
  placeholder?: string;
  label?: string;
  items?: SelectItem[];

  /** Used withing a dialog
   * Due to the focus trap within the dialog, it's important to change the portal target from the document's body to the dialog's content.
   */
  portalContainerRef?: RefObject<HTMLElement | null>;
  field?: AnyFieldApi;
  ref?: RefObject<HTMLDivElement | null>;
  triggerProps?: ChakraSelect.TriggerProps;
  controlProps?: ChakraSelect.ControlProps;
  loading?: boolean;
  searchValue?: string;
  canFilterList?: boolean;

  labelProps?: Field.LabelProps;

  itemProps?: ChakraSelect.ItemProps;
  itemLabelProps?: ChakraSelect.ItemTextProps;
  itemDescriptionProps?: ChakraSelect.ItemTextProps;
}

export function Select(props: SelectProps) {
  const {
    label,
    items,
    placeholder,
    portalContainerRef,
    field,
    ref,
    triggerProps,
    controlProps,
    loading = false,
    canFilterList = false,
    searchValue,
    itemProps,
    itemLabelProps,
    itemDescriptionProps,
    labelProps,

    ...xprops
  } = props;

  const [internalValue, setInternalValue] = useState(searchValue || "");

  const debounced = useDebouncedCallback((value) => {
    setInternalValue(value);
  }, 500);

  const collection = useMemo(() => createListCollection({ items: items ?? [] }), [items]);
  const filtered = collection.items.filter((item) => (item?.label || "").toLowerCase().includes(internalValue.toLowerCase()));

  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;
  const is_empty = filtered.length < 1;

  // console.log("internalValue", internalValue);

  return (
    <Field.Root invalid={is_invalid} gap="4px">
      {!!label && (
        <Field.Label aria-label={label} fontSize="14px" fontWeight="400" color="text.2" {...labelProps}>
          <HStack>
            {label}
            {loading && <Spinner size="xs" color="primary" />}
          </HStack>
        </Field.Label>
      )}

      <ChakraSelect.Root variant="subtle" collection={collection} ref={ref} {...xprops}>
        <ChakraSelect.HiddenSelect />
        {/* <ChakraSelect.Label>Select framework - {variant}</ChakraSelect.Label> */}
        <ChakraSelect.Control {...controlProps}>
          <ChakraSelect.Trigger
            bg="input"
            minH="50px"
            focusRing="inside"
            focusRingColor="primary"
            css={{
              '&[data-placeholder-shown] > [data-part="value-text"]': {
                color: "text.2",
                fontSize: "14px",
                fontWeight: 400,
              },
            }}
            _focus={{ bg: "primary.50" }}
            {...triggerProps}
          >
            {/* <ChakraSelect.ValueText placeholder={placeholder} /> */}
            <SelectValue placeholder={placeholder} loading={false} _placeholder={{ color: "text.3" }} />
          </ChakraSelect.Trigger>
          <ChakraSelect.IndicatorGroup>
            <ChakraSelect.Indicator />
          </ChakraSelect.IndicatorGroup>
        </ChakraSelect.Control>
        <Portal container={portalContainerRef}>
          <ChakraSelect.Positioner>
            <ChakraSelect.Content>
              {canFilterList && (
                <TextField
                  label="search"
                  placeholder="Search..."
                  autoComplete="off"
                  w="100%"
                  hideLabel
                  minH="32px"
                  h="32px"
                  pos="sticky"
                  autoFocus={false}
                  top="0"
                  onChange={(e) => {
                    debounced(e.target.value);
                  }}
                  // Stop propagation to prevent the select from closing when typing and the spacebar is pressed
                  onKeyUp={(e) => e.stopPropagation()}
                  onKeyDown={(e) => e.stopPropagation()}
                />
              )}

              <Box overflowY="scroll" pt={canFilterList ? "12px" : "0"}>
                {is_empty && (
                  <ChakraSelect.Item item={{}}>
                    <ChakraSelect.ItemText color="text.2" pointerEvents="none" textAlign="center">
                      No items
                    </ChakraSelect.ItemText>
                  </ChakraSelect.Item>
                )}
                {!is_empty &&
                  filtered.map((item) => (
                    <ChakraSelect.Item item={item} key={item.value} _hover={{ bg: "primary.50" }} {...itemProps}>
                      <Stack gap="1">
                        <ChakraSelect.ItemText color="text" {...itemLabelProps}>
                          {item.label}
                        </ChakraSelect.ItemText>
                        {item?.description && (
                          <Span color="text.3" textStyle="xs" {...itemDescriptionProps}>
                            {item?.description}
                          </Span>
                        )}
                      </Stack>
                      <ChakraSelect.ItemIndicator />
                    </ChakraSelect.Item>
                  ))}
              </Box>
            </ChakraSelect.Content>
          </ChakraSelect.Positioner>
        </Portal>
      </ChakraSelect.Root>

      {!!field && <FieldErrorInfo field={field} />}
    </Field.Root>
  );
}

const SelectValue = (props: ChakraSelect.ValueTextProps & { loading?: boolean }) => {
  const { loading = false, ...xprops } = props;
  const select = useSelectContext();
  const items = select.selectedItems as Array<{
    label: string;
    value: string;
    valueText?: string;
  }>;
  const valueText = items?.[0]?.valueText;

  if (valueText) {
    return (
      <ChakraSelect.ValueText {...xprops}>
        <HStack>
          {valueText}
          {loading && <Spinner size="xs" color="primary" />}
        </HStack>
      </ChakraSelect.ValueText>
    );
  }

  return <ChakraSelect.ValueText {...xprops} children={loading && <Spinner size="xs" color="primary" />} />;
};
