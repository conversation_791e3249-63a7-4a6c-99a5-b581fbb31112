/* eslint-disable @typescript-eslint/no-explicit-any */
import { Input, InputGroup, InputGroupProps, InputProps, Skeleton } from "@chakra-ui/react";
import { Icon } from "../ui/icon";
import { SyntheticEvent, useState } from "react";
import { useDebouncedCallback } from "use-debounce";

interface SearchFieldProps extends Omit<InputGroupProps, "onChange" | "children"> {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  debounceTime?: number;
  inputProps?: Omit<InputProps, "onChange" | "value">;
  loading?: boolean;
}

export function SearchField(props: SearchFieldProps) {
  const {
    value: externalValue,
    onChange,
    placeholder = "Search...",
    debounceTime = 500,
    inputProps = {},
    loading = false,
    ...rest
  } = props;

  const [internalValue, setInternalValue] = useState(externalValue || "");

  const debounced = useDebouncedCallback((value) => {
    onChange?.(value);
  }, debounceTime);

  const handleSearchQuery = (e: SyntheticEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.value;
    setInternalValue(newValue);
    debounced(newValue);
  };

  return (
    <Skeleton variant="shine" asChild loading={loading}>
      <InputGroup maxW={{ base: "100%", "1sm": "300px" } as any} startElement={<Icon name="search" />} {...rest}>
        <Input
          variant="subtle"
          type="search"
          placeholder={placeholder}
          value={internalValue}
          onChange={handleSearchQuery}
          {...inputProps}
        />
      </InputGroup>
    </Skeleton>
  );
}
