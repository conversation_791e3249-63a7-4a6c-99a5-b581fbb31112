import { Input, InputGroup, InputProps, BoxProps, Field, HStack, Spinner, SelectValueChangeDetails } from "@chakra-ui/react";
import { forwardRef, ReactNode, Ref, RefObject, SyntheticEvent, useMemo, useRef } from "react";

import { AnyFieldApi } from "@tanstack/react-form";
import { FieldErrorInfo } from "../form/field-info";
import { Select } from "./select";
import { findCountryInfoByPhoneCode, PhoneCodeType, useCommonList, usePartialState } from "@/hooks";
// import { Icon } from "../ui/icon";
import { formatePhone, sanitizePhone } from "@/utils";

export interface PhoneNumberFieldProps extends InputProps {
  label: string;
  endElement?: ReactNode;
  _root?: BoxProps;
  field?: AnyFieldApi;

  value?: string;
  portalContainerRef?: RefObject<HTMLElement | null>;
  defaultPhoneCode?: string;
  onPhoneCodeChange?: (code: string) => void;
  onValueChange?: (value: string) => void;
}

export const PhoneNumberField = forwardRef((props: PhoneNumberFieldProps, ref) => {
  const {
    label,
    endElement,
    field,
    // _root,
    portalContainerRef,

    value,
    defaultPhoneCode = "234",
    onPhoneCodeChange,
    onValueChange,
    ...input_props
  } = props;

  // const recipe = useSlotRecipe({ recipe: inputFieldRecipe });
  // const styles = recipe({ variant: "solid" });

  const anchorRef = useRef<HTMLDivElement | null>(null);
  const id = useMemo(() => `${label}-${((Math.random() + 1) * 16) << 4}`, [label]);

  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;

  // This list should have been prefetched in the main layout file
  // Prefetching makes the UX better, if the list has not been prefetched, it will be fetched here
  // and a loading indicator would be displayed
  const { phoneData } = useCommonList("country-list");
  const { phoneCodes, isPending: loading_list } = phoneData;

  const [default_value, setDefaultValue] = usePartialState(
    {
      ...findCountryInfoByPhoneCode(phoneCodes, defaultPhoneCode || "234"),
      phoneNumber: value,
    },
    [phoneCodes, defaultPhoneCode, value]
  );

  const getAnchorRect = () => {
    if (anchorRef.current) {
      return anchorRef.current.getBoundingClientRect();
    }
    return {};
  };

  const handlePhonNumberChange = (e: SyntheticEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.value;
    setDefaultValue({
      phoneNumber: formatePhone(newValue, default_value?.value),
    });
    onValueChange?.(sanitizePhone(newValue));
  };

  const handlePhoneCodeChange = (e: SelectValueChangeDetails<PhoneCodeType>) => {
    const selection = e.items[0];
    const formatted_phone_number = formatePhone(default_value?.phoneNumber || "", selection?.value);
    setDefaultValue({ ...selection, phoneNumber: formatted_phone_number });
    onPhoneCodeChange?.(selection?.code);
  };

  return (
    <Field.Root ref={anchorRef} p="0" invalid={is_invalid} css={{ "--dropdown-w": "82px" }} gap="4px" className="msmt-input-field">
      <Field.Label aria-label={label} htmlFor={id} fontSize="14px" fontWeight="400" color="text.2">
        <HStack>
          {label}
          {loading_list && <Spinner size="xs" color="primary" />}
        </HStack>
      </Field.Label>

      <InputGroup
        className="input-group"
        startElement={
          <Select
            minW="var(--dropdown-w)"
            items={phoneCodes}
            value={[default_value?.value || ""]}
            name="phone code"
            // onValueChange={(e) => {
            //   const selection = e.items[0];
            //   setDefaultValue(selection);
            //   onPhoneCodeChange?.(selection?.code);
            // }}
            onValueChange={handlePhoneCodeChange}
            portalContainerRef={portalContainerRef}
            positioning={{ getAnchorRect, sameWidth: true }}
            focusRing="none"
            canFilterList
            triggerProps={{
              bg: "transparent",
              minH: "fit-content",
              rounded: "0",
              pos: "relative",
              focusRing: "none",
              focusVisibleRing: "none",
              // p: 0,

              _after: {
                content: "''",
                pos: "absolute",
                h: "80%",
                w: "1px",
                bg: "text.3",
                right: "0",
                rounded: "full",
              },
              _focus: {
                focusRing: "none",
              },
            }}
          />
        }
        endElementProps={{ className: "end-element" }}
        startElementProps={{ pointerEvents: "auto", p: 0 }}
        endElement={
          // endElement ? endElement : <Icon name="phone" boxSize="16px" />
          endElement
        }
      >
        <Input
          pl="calc(var(--dropdown-w) + 8px) !important"
          ref={ref as Ref<HTMLInputElement>}
          id={id}
          name={label}
          placeholder="************"
          value={default_value?.phoneNumber}
          onChange={handlePhonNumberChange}
          {...input_props}
          // {...styles.input}
        />
      </InputGroup>
      {/* </Box> */}

      {!!field && <FieldErrorInfo field={field} />}
    </Field.Root>
  );
});
