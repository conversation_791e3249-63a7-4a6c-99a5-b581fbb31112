import { InputGroup, Textarea as ChakraTextarea, TextareaProps as ChakraTextareaProps, Field, Stack, Text } from "@chakra-ui/react";
import { forwardRef } from "react";

import { FieldErrorInfo } from "../form/field-info";
import { AnyFieldApi } from "@tanstack/react-form";

interface TextareaProps extends ChakraTextareaProps {
  // endIcon?: IconNames;
  label?: string;
  field?: AnyFieldApi;
  hideCharCount?: boolean;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>((props, ref) => {
  const { label, field, value, maxLength = 1000, hideCharCount = false, ...xprops } = props;

  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;

  return (
    <Field.Root invalid={is_invalid}>
      {!!label && (
        <Field.Label aria-label={label} fontSize="14px" fontWeight="400" color="text.2">
          {label}
        </Field.Label>
      )}

      <Stack w="100%">
        <InputGroup
        // endElement={<Icon name={endIcon ? endIcon : "pen"} />}
        // endElementProps={{
        //   h: "fit-content",
        //   alignSelf: "flex-start",
        //   py: "8px",
        //   px: "8px",
        // }}
        >
          <ChakraTextarea
            ref={ref}
            value={value}
            maxLength={maxLength}
            autoresize
            // pr="20px !important"
            {...xprops}
          />
        </InputGroup>

        {!hideCharCount && (
          <Text fontSize="12px" color="text.2" alignSelf="flex-end">
            {value?.toString().length} / {maxLength}
          </Text>
        )}
      </Stack>

      {field && <FieldErrorInfo field={field} />}
    </Field.Root>
  );
});
