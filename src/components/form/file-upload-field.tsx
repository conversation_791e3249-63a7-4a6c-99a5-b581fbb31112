import { Box, Field, FileUpload, Stack } from "@chakra-ui/react";
import { Icon } from "../ui/icon";
import { AnyFieldApi } from "@tanstack/react-form";
import { FieldErrorInfo } from "./field-info";
import { SyntheticEvent } from "react";

interface FileUploadFieldProps extends FileUpload.RootProps {
  label?: string;
  field?: AnyFieldApi;
  rootProps?: Field.RootProps;
  onFileRemoved?: (file: File) => void;
}

// TODO: Create a custom hook to store, upload files and track their progress.

export function FileUploadField(props: FileUploadFieldProps) {
  const { label, field, onFileRemoved, rootProps, ...xprops } = props;

  const id = `${label}-${((Math.random() + 1) * 16) << 4}`;
  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;

  return (
    <Field.Root {...rootProps} invalid={is_invalid}>
      {!!label && (
        <Field.Label aria-label={label} htmlFor={id} fontSize="14px" fontWeight="400" color="text.2">
          {label}
        </Field.Label>
      )}

      <FileUpload.Root alignItems="stretch" maxFiles={10} {...xprops}>
        <FileUpload.HiddenInput name={id} />
        <FileUpload.Dropzone minH="unset" h="192px" bg="primary.50" borderStyle="dashed" borderColor="primary" focusRingColor={"primary"}>
          <Icon name="cloud_upload" boxSize="40px" />
          <FileUpload.DropzoneContent>
            <Box fontSize="14px" color="text.3">
              Drag and drop files or
            </Box>
            <Box fontSize="14px" color="primary" fontWeight="600" textDecoration="underline" cursor="pointer">
              Select file
            </Box>
          </FileUpload.DropzoneContent>
        </FileUpload.Dropzone>
        {/* <FileUpload.List clearable /> */}

        <FileUploadList onFileRemoved={onFileRemoved} />
      </FileUpload.Root>

      {field && <FieldErrorInfo field={field} />}
    </Field.Root>
  );
}

interface FileUploadListProps extends FileUpload.ItemGroupProps {
  onFileRemoved?: (file: File) => void;
}

export function FileUploadList(props: FileUploadListProps) {
  const { onFileRemoved, ...xprops } = props;
  const handleFileRemove = (e: SyntheticEvent, file: File) => {
    e.preventDefault();
    onFileRemoved?.(file);
  };
  return (
    <FileUpload.ItemGroup {...xprops}>
      <FileUpload.Context>
        {({ acceptedFiles }) =>
          acceptedFiles.map((file) => (
            <FileUpload.Item key={file.name} file={file}>
              <FileUpload.ItemPreview />
              <Stack gap="0">
                <FileUpload.ItemName />
                <FileUpload.ItemSizeText />
              </Stack>

              <Box flex="1" />
              {/* <ProgressCircle size="xs" colorPalette="green" thickness="3px" /> */}
              <FileUpload.ItemDeleteTrigger alignSelf="center" onClick={(e) => handleFileRemove(e, file)} />
            </FileUpload.Item>
          ))
        }
      </FileUpload.Context>
    </FileUpload.ItemGroup>
  );
}
