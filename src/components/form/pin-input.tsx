import { PinInput as ChakraPinInput, Field, Group } from "@chakra-ui/react";
import { AnyFieldApi } from "@tanstack/react-form";
import * as React from "react";
import { FieldErrorInfo } from "../form/field-info";

export interface PinInputProps extends ChakraPinInput.RootProps {
  rootRef?: React.Ref<HTMLDivElement>;
  count?: number;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  attached?: boolean;
  field?: AnyFieldApi;
}

export const PinInput = React.forwardRef<HTMLInputElement, PinInputProps>(function PinInput(props, ref) {
  const { count = 6, inputProps, rootRef, attached, field, ...rest } = props;

  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;

  return (
    <Field.Root w="100%" invalid={is_invalid}>
      <ChakraPinInput.Root w="100%" ref={rootRef} otp {...rest}>
        <ChakraPinInput.HiddenInput ref={ref} {...inputProps} />
        <ChakraPinInput.Control w="100%">
          <Group attached={attached} w="100%" justifyContent="space-between">
            {Array.from({ length: count }).map((_, index) => (
              <ChakraPinInput.Input
                key={index}
                index={index}
                placeholder=""
                w="54px"
                h="48px"
                focusRingColor="primary"
                bg="#F6F8F9"
                rounded="4px"
                borderColor="transparent"
              />
            ))}
          </Group>
        </ChakraPinInput.Control>

        {!!field && <FieldErrorInfo field={field} />}
      </ChakraPinInput.Root>
    </Field.Root>
  );
});
