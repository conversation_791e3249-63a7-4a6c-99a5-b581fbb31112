import { Avatar, Box, Field, FileUpload, Float, useFileUploadContext } from "@chakra-ui/react";
import { LuX } from "react-icons/lu";
import Button from "../ui/button";
import { Icon } from "../ui/icon";
import { AnyFieldApi } from "@tanstack/react-form";
import { FieldErrorInfo } from "./field-info";

interface AvatarFilePreviewProps {
  src?: string | null;
  name?: string;
}

interface AvatarUploadProps extends FileUpload.RootProps {
  src?: string;
  name?: string;
  btnText?: string;
  field?: AnyFieldApi;
}

export function AvatarFilePreview(props: AvatarFilePreviewProps) {
  const { src, name } = props;

  const fileUpload = useFileUploadContext();
  const files = fileUpload.acceptedFiles;

  // if (files.length === 0 && !src) return null;

  if (files.length === 0)
    return (
      <Avatar.Root boxSize="100px">
        <Avatar.Fallback name={name} />
        {src && <Avatar.Image src={src} />}
      </Avatar.Root>
    );

  return (
    <FileUpload.ItemGroup alignItems="center">
      {files.map((file) => (
        <FileUpload.Item
          // w="auto"
          boxSize="100px"
          p="0"
          rounded="full"
          file={file}
          key={file.name}
        >
          <Box w="auto" boxSize="100px" p="0" rounded="full" overflow="hidden">
            <FileUpload.ItemPreviewImage boxSize="100%" objectFit="cover" />
          </Box>

          <Float placement="top-end">
            <FileUpload.ItemDeleteTrigger boxSize="4" layerStyle="fill.solid" rounded="full">
              <LuX />
            </FileUpload.ItemDeleteTrigger>
          </Float>
        </FileUpload.Item>
      ))}
    </FileUpload.ItemGroup>
  );
}

export function AvatarUpload(props: AvatarUploadProps) {
  const { src: raw_src, name, btnText = "Upload member avatar", field, ...xprops } = props;

  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;

  // FIX FOR:
  // An empty string ("") was passed to the src attribute.
  // This may cause the browser to download the whole page again over the network.
  // To fix this, either do not render the element at all or pass null to src instead of an empty string.
  const src = raw_src && Boolean(raw_src) ? raw_src : null;

  return (
    <Field.Root alignItems="center" invalid={is_invalid}>
      <FileUpload.Root alignItems="center" accept="image/*" {...xprops}>
        <FileUpload.HiddenInput />

        <AvatarFilePreview src={src} name={name} />

        <FileUpload.Trigger asChild>
          <Button
            variant="plain"
            size="sm"
            leftIcon={<Icon name="camera" color="text.2" />}
            color="primary"
            fontSize="12px"
            fontWeight="500"
            _hover={{
              border: "none",
              textDecoration: "underline",
              _before: { display: "none" },
              // "& > span > *": { color: "white !important" },
            }}
          >
            {btnText}
          </Button>
        </FileUpload.Trigger>
      </FileUpload.Root>
      {field && <FieldErrorInfo field={field} />}
    </Field.Root>
  );
}
