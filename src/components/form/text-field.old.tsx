import { Input, InputProps, BoxProps, Field, InputGroup } from "@chakra-ui/react";
import { forwardRef, ReactNode, Ref, useMemo } from "react";

// import { TextEdit } from "./icons";
import { AnyFieldApi } from "@tanstack/react-form";
import { FieldErrorInfo } from "./field-info";

export interface TextFieldProps extends InputProps {
  label: string;
  endElement?: ReactNode;
  _root?: BoxProps;
  field?: AnyFieldApi;
  hideLabel?: boolean;
}

export const TextField = forwardRef((props: TextFieldProps, ref) => {
  const { endElement, label, field, hideLabel, ...input_props } = props;
  // const recipe = useSlotRecipe({ recipe: inputFieldRecipe });
  // const styles = recipe({ variant: "solid" });

  const id = useMemo(() => `${label}-${((Math.random() + 1) * 16) << 4}`, [label]);

  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;

  return (
    <Field.Root invalid={is_invalid} gap="4px">
      {!hideLabel && (
        <Field.Label aria-label={label} htmlFor={id} fontSize="14px" fontWeight="400" color="text.2">
          {label}

          {/* <Field.RequiredIndicator
          fallback={
            <Badge size="xs" variant="surface">
              Optional
            </Badge>
          }
;        /> */}
        </Field.Label>
      )}

      <InputGroup endElement={endElement}>
        <Input
          ref={ref as Ref<HTMLInputElement>}
          id={id}
          name={label}
          placeholder=""
          {...input_props}
          // {...styles.input}
        />
      </InputGroup>

      {!!field && <FieldErrorInfo field={field} />}
    </Field.Root>
  );
});

// const inputFieldRecipe = defineSlotRecipe({
//   slots: ["root", "label", "input"],
//   className: "msmt-input-field",
//   base: {
//     root: {
//       w: "100%",
//       "--px": "8px",
//       "--py": "4px",
//       borderRadius: "4px",
//       position: "relative",
//     },
//   },

//   variants: {
//     variant: {
//       solid: {
//         root: {
//           display: "flex",
//           flexDir: "row",
//           bg: "#F6F8F9",
//           px: "var(--px)",
//           py: "4px",
//           minW: { sm: "100%", md: "294px" },
//           minH: "50px",
//           fontSize: "14px",

//           border: "1px solid",
//           borderColor: "transparent",

//           _focusWithin: {
//             borderColor: "primary",
//             bg: "primary.50",

//             "& > label": {
//               top: "var(--py)",
//               fontSize: "12px",
//               color: "#354959",
//               transform: "translateY(0%)",
//               transition: "all 300ms ease-in-out",
//             },

//             "& > .input-group .end-element": {
//               // bg: "red",
//               // alignItems: "flex-end",
//               transform: "translateY(14px)",
//               transition: "all 300ms ease-in-out",
//             },
//           },

//           "&:has(.input-group input:not(:placeholder-shown))": {
//             borderColor: "transparent",
//             // bg: "primary.50",

//             "& > label": {
//               top: "var(--py)",
//               fontSize: "12px",
//               color: "#354959",
//               transform: "translateY(0%)",
//               transition: "all 300ms ease-in-out",
//             },

//             "& > .input-group .end-element": {
//               // bg: "red",
//               // alignItems: "flex-end",
//               transform: "translateY(14px)",
//               transition: "all 300ms ease-in-out",
//             },
//           },

//           "& > .input-group .end-element": {
//             p: 0,
//             color: "text.3",
//             w: "14px",
//             willChange: "contents",
//             transition: "all 300ms ease-in-out",
//           },
//         },
//         label: {
//           bg: "transparent",
//           pos: "absolute",
//           top: "50%",
//           bottom: 0,
//           color: "#354959",
//           left: "var(--px)",
//           transform: "translateY(-50%)",
//           transition: "all 300ms ease-in-out",
//           zIndex: 2,
//         },
//         input: {
//           bg: "transparent",
//           p: 0,
//           pt: "25px",
//           w: "100%",
//           h: "100%",
//           border: "none",
//           shadow: "none",
//           fontWeight: "500",
//           _focusVisible: {
//             border: "none",
//             outlineColor: "transparent",
//           },
//         },
//       },
//     },

//     size: {
//       sm: {
//         root: { fontSize: "14px" },
//       },
//     },
//   },

//   defaultVariants: {
//     variant: "solid",
//     size: "sm",
//   },
// });
