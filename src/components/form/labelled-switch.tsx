import { chakra, HStack, Text, StackProps } from "@chakra-ui/react";
import { Switch, SwitchProps } from "../ui/switch";
import { ReactNode, useMemo } from "react";
import { AnyFieldApi } from "@tanstack/react-form";

interface LabelledSwitchProps extends StackProps {
  label: ReactNode;
  switchProps?: SwitchProps;
  field?: AnyFieldApi;
}

export function LabelledSwitch(props: LabelledSwitchProps) {
  const { label, switchProps, field, ...xprops } = props;

  const id = useMemo(() => `msmt-labelled-switch-${((Math.random() + 1) * 16) << 4}`, []);

  /// For some reason chakra ui adds these strings to the inner switch label
  /// to make our label togglable, we need to add the same strings
  const label_for = `switch:${id}:input`;
  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;

  return (
    <chakra.label w="100%" htmlFor={label_for} borderColor={is_invalid ? "red" : "transparent"}>
      <HStack
        bg="input"
        px="12px"
        rounded="4px"
        border="1px solid"
        borderColor="#F2F3F5"
        py="8px"
        justifyContent="space-between"
        {...xprops}
      >
        <Text color="text" fontSize="12px" fontWeight="500">
          {label}
        </Text>
        <Switch size="sm" id={id} name={typeof label === "string" ? label : id} {...switchProps} />
      </HStack>
    </chakra.label>
  );
}
