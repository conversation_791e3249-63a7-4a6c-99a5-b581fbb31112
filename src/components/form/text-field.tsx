/* eslint-disable @typescript-eslint/no-explicit-any */
import { Input, InputProps, BoxProps, Field, InputGroup } from "@chakra-ui/react";
import { forwardRef, ReactNode, Ref, useMemo, useState, useEffect, ChangeEvent } from "react";

// import { TextEdit } from "./icons";
import { AnyFieldApi } from "@tanstack/react-form";
import { FieldErrorInfo } from "./field-info";
import { formatCurrencyInput } from "@/utils/formatCurrency";

export interface TextFieldProps extends InputProps {
  label: string;
  endElement?: ReactNode;
  _root?: BoxProps;
  field?: AnyFieldApi;
  hideLabel?: boolean;
  formatNumber?: boolean; // Optional prop to enable/disable number formatting
}

export const TextField = forwardRef((props: TextFieldProps, ref) => {
  const {
    endElement,
    label,
    field,
    hideLabel,
    type,
    value,
    onChange,
    formatNumber = false, // Default to false
    ...input_props
  } = props;

  // State to track the formatted display value
  const [displayValue, setDisplayValue] = useState<string | number>((value || "") as string);

  // Track the actual numeric value for number inputs
  const [numericValue, setNumericValue] = useState<string>(typeof value === "number" ? value.toString() : (value as string) || "");

  // Update display value when the value prop changes
  useEffect(() => {
    if (value !== undefined) {
      if (type === "number" && formatNumber) {
        // For number inputs, format the display value but keep the raw value
        const stringValue = typeof value === "number" ? value.toString() : (value as string) || "";
        setNumericValue(stringValue.replace(/[^\d.]/g, ""));
        setDisplayValue(formatCurrencyInput(stringValue));
      } else {
        // For other inputs, use the value directly
        setDisplayValue((value || "") as string);
      }
    }
  }, [value, type, formatNumber]);

  const id = useMemo(() => `${label}-${((Math.random() + 1) * 16) << 4}`, [label]);

  const is_invalid = !!field && field.state.meta.errors.length > 0 && field.state.meta.isTouched;

  // Handle input changes
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    if (type === "number" && formatNumber) {
      // For number inputs, format the display value
      const rawValue = newValue.replace(/[^\d.]/g, "");
      setNumericValue(rawValue);

      // Update the display value with formatting
      const formattedValue = formatCurrencyInput(newValue);
      setDisplayValue(formattedValue);

      // Create a synthetic event with the raw numeric value
      const syntheticEvent = {
        ...e,
        target: {
          ...e.target,
          value: rawValue,
        },
      };

      // Call the original onChange with the synthetic event
      onChange?.(syntheticEvent as any);
    } else {
      // For other inputs, use the value directly
      setDisplayValue(newValue);
      onChange?.(e);
    }
  };

  return (
    <Field.Root invalid={is_invalid} gap="4px" {...(props._root || {})}>
      {!hideLabel && (
        <Field.Label aria-label={label} htmlFor={id} fontSize="14px" fontWeight="400" color="text.2">
          {label}
        </Field.Label>
      )}

      <InputGroup endElement={endElement}>
        <Input
          ref={ref as Ref<HTMLInputElement>}
          id={id}
          name={label}
          placeholder=""
          value={displayValue}
          onChange={handleChange}
          // For number inputs, use "text" type to allow formatted display
          type={type === "number" && formatNumber ? "text" : type}
          // Pass the raw numeric value to the hidden input for form submission
          {...(type === "number" && formatNumber
            ? {
                "data-numeric-value": numericValue,
              }
            : {})}
          {...input_props}
        />
      </InputGroup>

      {!!field && <FieldErrorInfo field={field} />}
    </Field.Root>
  );
});

// const inputFieldRecipe = defineSlotRecipe({
//   slots: ["root", "label", "input"],
//   className: "msmt-input-field",
//   base: {
//     root: {
//       w: "100%",
//       "--px": "8px",
//       "--py": "4px",
//       borderRadius: "4px",
//       position: "relative",
//     },
//   },

//   variants: {
//     variant: {
//       solid: {
//         root: {
//           display: "flex",
//           flexDir: "row",
//           bg: "#F6F8F9",
//           px: "var(--px)",
//           py: "4px",
//           minW: { sm: "100%", md: "294px" },
//           minH: "50px",
//           fontSize: "14px",

//           border: "1px solid",
//           borderColor: "transparent",

//           _focusWithin: {
//             borderColor: "primary",
//             bg: "primary.50",

//             "& > label": {
//               top: "var(--py)",
//               fontSize: "12px",
//               color: "#354959",
//               transform: "translateY(0%)",
//               transition: "all 300ms ease-in-out",
//             },

//             "& > .input-group .end-element": {
//               // bg: "red",
//               // alignItems: "flex-end",
//               transform: "translateY(14px)",
//               transition: "all 300ms ease-in-out",
//             },
//           },

//           "&:has(.input-group input:not(:placeholder-shown))": {
//             borderColor: "transparent",
//             // bg: "primary.50",

//             "& > label": {
//               top: "var(--py)",
//               fontSize: "12px",
//               color: "#354959",
//               transform: "translateY(0%)",
//               transition: "all 300ms ease-in-out",
//             },

//             "& > .input-group .end-element": {
//               // bg: "red",
//               // alignItems: "flex-end",
//               transform: "translateY(14px)",
//               transition: "all 300ms ease-in-out",
//             },
//           },

//           "& > .input-group .end-element": {
//             p: 0,
//             color: "text.3",
//             w: "14px",
//             willChange: "contents",
//             transition: "all 300ms ease-in-out",
//           },
//         },
//         label: {
//           bg: "transparent",
//           pos: "absolute",
//           top: "50%",
//           bottom: 0,
//           color: "#354959",
//           left: "var(--px)",
//           transform: "translateY(-50%)",
//           transition: "all 300ms ease-in-out",
//           zIndex: 2,
//         },
//         input: {
//           bg: "transparent",
//           p: 0,
//           pt: "25px",
//           w: "100%",
//           h: "100%",
//           border: "none",
//           shadow: "none",
//           fontWeight: "500",
//           _focusVisible: {
//             border: "none",
//             outlineColor: "transparent",
//           },
//         },
//       },
//     },

//     size: {
//       sm: {
//         root: { fontSize: "14px" },
//       },
//     },
//   },

//   defaultVariants: {
//     variant: "solid",
//     size: "sm",
//   },
// });
