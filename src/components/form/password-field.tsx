/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import { NotVisible, Visible } from "../ui/icons";
import { TextField, TextFieldProps } from "./text-field";
import { Checkbox, HStack, IconButton, Stack } from "@chakra-ui/react";
import { useFormContext } from "./context";
import { AnyFieldApi } from "@tanstack/react-form";

type PasswordFieldProps = TextFieldProps;

// TODO: Add the password length, special character, uppercase, lowercase, and at one number checks.
export function PasswordField(props: PasswordFieldProps) {
  const [is_visible, setVisibility] = useState(false);

  return (
    <TextField
      type={is_visible ? "text" : "password"}
      endElement={
        <IconButton
          aria-label="toggle password visibility"
          size="xs"
          boxSize="16px"
          rounded="md"
          variant="plain"
          color="text.3"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setVisibility(!is_visible);
          }}
        >
          {is_visible ? <NotVisible boxSize="16px" /> : <Visible boxSize="16px" />}
        </IconButton>
      }
      {...props}
    />
  );
}

function PasswordFieldValidationItem(props: Checkbox.RootProps) {
  const { children, ...xprops } = props;
  return (
    <Checkbox.Root {...xprops}>
      <Checkbox.HiddenInput />
      <Checkbox.Control
        boxSize="10px"
        rounded="full"
        css={{
          "&[data-state=checked]": {
            bg: "primary",
            borderColor: "primary",

            "& svg": {
              strokeWidth: "6px",
              transform: "scale(1.5)",
            },
          },
        }}
      />

      <Checkbox.Label fontSize="12px" color="text.3" fontWeight="400" _checked={{ color: "primary" }} _invalid={{ color: "stroke.error" }}>
        {children}
      </Checkbox.Label>
    </Checkbox.Root>
  );
}

export function PasswordFieldValidationChecks(props: { fieldname: string; field?: AnyFieldApi }) {
  const { fieldname, field } = props;
  const form = useFormContext();

  const is_invalid = !!field && field.state.meta.errors.length > 0;

  return (
    <form.Subscribe selector={(state) => [state.values[fieldname] as string]}>
      {([value]) => {
        // Check password validation rules
        const password = (value || "") as string;

        const hasMinLength = password.length >= 8;
        // const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        const hasUpperAndLowerCase = hasUpperCase && hasLowerCase;

        return (
          <Stack gap="4px" flexWrap="wrap">
            <HStack
              gap={{ base: "4px", "2sm": "16px" } as any}
              alignItems={{ base: "flex-start", "2sm": "center" } as any}
              flexDir={{ base: "column", "2sm": "row" } as any}
            >
              <PasswordFieldValidationItem invalid={is_invalid && !hasMinLength} checked={hasMinLength}>
                At least 8 characters long
              </PasswordFieldValidationItem>

              <PasswordFieldValidationItem invalid={is_invalid && !hasUpperAndLowerCase} checked={hasUpperAndLowerCase}>
                Uppercase and lowercase
              </PasswordFieldValidationItem>

              {/* <PasswordFieldValidationItem
                invalid={is_invalid && !hasSpecialChar}
                checked={hasSpecialChar}
              >
                At least One special character
              </PasswordFieldValidationItem> */}
            </HStack>

            <HStack
              gap={{ base: "4px", "2sm": "16px" } as any}
              alignItems={{ base: "flex-start", "2sm": "center" } as any}
              flexDir={{ base: "column", "2sm": "row" } as any}
            >
              <PasswordFieldValidationItem invalid={is_invalid && !hasNumber} checked={hasNumber}>
                At least One number
              </PasswordFieldValidationItem>
            </HStack>
          </Stack>
        );
      }}
    </form.Subscribe>
  );
}
