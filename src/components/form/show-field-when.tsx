/* eslint-disable @typescript-eslint/no-explicit-any */
import { Show, ShowProps } from "@chakra-ui/react";
import { useFormContext } from "./context";

type ShowFieldWhenProps<T = Record<string, never>> = ShowProps<
  (values: T) => boolean
>;

export function ShowFieldWhen(props: ShowFieldWhenProps) {
  const { when, children } = props;
  const form = useFormContext();

  return (
    <form.Subscribe selector={(state) => !!when?.(state.values)}>
      {(result) => <Show<any> when={result} children={children} />}
    </form.Subscribe>
  );
}
