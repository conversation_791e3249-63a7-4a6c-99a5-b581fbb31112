import { useAuthStore } from "@/stores";
import { Fragment } from "react/jsx-runtime";
import { chakra, <PERSON>ton, Show } from "@chakra-ui/react";
import { add } from "date-fns";
import { useCountdownTimer } from "@/hooks";
import { useMemo, useState } from "react";
import { ResendOtpDto } from "@/schemas";
import configs from "@/config";

interface ResendOTPProps {
  email?: string;
  requestType: ResendOtpDto["request_type"];
  timeStart: number;
  label?: string | ((hasMadeFirstReq: boolean) => string);
}

export function ResendOTP(props: ResendOTPProps) {
  const { email: input_email, requestType, label = "Resend code", timeStart } = props;

  const resendOtp = useAuthStore((state) => state.resendOtp);
  const user = useAuthStore((state) => state.user);
  const timer = useAuthStore((state) => state.timer);
  // const timer = useAuthStore((state) => state.timer);
  const setTimer = useAuthStore((state) => state.setTimer);
  const clearTimer = useAuthStore((state) => state.clearTimer);

  const [hasMadeFirstReq, setHasMadeFirstReq] = useState(timeStart > 0);

  const { asSeconds, isRunning } = useCountdownTimer(new Date(timeStart), {
    onFinish() {
      clearTimer();
    },
  });

  const label_str = useMemo(() => {
    if (typeof label === "function") return label(hasMadeFirstReq);
    return label;
  }, [hasMadeFirstReq, label]);

  const onResend = async () => {
    const email = input_email || user?.email;
    if (!email) return;

    const result = await resendOtp({ email, request_type: requestType });
    if (result.ok) {
      setTimer("otp", {
        start: add(new Date(), {
          seconds: configs.OTP_RESEND_TIMEOUT_SECS,
        }).getTime(),
      });
      setHasMadeFirstReq(true);
    }
  };

  const isLoading = (key: keyof typeof timer) => {
    return timer[key]?.loading;
  };

  return (
    <Fragment>
      <Show when={isRunning}>
        <chakra.span fontWeight="500" fontSize="14px" color="text.2">
          {label_str} in{" "}
          <chakra.span fontWeight="700" fontSize="14px">
            {asSeconds}s
          </chakra.span>
        </chakra.span>
      </Show>

      <Show when={!isRunning}>
        <Button
          w="fit-content"
          variant="ghost"
          p="0px"
          fontWeight="500"
          fontSize="14px"
          color="text.2"
          textDecoration="none"
          textUnderlineOffset={"8px"}
          _hover={{ bg: "transparent", color: "primary" }}
          onClick={onResend}
          disabled={isLoading("otp")}
          loading={isLoading("otp")}
        >
          {label_str}
        </Button>
      </Show>
    </Fragment>
  );
}
