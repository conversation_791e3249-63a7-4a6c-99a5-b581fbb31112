import {
  Heading,
  Skeleton,
  Stack,
  StackProps,
  Text,
  TextProps,
} from "@chakra-ui/react";
import { isValidElement, ReactNode, useMemo } from "react";

export interface FieldCardProps extends StackProps {
  heading?: ReactNode;
  description?: ReactNode;

  _header?: StackProps;
  _content?: StackProps;
  loading?: boolean;
  headingProps?: TextProps;
  descriptionProps?: TextProps;
}

export function FieldCard(props: FieldCardProps) {
  const {
    heading,
    description,
    _header,
    _content,
    loading = false,
    children,
    headingProps,
    descriptionProps,
    ...xprops
  } = props;

  const isElement = useMemo(
    () => (value: ReactNode) => isValidElement(value),
    []
  );

  return (
    <Stack
      bg="white"
      w="100%"
      rounded={{ sm: "8px", md: "8px" }}
      p={{ sm: "16px", md: "24px" }}
      gap="20px"
      border="1px solid transparent"
      borderColor="stroke.divider"
      {...xprops}
    >
      {!!(heading || description) && (
        <Stack gap="4px" {..._header}>
          {!!heading && !isElement(heading) ? (
            <Skeleton w="fit-content" loading={loading}>
              <Heading
                as="h5"
                id="heading"
                fontSize="md"
                fontWeight="600"
                {...headingProps}
              >
                {heading}
              </Heading>
            </Skeleton>
          ) : (
            heading
          )}

          {!!description && !isElement(description) ? (
            <Skeleton w="fit-content" loading={loading}>
              <Text
                id="description"
                color="text"
                fontSize="12px"
                fontWeight="400"
                {...descriptionProps}
              >
                {description}
              </Text>
            </Skeleton>
          ) : (
            description
          )}
        </Stack>
      )}

      <Stack gap="20px" {..._content}>
        {children}
      </Stack>
    </Stack>
  );
}
