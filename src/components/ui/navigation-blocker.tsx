import { PropsWithChildren, useCallback, useEffect, useRef } from "react";
import { useNavigate, useBlocker, BlockerFunction, useBeforeUnload } from "react-router";
import { EventEmitter } from "eventemitter3";
import { NavigationBlockerContext, NavigationBlockerProps, usePartialState } from "@/hooks";
import { confirmation_dialog } from "./confirmation-modal";

type UseBlockerNextLocation = Parameters<BlockerFunction>[0];
const emitter = new EventEmitter();

export function NavigationBlocker(props: NavigationBlockerProps) {
  const { block_navigations, block_reloads, onConfirm } = props;
  const navigate = useNavigate();
  //   const { open, onOpen, onClose } = useDisclosure();

  const { current: id } = useRef("nav-blocker");

  const can_leave_view_ref = useRef(!block_navigations);
  const next_location_ref = useRef<UseBlockerNextLocation | null>(null);

  const on_confirm = async () => {
    await onConfirm?.();
    can_leave_view_ref.current = true;
    // onClose();
    confirmation_dialog.close(id);
    emitter.emit("leave_page");
  };

  //   console.log("Nav Blocker", { block_navigations, can_leave_view_ref });

  useBlocker((next) => {
    // console.log("About to leave view", { next, can_leave_view_ref, next_location_ref });
    if (!can_leave_view_ref.current) {
      //   onOpen();

      confirmation_dialog.open(id, {
        title: "Leave Page?",
        description: "Changes that you made may not be saved.",
        onConfirm: on_confirm,
        applyBtnText: "Leave",
      });
      next_location_ref.current = next;
      return true;
    }

    return !can_leave_view_ref.current;
  });

  useBeforeUnload((event) => {
    if (block_reloads) {
      event.preventDefault();
      return "";
    }
  });

  const handle_leave_page = useCallback(() => {
    if (next_location_ref?.current !== null) {
      const { nextLocation } = next_location_ref.current;
      next_location_ref.current = null;
      navigate(nextLocation?.pathname /*, { viewTransition: true }*/);
    }
  }, [next_location_ref, navigate]);

  useEffect(() => {
    emitter.addListener("leave_page", handle_leave_page);
    return () => {
      emitter.removeListener("leave_page", handle_leave_page);
    };
  }, [handle_leave_page]);

  useEffect(() => {
    can_leave_view_ref.current = !block_navigations;
  }, [block_navigations]);

  return <confirmation_dialog.Viewport />;
}

export function NavigationBlockerProvider(props: PropsWithChildren<NavigationBlockerProps>) {
  const { children, block_navigations, block_reloads, onConfirm } = props;

  const [state, set] = usePartialState<NavigationBlockerProps>(
    {
      block_navigations: block_navigations ?? false,
      block_reloads: block_reloads ?? false,
      onConfirm: onConfirm ?? (async () => {}),
    },
    [block_navigations, block_reloads, onConfirm]
  );

  //   console.log("Blocker", { ...state });

  return (
    <NavigationBlockerContext.Provider value={{ state, set }}>
      {children}
      <NavigationBlocker {...state} />
    </NavigationBlockerContext.Provider>
  );
}
