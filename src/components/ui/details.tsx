import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>kel<PERSON>, Span, <PERSON><PERSON>, StackProps, Text, TextProps } from "@chakra-ui/react";
import { isValidElement, ReactNode, useMemo } from "react";
import { Icon } from "./icon";

type OrientationType = "vertical" | "horizontal";

type ItemStyleType = {
  wrapper: StackProps;
  title: TextProps;
  description: TextProps;
};

export interface DetailItemProps extends Omit<StackProps, "title"> {
  title: ReactNode;
  description: ReactNode;
  orientation?: OrientationType;
  _styles?: Partial<ItemStyleType>;
  loading?: boolean;
}

export interface DetailsProps extends StackProps {
  orientation?: OrientationType;
  _itemStyles?: Partial<ItemStyleType>;
  items?: { title: ReactNode; description: ReactNode }[];
  loading?: boolean;
}

export function Details(props: DetailsProps) {
  const { items, children, orientation, loading = false, onClick, _itemStyles, ...xprops } = props;

  const clickable = !!onClick;
  const as = clickable ? "button" : undefined;

  return (
    <Stack
      as={as}
      p="12px"
      border="1px solid"
      borderColor="stroke.divider"
      rounded="8px"
      gap={{ sm: "20px", md: "8px" }}
      data-clickable={clickable}
      focusVisibleRing="outside"
      focusRing="outside"
      focusRingColor="primary"
      pos="relative"
      transition="all .2s ease-in"
      css={{
        "&[data-clickable=true]": {
          cursor: "pointer",
          _hover: {
            bg: "primary.50",
            borderColor: "primary",
            transition: "all .2s ease-in",

            "& .icon": {
              transform: "translate(2px, -50%)",
              transition: "all .2s ease-in",
              "& *": { color: "primary" },
            },
          },

          "& .icon": {
            transform: "translate(0px, -50%)",
            transition: "all .2s ease-in",
          },
        },
      }}
      onClick={loading ? undefined : onClick}
      {...xprops}
    >
      {!!items && (
        <For each={items}>
          {(item, i) => <DetailItem key={`item-${i}`} orientation={orientation} _styles={_itemStyles} loading={loading} {...item} />}
        </For>
      )}

      {!items && children}

      {clickable && (
        <Skeleton variant="shine" loading={loading}>
          <Span className="icon" pos="absolute" transform=" translateY(-50%)" top="50%" right="12px">
            <Icon name="chevron_down" transform="rotate(-90deg)" boxSize="24px" />
          </Span>
        </Skeleton>
      )}
    </Stack>
  );
}

export function DetailItem(props: DetailItemProps) {
  const { title, description, orientation = "horizontal", _styles, loading, ...xprops } = props;

  const styles = useMemo(() => {
    const map: Record<typeof orientation, ItemStyleType> = {
      horizontal: {
        wrapper: { flexDir: "row", ..._styles?.wrapper },
        title: { ..._styles?.title },
        description: { ..._styles?.description },
      },
      vertical: {
        wrapper: {
          flexDir: "column",
          alignItems: "flex-start",
          ..._styles?.wrapper,
        },
        title: { ..._styles?.title },
        description: { ..._styles?.description },
      },
    };

    return map[orientation];
  }, [orientation, _styles]);

  return (
    <HStack justifyContent="space-between" {...styles.wrapper} {...xprops}>
      <Show when={!isValidElement(title)}>
        <Skeleton loading={loading}>
          <Text fontSize="12px" fontWeight="400" color="text.2" {...styles.title}>
            {title}
          </Text>
        </Skeleton>
      </Show>

      <Show when={isValidElement(title)}>
        <Skeleton loading={loading}>{title}</Skeleton>
      </Show>

      <Show when={isValidElement(description)}>
        <Skeleton loading={loading}>{description}</Skeleton>
      </Show>

      <Show when={!isValidElement(description)}>
        <Skeleton loading={loading}>
          <Text fontSize="12px" fontWeight="500" color="text" {...styles.description}>
            {description}
          </Text>
        </Skeleton>
      </Show>
    </HStack>
  );
}
