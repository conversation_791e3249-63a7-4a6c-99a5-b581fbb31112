/* eslint-disable @typescript-eslint/no-explicit-any */
import { Breadcrumb as ChakraBreadcrumb, Show, Span, type SystemStyleObject } from "@chakra-ui/react";
import * as React from "react";
import { Link, LinkProps } from "react-router";
import { Icon } from "./icon";

export interface BreadcrumbProps extends ChakraBreadcrumb.RootProps {
  separator?: React.ReactNode;
  separatorGap?: SystemStyleObject["gap"];
  items: Array<{
    title: React.ReactNode;
    to?: LinkProps["to"];
    is_first?: boolean;
  }>;
}

export const Breadcrumb = React.forwardRef<HTMLDivElement, BreadcrumbProps>(function BreadcrumbRoot(props, ref) {
  const { separator, separatorGap, items, ...rest } = props;

  // const history = useLocation();
  // console.log("History", history);

  return (
    <ChakraBreadcrumb.Root ref={ref} {...rest}>
      <ChakraBreadcrumb.List gap={separatorGap}>
        {items.map((item, index) => {
          const last = index === items.length - 1;
          return (
            <React.Fragment key={index}>
              <Show when={!last}>
                <ChakraBreadcrumb.Item viewTransitionName={`breadcrumb-item-${index}`}>
                  <ChakraBreadcrumb.Link
                    asChild
                    color="text"
                    focusRingColor="primary"
                    _hover={{
                      color: "primary",
                      "& :where(svg)": { color: "primary" },
                    }}
                  >
                    <Link to={item?.to || "#"} viewTransition>
                      {!!item?.is_first && <Icon name="arrow_left" color="text" />}
                      <Span maxW={{ sm: "20svw", "2sm": "100%" } as any} truncate>
                        {item.title}
                      </Span>
                    </Link>
                  </ChakraBreadcrumb.Link>
                </ChakraBreadcrumb.Item>
              </Show>
              <Show
                when={last}
                fallback={
                  <ChakraBreadcrumb.Separator viewTransitionName={`breadcrumb-separator-${index}`}>{separator}</ChakraBreadcrumb.Separator>
                }
              >
                <ChakraBreadcrumb.Item viewTransitionName={`breadcrumb-item-${index}`}>
                  <ChakraBreadcrumb.CurrentLink maxW={{ sm: "20svw", "1sm": "100%" } as any} truncate>
                    {item.title}
                  </ChakraBreadcrumb.CurrentLink>
                </ChakraBreadcrumb.Item>
              </Show>
            </React.Fragment>
          );
        })}
      </ChakraBreadcrumb.List>
    </ChakraBreadcrumb.Root>
  );
});
