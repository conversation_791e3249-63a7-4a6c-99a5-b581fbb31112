import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Skeleton, StackProps, Text } from "@chakra-ui/react";
import Button from "./button";
import { useListFilter } from "@/hooks";
import { FilterTag } from "./filter-tag";
import { IListFilter } from "@/interfaces";
import { MapForUnion, MapKeyLiteral, mapField, sanitizeKey } from "@/utils";

type UseListFilterType<T> = ReturnType<typeof useListFilter<T extends IListFilter ? T : IListFilter>>;

interface AppliedListFiltersProps<T> extends UseListFilterType<T>, Omit<StackProps, "filter"> {
  loading?: boolean;

  /**
   * The key path to use for mapping the filter values
   */
  keyPath?: MapForUnion;

  /**
   * List of keys to omit from the filter list
   */
  omitList?: (keyof T | string)[];

  /**
   * List of provider tiers to use for mapping the tier_id filter value
   */
  tiers?: { id: string; name: string }[];

  /**
   * A mapping of filter keys to their display names
   */
  keyMap?: Record<keyof T | string, string>;
}

export function AppliedListFilters<T>(props: AppliedListFiltersProps<T>) {
  const { filter, clearFilter, keyPath, loading = false, omitList, tiers, keyMap, ...xprops } = props;

  // const { range, ...rest } = raw;
  // const filter = { ...rest, ...omit(range, ["value"]) };
  // console.log("Applied filters", filter);

  const map_keyvalue = (key: string, value: string | number | object) => {
    // console.log("mapp keys", key, value);

    if (key === "range") {
      const v = value as NonNullable<IListFilter["range"]>;
      return mapField(v, "range");
    }

    if (!keyPath) return value;

    if (key == "tier_id" && tiers) {
      const tier = tiers.find((t) => t.id == value);
      return tier?.name;
    }
    const value_key = key == "status" ? keyPath : (`${keyPath}-${key}` as MapKeyLiteral);
    return mapField(value, value_key);
  };

  const map_key = (key: string): string => {
    // Map of keys to their display names
    const map: Record<string, string> = {
      tier_id: "Tier",
      range: "Date",
      ...keyMap,
    };

    return map[key] || sanitizeKey(key, " ");
  };

  const entries = Object.entries(filter)
    .filter(([key, value]) => !!value && !["page", "item_per_page", ...(omitList ?? [])].includes(key))
    .map(([key, value]) => [key, map_keyvalue(key, value)] as [string, string]);

  return (
    <Show when={entries.length > 0}>
      <HStack
        fontSize="14px"
        color="text.3"
        alignItems={{ base: "flex-start", md: "center" }}
        flexDir={{ base: "column", md: "row" }}
        {...xprops}
      >
        <Skeleton variant="shine" loading={loading}>
          <Text>Filters applied:</Text>
        </Skeleton>

        <HStack alignItems="center" flexWrap="wrap">
          {entries.map(([key, value]) => (
            <Skeleton key={key} variant="shine" loading={loading}>
              <FilterTag name={map_key(key)} value={value} onClose={() => clearFilter(key)} />
            </Skeleton>
          ))}
        </HStack>

        <Skeleton w="fit-content" h={loading ? "24px" : "auto"} variant="shine" loading={loading}>
          <Button
            variant="plain"
            color="text"
            size="sm"
            px={{ base: "4px", md: "12px" }}
            textDecoration="underline"
            textDecorationColor="text.3"
            _hover={{ textDecor: "none", "& > *": { color: "white" } }}
            onClick={() => clearFilter("all")}
          >
            Clear all filters
          </Button>
        </Skeleton>
      </HStack>
    </Show>
  );
}
