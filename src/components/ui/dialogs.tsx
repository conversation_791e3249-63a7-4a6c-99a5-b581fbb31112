import { createOverlay, Dialog, DialogRootProps, Portal, VStack } from "@chakra-ui/react";
import Button from "./button";
import { FancyCheckMark } from "./icons";

interface SuccessDialogProps extends Omit<DialogRootProps, "children"> {
  title: string;
  description: string;
  proceedBtnText?: string;
  onProceed?: () => void;
  loading?: boolean;
  closeOnConfirm?: boolean;
}

export const success_dialog = createOverlay<SuccessDialogProps>((props) => {
  const { title, description, proceedBtnText, onProceed, loading = false, ...rest } = props;
  return (
    <Dialog.Root {...rest}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content rounded="16px" p="24px" maxW="400px" gap="24px" bg="white">
            <VStack>
              <FancyCheckMark />

              <Dialog.Header p="0" justifyContent="space-between">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%" textAlign="center">
                  {title}
                </Dialog.Title>

                {/* <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close tier modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger> */}
              </Dialog.Header>

              <Dialog.Description fontSize="14px" color="text" textAlign="center">
                {description ?? "This action would suspend name [provider first name] from this platform"}
              </Dialog.Description>
            </VStack>

            {/* <Dialog.Body p="0">
              <Text fontSize="14px" color="text">
                {description ??
                  "This action would suspend name [provider first name] from this platform"}
              </Text>
            </Dialog.Body> */}

            <Dialog.Footer p="0">
              {/* <Dialog.ActionTrigger asChild>
                <Button w="50%" size="md" variant="subtle" disabled={loading}>
                  Cancel
                </Button>
              </Dialog.ActionTrigger> */}

              <Button w="50%" size="md" onClick={onProceed} loading={loading} disabled={loading}>
                {proceedBtnText ?? "Continue"}
              </Button>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
});
