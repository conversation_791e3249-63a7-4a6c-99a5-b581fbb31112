import { <PERSON><PERSON>, <PERSON>, LinkP<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useR<PERSON>ip<PERSON> } from "@chakra-ui/react";
import { NavLink as RouterLink } from "react-router";
import { navItemRecipe } from "@/system";
import { Icon, IconNames } from "./icon";

type RecipeArg = NonNullable<Parameters<ReturnType<typeof useRecipe<{ key: RecipeKey; recipe: typeof navItemRecipe }>>>[0]>;
type ExtractStringUnion<T> = T extends string ? T : never;
type Variants = ExtractStringUnion<NonNullable<RecipeArg["variant"]>>;

interface TopNavItemProps extends Omit<LinkProps, "variant"> {
  variant?: Variants;
  to?: string;
  leftIcon?: IconNames;
  prefetch?: "intent" | "viewport";
}

export function TopNavItem(props: TopNavItemProps) {
  const { variant = "solid", to = "/", leftIcon, children, prefetch, ...xprops } = props;

  const recipe = useRecipe<{ recipe: typeof navItemRecipe }>({
    recipe: navItemRecipe,
  });
  const styles = recipe({ variant });

  return (
    // <Link as={RouterLink} {...{ to, viewTransition: true }} {...styles} {...xprops}>
    <Link asChild {...styles} {...xprops}>
      <RouterLink to={to} viewTransition prefetch={prefetch}>
        {leftIcon && <Icon name={leftIcon} />}
        {children}
      </RouterLink>
    </Link>
  );
}

/**
 * Mobile nav item is intended to be used within the Drawer.Root component
 */
export function MobileTopNavItem(props: TopNavItemProps) {
  return (
    <Drawer.ActionTrigger asChild>
      <TopNavItem
        rounded="8px"
        py="16px"
        alignItems="center"
        justifyContent="center"
        bg="transparent"
        color="white"
        borderColor="menu.stroke"
        css={{
          "&.active": {
            bg: "primary",
            color: "white",
          },
        }}
        {...props}
        prefetch="viewport"
      />
    </Drawer.ActionTrigger>
  );
}
