/* eslint-disable @typescript-eslint/no-explicit-any */
import { BoxProps, chakra, ChakraComponent } from "@chakra-ui/react";

export function Mail(props: BoxProps) {
  return (
    <chakra.svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg" {...(props as any)}>
      <path
        d="M14.6663 4.82129L8.68634 8.62129C8.48052 8.75024 8.24255 8.81863 7.99967 8.81863C7.7568 8.81863 7.51883 8.75024 7.31301 8.62129L1.33301 4.82129M2.66634 2.82129H13.333C14.0694 2.82129 14.6663 3.41824 14.6663 4.15462V12.1546C14.6663 12.891 14.0694 13.488 13.333 13.488H2.66634C1.92996 13.488 1.33301 12.891 1.33301 12.1546V4.15462C1.33301 3.41824 1.92996 2.82129 2.66634 2.82129Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </chakra.svg>
  );
}
export function Visible(props: BoxProps) {
  return (
    <chakra.svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg" {...(props as any)}>
      <path
        d="M1.37468 8.38654C1.31912 8.23686 1.31912 8.07221 1.37468 7.92254C1.91581 6.61044 2.83435 5.48856 4.01386 4.69914C5.19336 3.90971 6.58071 3.48828 8.00001 3.48828C9.41932 3.48828 10.8067 3.90971 11.9862 4.69914C13.1657 5.48856 14.0842 6.61044 14.6253 7.92254C14.6809 8.07221 14.6809 8.23686 14.6253 8.38654C14.0842 9.69863 13.1657 10.8205 11.9862 11.6099C10.8067 12.3994 9.41932 12.8208 8.00001 12.8208C6.58071 12.8208 5.19336 12.3994 4.01386 11.6099C2.83435 10.8205 1.91581 9.69863 1.37468 8.38654Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.00001 10.1545C9.10458 10.1545 10 9.25911 10 8.15454C10 7.04997 9.10458 6.15454 8.00001 6.15454C6.89544 6.15454 6.00001 7.04997 6.00001 8.15454C6.00001 9.25911 6.89544 10.1545 8.00001 10.1545Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </chakra.svg>
  );
}

export function NotVisible(props: BoxProps) {
  return (
    <chakra.svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" {...(props as any)}>
      <path
        d="M10.733 5.80702C13.0624 5.52942 15.4186 6.02184 17.4419 7.20907C19.4651 8.3963 21.0442 10.2131 21.938 12.382C22.0214 12.6065 22.0214 12.8535 21.938 13.078C21.5705 13.969 21.0848 14.8065 20.494 15.568M14.084 14.889C13.5182 15.4355 12.7604 15.7379 11.9738 15.731C11.1872 15.7242 10.4348 15.4087 9.87856 14.8525C9.32234 14.2962 9.00683 13.5438 9 12.7572C8.99316 11.9706 9.29554 11.2128 9.84202 10.647M17.479 18.23C16.1525 19.0158 14.6725 19.507 13.1394 19.6704C11.6063 19.8338 10.056 19.6655 8.59365 19.1769C7.13133 18.6883 5.79121 17.8909 4.66423 16.8388C3.53725 15.7866 2.64977 14.5044 2.06202 13.079C1.97868 12.8545 1.97868 12.6075 2.06202 12.383C2.94865 10.2329 4.50869 8.42826 6.50802 7.24002M2.00002 2.73102L22 22.731"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </chakra.svg>
  );
}

export function TextEdit(props: BoxProps) {
  return (
    <chakra.svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg" {...(props as any)}>
      <path
        d="M2 10.6546L4.66667 5.32129L7.33333 10.6546M2.66667 9.32129H6.66667M14 6.65462V10.6546M14 8.65462C14 9.75919 13.1046 10.6546 12 10.6546C10.8954 10.6546 10 9.75919 10 8.65462C10 7.55005 10.8954 6.65462 12 6.65462C13.1046 6.65462 14 7.55005 14 8.65462Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </chakra.svg>
  );
}

export function HouseAddress(props: BoxProps) {
  return (
    <chakra.svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg" {...(props as any)}>
      <chakra.g clipPath="url(#clip0_19182_11242)">
        <chakra.path
          d="M12.0002 7.32113C12.0002 5.90664 11.4383 4.55008 10.4381 3.54989C9.43787 2.5497 8.08132 1.98779 6.66683 1.98779C5.25234 1.98779 3.89579 2.5497 2.89559 3.54989C1.8954 4.55008 1.3335 5.90664 1.3335 7.32113C1.3335 10.6498 5.02616 14.1165 6.26616 15.1871C6.38174 15.2738 6.52235 15.3206 6.66683 15.3205M12.0002 15.3211V13.3211M10.0002 15.3211C9.82335 15.3211 9.65378 15.2509 9.52876 15.1259C9.40373 15.0008 9.3335 14.8313 9.3335 14.6545V11.9878C9.33348 11.8781 9.36055 11.77 9.4123 11.6733C9.46405 11.5765 9.53889 11.494 9.63016 11.4331L11.6302 10.0998C11.7397 10.0267 11.8685 9.98769 12.0002 9.98769C12.1319 9.98769 12.2606 10.0267 12.3702 10.0998L14.3702 11.4331C14.4614 11.494 14.5363 11.5765 14.588 11.6733C14.6398 11.77 14.6668 11.8781 14.6668 11.9878V14.6545C14.6668 14.8313 14.5966 15.0008 14.4716 15.1259C14.3465 15.2509 14.177 15.3211 14.0002 15.3211H10.0002ZM8.66683 7.32113C8.66683 8.4257 7.7714 9.32113 6.66683 9.32113C5.56226 9.32113 4.66683 8.4257 4.66683 7.32113C4.66683 6.21656 5.56226 5.32113 6.66683 5.32113C7.7714 5.32113 8.66683 6.21656 8.66683 7.32113Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </chakra.g>

      <chakra.defs>
        <chakra.clipPath id="clip0_19182_11242">
          <rect width="16" height="16" fill="white" transform="translate(0 0.654541)" />
        </chakra.clipPath>
      </chakra.defs>
    </chakra.svg>
  );
}

export function FancyCheckMark() {
  return (
    <svg
      width="92"
      height="93"
      viewBox="0 0 92 93"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <rect x="2.32886" y="2.77258" width="87.3418" height="87.3418" fill="url(#pattern0_14029_11173)" />
      <defs>
        <pattern id="pattern0_14029_11173" patternContentUnits="objectBoundingBox" width="1" height="1">
          <use xlinkHref="#image0_14029_11173" transform="scale(0.00666667)" />
        </pattern>
        <image
          id="image0_14029_11173"
          width="150"
          height="150"
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
}

export function BlueFancyCheckMark() {}

export function CalendarIllustration(props: ChakraComponent<"svg", BoxProps>) {
  return (
    <chakra.svg width="106" height="80" viewBox="0 0 106 80" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g opacity="0.5">
        <path
          d="M58.8311 1.87602C57.4027 1.7736 55.9738 1.67951 54.5445 1.59375C53.5208 2.0731 52.4367 3.11598 52.0713 4.21015C52.0618 4.23873 52.055 4.26811 52.0468 4.29701C52.0508 4.31343 52.0554 4.32948 52.0583 4.34602C52.163 4.92743 51.3776 5.43503 52.0571 5.88626C52.7922 6.37431 54.1659 6.2399 55.0229 6.19032C55.1364 5.80343 55.6138 4.78754 55.9465 4.56436C56.6709 3.3796 57.5894 2.49815 58.8311 1.87602Z"
          fill="#5388F6"
        />
        <path
          d="M39.7314 10.0782C39.6388 9.58389 39.6756 9.00761 39.6477 8.5009C39.5796 7.27053 39.4523 6.04955 39.1151 4.86092C38.7142 3.45436 38.0096 1.87852 36.6697 1.15161C35.868 0.716679 31.6417 0.398032 30.7853 0.672499C29.6643 1.03216 28.7576 2.42912 28.2518 3.44254C28.0162 3.91339 27.5215 4.92872 27.689 5.43766L27.5271 6.15974L27.593 6.10369L27.555 6.25354C28.9005 6.53512 30.3599 6.26179 31.6898 5.99716C31.6853 5.98196 31.6797 5.96725 31.6763 5.95184C31.6283 5.69737 31.7221 5.41969 31.8639 5.21221L32.0202 5.26972C32.0816 4.93932 32.3128 4.52374 32.4658 4.21867C32.7338 3.68571 32.9783 3.19262 33.5589 2.99911C33.5232 2.68981 33.5667 2.60263 33.7543 2.3543C34.0257 1.99484 34.5795 1.82459 35.0083 1.77825C35.7039 1.7029 36.5559 1.8619 37.0997 2.3304C38.3614 3.41819 39.0738 6.06662 39.2882 7.65409C39.3775 8.31788 39.3786 9.2863 39.6634 9.88336L39.7314 10.0782Z"
          fill="#5388F6"
        />
        <path
          d="M39.6641 9.88471C39.3794 9.28765 39.3782 8.31927 39.2889 7.65543C39.0745 6.06797 38.3621 3.41954 37.1004 2.33175C36.5566 1.86325 35.7046 1.70421 35.009 1.7796C34.5802 1.82594 34.0264 1.99623 33.755 2.35565C33.5675 2.60398 33.5239 2.69116 33.5596 3.00046C33.6445 3.67267 34.0732 4.45303 34.2575 5.13235C34.4641 5.88962 34.5444 7.22392 34.8671 7.86418C34.7767 8.41287 34.7197 9.46258 34.9955 9.94685C35.6577 9.97754 39.2352 10.2875 39.6004 9.95323C39.6239 9.93218 39.6428 9.90751 39.6641 9.88471Z"
          fill="url(#paint0_linear_21253_13995)"
        />
        <path
          d="M85.8574 12.611C85.8519 10.041 85.8178 6.75756 84.2641 4.57015C83.7273 3.81454 83.113 3.40119 82.2022 3.25C81.0802 3.96204 79.7747 5.02012 79.4479 6.38559C79.3946 6.60766 79.4009 6.67164 79.5079 6.86762L80.0601 6.07271C80.6044 7.43363 80.9095 8.71118 80.997 10.1755C81.2074 10.7062 81.082 11.6705 81.0605 12.2448C82.3928 12.3479 83.7245 12.4579 85.0556 12.5747C85.3124 12.6174 85.5961 12.603 85.8574 12.611Z"
          fill="url(#paint1_linear_21253_13995)"
        />
        <path
          d="M85.8574 12.611C85.8519 10.041 85.8178 6.75756 84.2641 4.57015C83.7273 3.81454 83.113 3.40119 82.2022 3.25C81.0802 3.96204 79.7747 5.02012 79.4479 6.38559C79.3946 6.60766 79.4009 6.67164 79.5079 6.86762L80.0601 6.07271C80.567 5.50939 81.1971 4.73862 82.0242 4.72411C82.4888 4.71594 82.8705 4.94497 83.1878 5.26609C84.8765 6.97504 85.0632 10.3241 85.0556 12.5747C85.3124 12.6174 85.5961 12.603 85.8574 12.611Z"
          fill="#2A5CF8"
        />
        <path
          d="M62.4585 11.068C62.7596 8.56505 62.5668 5.15443 60.946 3.07936C60.3598 2.32899 59.7629 1.99872 58.8301 1.87891C57.5885 2.50108 56.67 3.38248 55.9456 4.56721L56.4525 4.51116L56.6088 4.52177C57.745 6.4797 57.8519 8.81883 57.8547 11.0267L61.7354 11.2625C61.9299 11.2903 62.1071 11.2949 62.3031 11.2837L62.4585 11.068Z"
          fill="url(#paint2_linear_21253_13995)"
        />
        <path
          d="M62.4585 11.068C62.7596 8.56505 62.5668 5.15443 60.946 3.07936C60.3598 2.32899 59.7629 1.99872 58.8301 1.87891C57.5885 2.50108 56.67 3.38248 55.9456 4.56721L56.4525 4.51116L56.6088 4.52177C56.887 3.953 57.3768 3.48746 57.9865 3.28872C58.4997 3.12147 59.12 3.18946 59.5949 3.44024C60.5514 3.94515 61.07 5.1361 61.3612 6.12305C61.8224 7.6865 61.9761 9.65179 61.7354 11.2625C61.9299 11.2903 62.1071 11.2949 62.3032 11.2837L62.4585 11.068Z"
          fill="#2A5CF8"
        />
        <path
          d="M99.397 15.6094C99.183 15.6769 99.1584 15.6702 99.0486 15.8787C98.6449 16.6436 98.0588 19.2557 97.7974 20.2301C97.3448 21.8811 96.8748 23.5271 96.3873 25.1682C94.5014 31.6623 92.9026 38.2341 91.1675 44.7678C89.4475 51.2377 87.6599 57.6887 85.8045 64.1209C85.1928 66.2796 84.6116 68.4465 84.0611 70.6216C83.5864 72.4743 83.2139 74.6285 82.3782 76.3385C82.0568 76.9962 81.6404 77.3625 80.955 77.609C80.5588 77.7514 80.1486 77.7909 79.7296 77.7694C77.7238 77.6665 75.6705 77.2945 73.6667 77.0758L62.036 75.8904L28.7796 72.6931L8.64414 70.7486C7.01615 70.6008 5.38816 70.3849 3.76017 70.2575C3.57037 70.2427 3.39949 70.2047 3.23314 70.3023C3.32133 70.7836 3.76463 71.2402 4.15766 71.5098C4.56971 71.7933 4.99623 71.9436 5.4864 72.0297C6.86427 72.2719 8.32477 72.3127 9.72052 72.4374C11.6433 72.6094 13.5616 72.8388 15.4833 73.0169L21.8992 73.6189C23.1197 73.7419 24.3646 73.9331 25.5884 73.994L62.7377 77.5593L75.8765 78.8761C77.7014 79.0518 79.5352 79.289 81.3643 79.4015C82.5942 79.4771 84.0338 78.3675 84.9387 77.5925C86.1921 76.5193 87.2628 75.1865 88.3612 73.9556L93.2797 68.4776C94.0652 67.6081 94.8472 66.6329 95.7305 65.8664C95.7472 65.8521 95.7642 65.8379 95.7811 65.8236C96.5035 64.9204 97.3763 64.0787 98.1625 63.2269L102.721 58.3119C103.487 57.5013 105.026 56.1799 105.497 55.2059C106.67 52.7784 101.759 30.4093 100.906 26.1789C100.531 24.2641 100.174 22.3462 99.8334 20.4251C99.6697 19.5365 99.4321 18.6326 99.3243 17.7365C99.2416 17.0511 99.4024 16.306 99.397 15.6094Z"
          fill="url(#paint3_linear_21253_13995)"
        />
        <path
          d="M98.9754 19.9414C98.5629 20.6419 97.9299 24.4366 97.649 25.4879C95.2241 34.5654 92.644 43.6013 90.2854 52.6962C92.6287 53.1411 95.0551 53.3256 97.4222 53.6277C100.001 53.9003 102.596 54.4683 105.187 54.5628C104.963 49.4612 103.685 43.9127 102.747 38.8825L100.478 27.3519C99.9882 24.8834 99.5641 22.3885 98.9754 19.9414Z"
          fill="#0E217D"
        />
        <path
          d="M99.3975 15.6071C99.3151 15.0373 99.124 14.4378 98.7331 14.0024C98.1078 13.3056 97.2272 13.2384 96.3557 13.1953C95.1734 18.1719 93.5196 22.9843 92.0348 27.8742C91.6852 29.0255 91.2197 30.1755 90.9186 31.3349L90.8371 31.3215C88.8156 38.1828 86.1418 44.8436 83.7322 51.5727L80.8682 59.6815C80.1457 61.683 79.4784 63.9662 78.2687 65.7314C77.475 66.1923 76.9862 66.5348 76.0252 66.5276C74.8432 66.5185 73.636 66.3371 72.46 66.2223L67.4271 65.7751L47.5817 63.8791L24.4052 61.7876L8.73045 60.4377C6.09082 60.2097 3.44341 60.054 0.809357 59.7705C0.628456 60.0497 0.646332 60.09 0.715546 60.4106C0.847275 61.017 1.82655 64.4653 2.17939 64.6982C2.5501 64.9427 3.27029 64.8592 3.70242 64.8537C3.41544 65.1625 2.94202 65.7374 2.96881 66.1795C2.96995 66.1982 2.97552 66.2165 2.97889 66.2351C2.58025 66.5933 2.49315 67.2584 2.4686 67.769C2.43397 68.5 2.57021 69.6313 3.09501 70.1899C3.14638 70.2446 3.16312 70.2501 3.23343 70.3001C3.39983 70.2025 3.57066 70.2405 3.76048 70.2553C5.38847 70.3827 7.01646 70.5985 8.64445 70.7464L28.7799 72.6909L62.0363 75.8882L73.667 77.0735C75.6708 77.2923 77.7241 77.6643 79.7299 77.7672C80.1489 77.7887 80.5591 77.7492 80.9553 77.6068C81.6407 77.3603 82.0571 76.994 82.3785 76.3363C83.2142 74.6263 83.5867 72.4721 84.0614 70.6193C84.6119 68.4443 85.1931 66.2774 85.8048 64.1187C87.6601 57.6865 89.4478 51.2354 91.1678 44.7656C92.9029 38.2319 94.5017 31.6601 96.3876 25.166C96.8751 23.5249 97.3451 21.8789 97.7977 20.2279C98.0591 19.2535 98.6452 16.6414 99.0489 15.8765C99.1589 15.668 99.1834 15.6747 99.3975 15.6071Z"
          fill="#007BFF"
        />
        <path
          d="M80.095 71.6313C60.3646 69.5987 40.6158 67.7652 20.8487 66.131C16.9257 65.7473 12.9998 65.3978 9.07087 65.0824C7.91187 64.9779 4.80325 64.4826 3.82402 64.6714L3.70234 64.8563C3.41535 65.1651 2.94193 65.74 2.96871 66.182C2.96985 66.2008 2.97542 66.2191 2.97879 66.2376L3.06589 66.3983C10.4644 66.8723 17.8641 67.6161 25.2503 68.2552C34.594 68.9958 43.9293 69.8306 53.2562 70.7596L71.8136 72.6523C74.2647 72.8928 76.7333 73.2623 79.1897 73.4065C79.8893 73.4474 80.5807 73.5235 81.1474 73.0339C81.9636 72.3286 82.5967 70.5712 82.6941 69.5044C82.7049 69.3865 82.6871 69.2973 82.6554 69.1857L82.6364 69.1162C82.6211 69.0625 82.6026 69.01 82.5841 68.9575C82.2594 69.7484 81.8309 71.2032 80.9673 71.5722C80.6873 71.6918 80.3875 71.7089 80.095 71.6313Z"
          fill="#1C4BF1"
        />
        <path
          d="M90.9189 31.3337L90.8374 31.3203C88.816 38.1816 86.1422 44.8424 83.7325 51.5715L80.8685 59.6803C80.146 61.6818 79.4787 63.965 78.2691 65.7302C77.9131 66.9536 77.7736 68.2387 78.4142 69.3972C78.7362 69.9795 80.0519 71.1232 80.0954 71.6275C80.388 71.7051 80.6878 71.688 80.9676 71.5684C81.8313 71.1994 82.2597 69.7446 82.5844 68.9537L92.7588 35.3559C92.2813 33.975 91.5311 32.6612 90.9189 31.3337Z"
          fill="#B9C0D3"
        />
        <path
          d="M0.80899 59.7734C0.628082 60.0527 0.645966 60.093 0.715179 60.4136C0.846909 61.02 1.82619 64.4682 2.17902 64.7012C2.54973 64.9457 3.26994 64.8622 3.70206 64.8567L3.82375 64.6718C4.80299 64.483 7.91161 64.9783 9.07061 65.0828C12.9995 65.3982 16.9255 65.7478 20.8484 66.1314C40.6156 67.7656 60.3644 69.5991 80.0947 71.6317C80.0512 71.1274 78.7355 69.9837 78.4135 69.4014C77.7729 68.2429 77.9123 66.9578 78.2683 65.7345C77.4746 66.1953 76.9858 66.5378 76.0248 66.5306C74.8428 66.5215 73.6356 66.3401 72.4596 66.2253L67.4267 65.7781L47.5813 63.8822L24.4048 61.7907L8.73004 60.4407C6.09046 60.2127 3.44304 60.057 0.80899 59.7734Z"
          fill="#CCD6F6"
        />
        <path
          d="M82.2021 3.24779C80.7325 3.12208 79.2611 3.02385 77.7878 2.95312C76.1602 3.86416 75.3838 5.48052 74.8955 7.20845C74.4601 8.74934 74.3377 10.37 74.3048 11.9651C71.8729 11.905 69.4305 11.7166 67.0007 11.5853C65.5749 11.5081 63.9631 11.5664 62.5749 11.274L62.4587 11.0665L62.3034 11.2822C62.1073 11.2934 61.9301 11.2888 61.7356 11.261L57.855 11.0252C57.8521 8.81737 57.7453 6.47824 56.609 4.52027L56.4528 4.50966L55.9459 4.56571C55.6131 4.78892 55.1358 5.80477 55.0222 6.19166C54.1653 6.24125 52.7915 6.37566 52.0565 5.88761C51.3769 5.43638 52.1623 4.92878 52.0577 4.34737C52.0548 4.33083 52.0501 4.31477 52.0462 4.29835C51.0071 6.36147 50.7363 8.47873 50.6042 10.7575C48.2031 10.57 45.7886 10.4723 43.383 10.3526C42.2614 10.2968 40.802 10.3851 39.7323 10.0786L39.6642 9.88374C39.643 9.9065 39.624 9.93122 39.6005 9.95231C39.2354 10.2866 35.6578 9.97666 34.9957 9.94593C34.7199 9.46166 34.7768 8.41195 34.8673 7.86326C34.5446 7.223 34.4642 5.8887 34.2577 5.13143C34.0734 4.45211 33.6446 3.67171 33.5598 2.99954C32.9792 3.19305 32.7346 3.68614 32.4666 4.2191C32.3137 4.52413 32.0825 4.93975 32.0211 5.27015L31.8648 5.21263C31.723 5.42008 31.6292 5.6978 31.6772 5.95227C31.6806 5.96767 31.6861 5.98243 31.6906 5.99759C30.3608 6.26222 28.9014 6.53555 27.5559 6.25397L27.5939 6.10412L27.528 6.16016L27.6899 5.43809C27.1149 6.09038 27.0277 8.1246 27.0914 8.97109C27.1037 9.14728 27.1282 9.3027 27.1963 9.46585C27.174 9.47267 27.1517 9.48003 27.1294 9.48617C24.789 10.1147 20.3651 7.94585 18.641 10.7577C18.2647 11.3723 13.1898 26.1076 13.2055 26.4194L13.2914 26.5811C19.5252 27.2992 23.4776 27.013 30.2045 27.3567C33.319 27.5162 36.4321 27.6959 39.5436 27.8957L74.7399 30.1471L85.4619 30.8801C87.2027 31.0013 89.1363 30.984 90.8368 31.3224L90.9183 31.3358C91.2193 30.1764 91.6849 29.0264 92.0345 27.8751C93.5193 22.9852 95.1731 18.1728 96.3555 13.1962C94.7916 12.9805 93.1244 12.9909 91.5445 12.8976C89.6717 12.787 87.7303 12.5618 85.8574 12.6089C85.5961 12.6009 85.3124 12.6153 85.0556 12.5726C83.7244 12.4557 82.3927 12.3457 81.0605 12.2427C81.0819 11.6684 81.2074 10.7041 80.997 10.1734C80.9094 8.70914 80.6044 7.43154 80.0601 6.07063L79.5079 6.86554C79.4008 6.66959 79.3946 6.60558 79.4478 6.3835C79.7747 5.01791 81.0802 3.95983 82.2021 3.24779Z"
          fill="url(#paint4_linear_21253_13995)"
        />
        <path
          d="M27.0034 15.3908C26.9777 15.4065 26.951 15.4209 26.9253 15.4376C26.568 15.6741 26.319 16.3673 26.2419 16.7687C26.0822 17.5892 26.2408 18.5515 26.7187 19.2444C27.1564 19.8791 27.8465 20.2376 28.5935 20.3735C28.6597 20.3854 28.726 20.3956 28.7922 20.4041C28.8592 20.4127 28.9262 20.4195 28.9932 20.4246C29.0602 20.4297 29.1272 20.4332 29.1942 20.435C29.2612 20.4368 29.3281 20.4369 29.3952 20.4352C29.4621 20.4337 29.5292 20.4304 29.5962 20.4254C29.6631 20.4204 29.7301 20.4137 29.7971 20.4053C29.8634 20.3969 29.9296 20.3869 29.9959 20.3751C30.0621 20.3634 30.128 20.3501 30.1935 20.335C30.2591 20.32 30.3242 20.3033 30.3889 20.285C30.453 20.2666 30.517 20.2467 30.581 20.2252C30.6443 20.2036 30.7072 20.1804 30.7697 20.1557C30.8323 20.131 30.894 20.1047 30.9551 20.0768C31.0161 20.049 31.0764 20.0196 31.136 19.9887C31.1955 19.9578 31.2543 19.9254 31.3124 19.8915C31.3705 19.8577 31.4278 19.8224 31.4843 19.7856C31.5402 19.7488 31.5953 19.7106 31.6496 19.6711C32.5484 19.0235 33.2474 18.0017 33.4071 16.8944C33.5713 15.7603 33.1626 14.6935 32.4859 13.8008C32.0594 14.6093 32.4145 15.6544 32.2582 16.5205C32.1365 17.1959 31.5569 17.6175 31.3839 18.2166C30.7519 18.6964 30.2182 19.0275 29.4019 19.035C28.8101 19.0405 28.2395 18.8187 27.822 18.3941C27.0079 17.5653 27.0012 16.4728 27.0034 15.3908Z"
          fill="#0E217D"
        />
        <path
          d="M74.2821 17.2992C74.2358 17.3564 74.1903 17.4134 74.1464 17.4723C73.582 18.2318 73.2377 19.3053 73.3902 20.2512C73.5244 21.0829 74.0301 21.811 74.716 22.288C75.5184 22.8459 76.5254 23.0114 77.4784 22.8395C78.5656 22.6435 79.5797 22.0265 80.1994 21.1042C80.7695 20.2562 81.0396 19.0673 80.823 18.0613C80.6452 17.2364 80.1396 16.7182 79.4522 16.2734C79.5075 17.6402 79.8026 19.6235 78.8088 20.7169C78.7239 20.8103 78.6393 20.8886 78.5402 20.9662C78.2497 21.143 77.9119 21.2715 77.5812 21.3488C76.9142 21.5045 76.1725 21.6515 75.5594 21.256C74.3134 20.4524 74.6036 18.6321 74.3039 17.3869L74.2821 17.2992Z"
          fill="#0E217D"
        />
        <path
          d="M55.564 14.8594C55.5629 15.4709 55.5911 16.1037 55.5488 16.713C55.5576 17.2676 55.5903 17.8326 55.5633 18.3862C55.247 18.8742 54.9241 19.2787 54.5082 19.687C54.377 19.8123 54.1716 19.8919 53.9985 19.9431C53.2906 20.1518 52.2653 20.1483 51.6048 19.7811C50.2751 19.0417 50.945 17.1481 50.5912 15.9855C50.5855 15.967 50.5789 15.9488 50.5729 15.9305C49.9055 16.6958 49.4415 17.5224 49.5062 18.5702C49.5593 19.4308 49.9764 20.3366 50.6278 20.907C51.3111 21.5054 52.2508 21.7374 53.1442 21.6623C54.262 21.5681 55.2973 20.9617 56.0109 20.1088C56.6599 19.3334 57.0334 18.3391 56.9371 17.3222C56.8402 16.2976 56.3158 15.5241 55.564 14.8594Z"
          fill="#0E217D"
        />
        <path
          d="M33.5596 2.99609C32.9789 3.1896 32.7344 3.68269 32.4664 4.21565C32.3134 4.52068 32.0823 4.93631 32.0209 5.2667C31.5888 6.17615 31.4213 7.26719 31.3119 8.26194C31.1913 9.36258 30.9713 17.2385 31.1946 17.9334C31.2281 18.0391 31.3062 18.1398 31.3833 18.2163C31.5564 17.6173 32.1359 17.1956 32.2576 16.5202C32.4139 15.6542 32.0589 14.609 32.4854 13.8005C32.4374 13.7227 32.3972 13.6569 32.3759 13.5667C32.2386 12.9783 32.2285 10.2817 32.53 9.81251C33.322 9.86388 34.1145 9.91054 34.9072 9.95252C34.8659 9.88741 34.8447 9.85985 34.8168 9.79206C34.6258 9.31649 34.6604 8.32051 34.867 7.85982C34.5443 7.21955 34.464 5.88526 34.2574 5.12798C34.0732 4.4487 33.6445 3.6683 33.5596 2.99609Z"
          fill="#FEFEFE"
        />
        <path
          d="M32.0215 5.27236L31.8652 5.21484C31.7234 5.42229 31.6296 5.70001 31.6776 5.95448C31.681 5.96988 31.6865 5.98464 31.691 5.9998C30.3612 6.26443 28.9018 6.53776 27.5563 6.25618L27.5943 6.10633L27.5284 6.16237L27.6903 5.44029C27.1152 6.09259 27.0281 8.12681 27.0918 8.9733C27.1041 9.14949 27.1286 9.30491 27.1967 9.46805C26.8372 9.94708 26.9522 14.5684 27.0035 15.3962C27.0013 16.4782 27.008 17.5707 27.822 18.3995C28.2396 18.8242 28.8102 19.0459 29.4019 19.0404C30.2182 19.033 30.7519 18.7018 31.3839 18.222C31.3068 18.1455 31.2287 18.0448 31.1952 17.9391C30.9719 17.2442 31.1918 9.36827 31.3124 8.26764C31.4219 7.27284 31.5894 6.1818 32.0215 5.27236Z"
          fill="url(#paint5_linear_21253_13995)"
        />
        <path
          d="M52.0457 4.29688C51.0066 6.35999 50.7358 8.47725 50.6037 10.756C50.616 11.9603 50.8068 14.9847 50.5721 15.9272C50.5781 15.9455 50.5847 15.9637 50.5904 15.9822C50.9442 17.1449 50.2743 19.0384 51.6041 19.7778C52.2645 20.145 53.2899 20.1486 53.9977 19.9398C54.1709 19.8886 54.3762 19.809 54.5074 19.6838C54.9234 19.2754 55.2463 18.8709 55.5626 18.383C55.5895 17.8293 55.5569 17.2644 55.5481 16.7097C55.397 15.5897 55.3916 12.0771 55.6222 11.0097L55.8372 10.8978C56.5064 10.9699 57.1824 10.9898 57.8545 11.0236C57.8516 8.81581 57.7448 6.47668 56.6085 4.51871L56.4523 4.5081L55.9454 4.56415C55.6126 4.78736 55.1353 5.80321 55.0217 6.1901C54.1648 6.23969 52.791 6.3741 52.056 5.88605C51.3764 5.43482 52.1618 4.92722 52.0572 4.34581C52.0543 4.32935 52.0496 4.31325 52.0457 4.29688Z"
          fill="url(#paint6_linear_21253_13995)"
        />
        <path
          d="M54.6595 10.1133C54.3579 11.7693 54.5788 13.6755 54.555 15.3614C54.5353 16.7558 54.3182 18.3043 54.5081 19.6844C54.924 19.2761 55.2469 18.8716 55.5632 18.3836C55.5901 17.83 55.5575 17.265 55.5487 16.7104C55.3976 15.5904 55.3923 12.0778 55.6229 11.0104L55.8379 10.8985C55.8079 10.881 55.7805 10.8575 55.7478 10.8459C55.5725 10.7836 55.2804 10.8217 55.1143 10.8922C54.982 10.9484 54.9338 10.9738 54.7863 10.9315C54.6563 10.6725 54.6697 10.3971 54.6595 10.1133Z"
          fill="#CCD6F6"
        />
        <path
          d="M56.6085 4.52233L56.4522 4.51172C56.384 4.6154 56.3175 4.72022 56.2529 4.82614C56.1882 4.93214 56.1253 5.03919 56.0643 5.14727C56.0033 5.25534 55.9441 5.36443 55.8869 5.47453C55.8296 5.58463 55.7742 5.69567 55.7206 5.80773C55.6671 5.9197 55.6155 6.03253 55.5658 6.14629C55.5161 6.26009 55.4684 6.37471 55.4226 6.4901C55.3769 6.60548 55.333 6.7216 55.2911 6.83845C55.2493 6.9553 55.2095 7.07285 55.1717 7.19108C55.1339 7.30927 55.098 7.42812 55.0642 7.54761C55.0304 7.66702 54.9987 7.78696 54.969 7.90747C54.9393 8.02806 54.9116 8.14905 54.886 8.27046C54.8605 8.39194 54.837 8.51383 54.8156 8.63613C54.7941 8.75835 54.7748 8.88097 54.7575 9.00392C54.7402 9.12682 54.725 9.24997 54.7119 9.37341C54.6989 9.49688 54.688 9.62052 54.6791 9.74432C54.6702 9.86821 54.6634 9.99213 54.6588 10.1161C54.6691 10.4 54.6557 10.6753 54.7856 10.9344C54.9331 10.9767 54.9814 10.9512 55.1137 10.8951C55.2797 10.8246 55.5719 10.7864 55.7471 10.8487C55.7799 10.8604 55.8072 10.8838 55.8373 10.9013C56.5064 10.9735 57.1824 10.9933 57.8545 11.0272C57.8516 8.81939 57.7447 6.48026 56.6085 4.52233Z"
          fill="#FEFEFE"
        />
        <path
          d="M82.2021 3.24779C80.7326 3.12208 79.2611 3.02385 77.7878 2.95312C76.1603 3.86416 75.3838 5.48052 74.8955 7.20845C74.4602 8.74934 74.3377 10.37 74.3048 11.9651C74.2125 13.3407 74.198 14.7171 74.2612 16.0944C74.2803 16.4497 74.3701 16.9612 74.2824 17.2984L74.3042 17.3861C74.6039 18.6313 74.3137 20.4515 75.5597 21.2551C76.1728 21.6506 76.9144 21.5036 77.5815 21.3479C77.9122 21.2707 78.25 21.1422 78.5405 20.9654C78.6396 20.8878 78.7242 20.8094 78.8091 20.716C79.8029 19.6227 79.5078 17.6394 79.4525 16.2726C79.1974 15.1695 79.3716 13.3926 79.3715 12.2255C79.8955 12.2313 80.4299 12.2975 80.953 12.3375C81.0142 11.6192 80.996 10.8936 80.997 10.1733C80.9094 8.70906 80.6044 7.43146 80.06 6.07054L79.5079 6.86545C79.4008 6.66951 79.3946 6.6055 79.4478 6.38342C79.7747 5.01791 81.0802 3.95983 82.2021 3.24779Z"
          fill="url(#paint7_linear_21253_13995)"
        />
        <path
          d="M78.3748 11.3203C78.0873 13.0245 78.3816 14.8867 78.3157 16.6162C78.4487 18.0614 78.4257 19.518 78.5405 20.9657C78.6396 20.8881 78.7242 20.8097 78.809 20.7163C79.8029 19.623 79.5078 17.6397 79.4525 16.2729C79.1973 15.1698 79.3715 13.3929 79.3714 12.2258C79.0826 12.2408 78.8199 12.2479 78.5324 12.1986C78.3518 11.924 78.3917 11.6439 78.3748 11.3203Z"
          fill="#CCD6F6"
        />
        <path
          d="M80.0603 6.07031L79.5082 6.86522C78.8737 8.25337 78.3689 9.77414 78.375 11.3197C78.3919 11.6433 78.352 11.9235 78.5327 12.1981C78.8202 12.2474 79.0829 12.2403 79.3717 12.2253C79.8957 12.2311 80.4301 12.2973 80.9533 12.3373C81.0144 11.619 80.9963 10.8934 80.9972 10.1731C80.9097 8.70883 80.6046 7.43123 80.0603 6.07031Z"
          fill="#FEFEFE"
        />
        <path
          d="M19.3479 26.8697C17.3403 26.6937 15.2969 26.8146 13.2915 26.5836L13.2056 26.4219C12.4988 27.5563 11.9963 29.5149 11.5005 30.8123C9.41693 36.2557 7.48524 41.7615 5.44524 47.2218C4.91264 48.6491 0.685219 59.2068 0.781219 59.6645C0.789024 59.7012 0.800201 59.7373 0.809143 59.7738C3.4432 60.0573 6.09063 60.213 8.73025 60.4411L24.405 61.791L47.5815 63.8825L67.4268 65.7784L72.4598 66.2256C73.6358 66.3404 74.843 66.5219 76.025 66.5309C76.986 66.5382 77.4747 66.1956 78.2685 65.7348C79.4781 63.9696 80.1455 61.6864 80.868 59.6848L83.732 51.576C86.1416 44.8469 88.8154 38.1861 90.8369 31.3249C89.1365 30.9864 87.2029 31.0037 85.462 30.8826L74.74 30.1495L39.5437 27.8981C36.4321 27.6983 33.3191 27.5187 30.2046 27.3592C28.0886 27.2511 23.8366 26.9631 23.8366 26.9631C23.8366 26.9631 22.5024 26.8655 19.3479 26.8697Z"
          fill="url(#paint8_linear_21253_13995)"
        />
        <path
          d="M35.1355 35.8555C35.1355 35.8555 33.4577 41.3719 32.5271 42.6386C32.42 42.7838 32.4177 42.7717 32.4367 42.9485L32.5908 43.0832C33.4394 43.1523 34.2835 43.2344 35.1355 43.1886L35.6313 43.2843C38.2597 43.4858 40.9295 43.5462 43.5413 43.9004C44.5954 41.5081 45.5588 39.0731 46.5977 36.6729C45.4977 36.5763 35.1355 35.8555 35.1355 35.8555Z"
          fill="#51A5FC"
        />
        <path
          d="M53.0334 36.8934C52.8333 36.8543 52.7445 36.8565 52.5497 36.9168C52.0366 37.8144 51.6712 38.8149 51.2464 39.7577C50.5815 41.2338 49.8811 42.6924 49.2446 44.1815C52.7743 44.5698 56.311 44.878 59.8547 45.1058L60.145 45.0806C60.783 44.3818 62.9561 38.9568 63.6309 37.5698C60.1007 37.4616 56.5542 37.1734 53.0334 36.8934Z"
          fill="#51A5FC"
        />
        <path
          d="M19.0334 34.8934C18.8333 34.8543 18.7445 34.8565 18.5497 34.9168C18.0366 35.8144 17.6712 36.8149 17.2464 37.7577C16.5815 39.2338 15.8811 40.6924 15.2446 42.1815C18.7743 42.5698 22.311 42.878 25.8547 43.1058L26.145 43.0806C26.783 42.3818 28.9561 36.9568 29.6309 35.5698C26.1007 35.4616 22.5542 35.1734 19.0334 34.8934Z"
          fill="#51A5FC"
        />
        <path
          d="M31.6057 48.4098C31.3433 48.3825 31.1088 48.3349 30.8598 48.4396C30.3607 49.3131 27.6742 55.2112 27.7691 55.8718C31.1493 56.2296 34.5349 56.533 37.9256 56.7821C38.121 56.8029 38.3052 56.7941 38.5017 56.7818C39.0377 56.1623 41.1258 50.3393 41.5791 49.1716C38.3097 48.7009 34.9019 48.6722 31.6057 48.4098Z"
          fill="#51A5FC"
        />
        <path
          d="M49.1467 49.6121L47.75 49.5117C46.7297 52.1195 45.6875 54.7186 44.6234 57.3087C47.8809 57.691 51.2093 57.8228 54.4811 58.1189L55.4626 58.2019C56.3739 55.5472 57.5014 52.9238 58.6504 50.3638C55.4795 50.1509 52.3116 49.9003 49.1467 49.6121Z"
          fill="#2A5CF8"
        />
        <path
          d="M69.5537 37.9922L69.5453 38.0508L69.4558 38.0209C69.1428 38.2928 66.6447 44.6259 66.2687 45.5675C68.0043 45.6059 69.7519 45.7894 71.4854 45.9C73.3857 46.0213 75.3179 46.0712 77.2067 46.3101L77.5134 46.2674C78.0875 45.6039 79.984 39.9792 80.5205 38.6726C76.8663 38.5213 73.2047 38.222 69.5537 37.9922Z"
          fill="#51A5FC"
        />
        <path
          d="M14.5231 46.8242C14.235 46.7916 13.9481 46.7487 13.7002 46.9043C13.3083 47.7844 10.7848 54.1424 10.8239 54.5412C12.1157 54.7451 13.4769 54.7836 14.781 54.8899C16.8609 55.0516 18.937 55.2515 21.0094 55.4896L21.3254 55.5036C22.0601 55.0425 23.8935 48.5269 24.7812 47.6467C22.8451 47.5405 20.9022 47.3461 18.9683 47.1914C17.491 47.073 15.9937 47.0059 14.5231 46.8242Z"
          fill="#1C4BF1"
        />
        <path
          d="M65.9824 50.9058L64.7024 50.793L61.6061 58.6655C64.8986 58.9805 68.2033 59.2038 71.4904 59.5717C71.8669 59.5977 72.2486 59.6626 72.6237 59.7092C73.5681 57.0221 74.6912 54.3761 75.7363 51.7261C72.5056 51.3558 69.2236 51.1942 65.9824 50.9058Z"
          fill="#2A5CF8"
        />
        <path
          d="M45.1757 35.2461L45.789 35.7048C45.789 35.7048 43.8962 37.9571 43.6559 38.1929L40.6865 41.5011C40.6865 41.5011 38.506 43.9378 38.2812 44.043C38.2971 43.5578 38.4003 43.0696 38.3741 42.5847C38.7716 42.3124 45.0578 35.5027 45.1757 35.2461Z"
          fill="#1C4BF1"
        />
        <path
          d="M41.7555 32C42.2073 32.2068 42.7099 32.8559 43.0646 33.2177C43.3736 33.5011 45.1364 34.9968 45.1761 35.247C45.0583 35.5036 38.772 42.3133 38.3745 42.5855C38.4008 43.0705 38.2976 43.5587 38.2817 44.0438L38.2493 44.1499C37.8793 44.1566 36.5512 42.278 36.0529 42.104C35.9986 42.0849 35.8832 42.0896 35.8227 42.0843L35.907 42.0107C35.9174 41.914 35.9162 41.9206 35.8576 41.8412C35.6383 41.5428 34.2022 39.9644 33.9408 39.9251L33.9396 39.8125C33.6288 39.4206 33.017 38.6115 32.6378 38.3538L32.5248 38.2457C32.5132 38.1607 32.4638 38.0788 32.4277 38.0011C32.4973 37.7232 32.7049 37.6305 32.8362 37.3876C32.8405 37.379 32.8448 37.37 32.8491 37.3613C33.158 37.1489 35.0308 35.2084 35.115 34.8996C35.4503 35.0744 37.1331 36.5958 37.2387 36.9186C37.2949 36.8891 37.3468 36.8546 37.3969 36.816C37.7309 36.5597 40.4439 33.6982 40.5922 33.3998C40.6057 33.3731 40.6057 33.3013 40.6099 33.2688C40.6484 33.2549 40.6857 33.2388 40.7217 33.2188C40.9928 33.0688 41.6761 32.2737 41.7555 32Z"
          fill="#51A5FC"
        />
        <path
          d="M32.8491 37.3594C33.2392 37.473 36.84 41.0766 37.4793 41.6991C37.7694 41.9815 38.0594 42.3345 38.3745 42.5836C38.4008 43.0686 38.2976 43.5568 38.2817 44.0419L38.2493 44.148C37.8793 44.1547 36.5512 42.2761 36.0529 42.1021C35.9986 42.0829 35.8832 42.0877 35.8227 42.0824L35.907 42.0088C35.9174 41.912 35.9162 41.9187 35.8576 41.8393C35.6383 41.5409 34.2022 39.9625 33.9408 39.9232L33.9396 39.8106C33.6288 39.4187 33.017 38.6096 32.6378 38.3519L32.5248 38.2437C32.5132 38.1587 32.4638 38.0769 32.4277 37.9991C32.4973 37.7212 32.7049 37.6286 32.8362 37.3857C32.8405 37.377 32.8448 37.3681 32.8491 37.3594Z"
          fill="#2A5CF8"
        />
      </g>
      <defs>
        <linearGradient id="paint0_linear_21253_13995" x1="37.3028" y1="10.2104" x2="35.7861" y2="1.74191" gradientUnits="userSpaceOnUse">
          <stop stop-color="#1321B5" />
          <stop offset="1" stop-color="#2F56E5" />
        </linearGradient>
        <linearGradient id="paint1_linear_21253_13995" x1="83.2736" y1="12.5683" x2="82.1272" y2="4.82393" gradientUnits="userSpaceOnUse">
          <stop stop-color="#0B1ABA" />
          <stop offset="1" stop-color="#2C4CD6" />
        </linearGradient>
        <linearGradient id="paint2_linear_21253_13995" x1="61.1546" y1="11.188" x2="58.0909" y2="3.36377" gradientUnits="userSpaceOnUse">
          <stop stop-color="#0C19AF" />
          <stop offset="1" stop-color="#284FDB" />
        </linearGradient>
        <linearGradient id="paint3_linear_21253_13995" x1="92.2413" y1="73.1255" x2="33.1226" y2="29.9299" gradientUnits="userSpaceOnUse">
          <stop stop-color="#1F38A0" />
          <stop offset="1" stop-color="#334FD5" />
        </linearGradient>
        <linearGradient id="paint4_linear_21253_13995" x1="50.3656" y1="36.2799" x2="59.2137" y2="3.38706" gradientUnits="userSpaceOnUse">
          <stop stop-color="#3A63DA" />
          <stop offset="1" stop-color="#5D97F8" />
        </linearGradient>
        <linearGradient id="paint5_linear_21253_13995" x1="29.1648" y1="18.7912" x2="29.5641" y2="6.02184" gradientUnits="userSpaceOnUse">
          <stop stop-color="#1D34CB" />
          <stop offset="1" stop-color="#507EEC" />
        </linearGradient>
        <linearGradient id="paint6_linear_21253_13995" x1="53.7786" y1="19.847" x2="54.4942" y2="5.62068" gradientUnits="userSpaceOnUse">
          <stop stop-color="#2336CB" />
          <stop offset="1" stop-color="#5388F7" />
        </linearGradient>
        <linearGradient id="paint7_linear_21253_13995" x1="75.6687" y1="21.2235" x2="79.2929" y2="2.64442" gradientUnits="userSpaceOnUse">
          <stop stop-color="#243DD6" />
          <stop offset="1" stop-color="#629CF9" />
        </linearGradient>
        <linearGradient id="paint8_linear_21253_13995" x1="30.327" y1="75.1612" x2="61.7067" y2="16.05" gradientUnits="userSpaceOnUse">
          <stop stop-color="#DEE7F6" />
          <stop offset="1" stop-color="#FFFFFE" />
        </linearGradient>
      </defs>
    </chakra.svg>
  );
}

export function MedicalRecordillustration(props: BoxProps) {
  return (
    <chakra.svg width="36" height="50" viewBox="0 0 36 50" fill="none" xmlns="http://www.w3.org/2000/svg" {...(props as any)}>
      <path
        d="M35.0847 43.7834C35.1499 44.1879 34.8744 44.5691 34.4699 44.6343L7.8882 48.9153C7.48349 48.9804 7.10225 48.7049 7.03711 48.3005L1.23033 12.2446C1.16515 11.8399 1.44067 11.4588 1.84537 11.3937L28.4271 7.11269C28.8318 7.04751 29.2128 7.32281 29.278 7.72753L35.0847 43.7834Z"
        fill="#00506C"
      />
      <path d="M27.176 8.93828L3.60498 12.7344L9.00905 46.2897L32.5801 42.4936L27.176 8.93828Z" fill="white" />
      <path d="M3.60498 12.7344L9.00908 46.29L32.5799 42.4939L3.60498 12.7344Z" fill="#E0EAEF" />
      <path
        d="M20.6715 7.43202L20.3836 5.64395L17.6012 6.09205C17.6774 5.7267 17.6924 5.34286 17.6295 4.9523C17.3533 3.23733 15.7392 2.07092 14.0243 2.34711C12.3094 2.62329 11.1433 4.23746 11.4194 5.95243C11.4823 6.34276 11.6174 6.70266 11.8044 7.02562L9.02181 7.47376L9.30978 9.26183L7.2983 9.58577L7.85906 13.0677L23.2438 10.59L22.683 7.10807L20.6715 7.43202ZM12.9716 5.70221C12.8336 4.84496 13.4169 4.03772 14.2741 3.89966C15.1318 3.76154 15.9387 4.34487 16.0768 5.20213C16.1467 5.63586 16.0315 6.05629 15.7905 6.38413C15.6725 6.54386 15.5249 6.6819 15.3536 6.78877C15.3529 6.78889 15.3523 6.78969 15.3523 6.78969C15.267 6.84225 15.1757 6.88765 15.0799 6.92403C15.079 6.92418 15.0778 6.92437 15.0772 6.92471C14.9809 6.96094 14.8798 6.98794 14.7744 7.0049C14.6691 7.02187 14.5648 7.02795 14.4622 7.02375C14.4613 7.02367 14.4603 7.02382 14.4594 7.02397C14.3571 7.01974 14.2562 7.00527 14.1586 6.98194C14.1579 6.98182 14.1571 6.98147 14.1571 6.98147C13.9609 6.93379 13.777 6.84912 13.6147 6.73407C13.2825 6.49877 13.0415 6.13571 12.9716 5.70221Z"
        fill="#008BCC"
      />
      <path
        d="M22.2276 9.90829C20.7718 10.3832 19.2241 10.4779 17.7338 10.7768C15.8657 11.1514 13.9754 11.403 12.0845 11.6283C11.5708 11.6896 11.2189 11.627 11.0366 11.0894C10.8594 10.5673 10.8092 9.89634 10.8173 9.34625C10.8253 8.80761 11.1659 8.54847 11.648 8.38583C11.9994 8.26734 12.5688 8.29825 12.8514 8.03965C13.366 7.5687 12.2682 6.59984 12.2053 6.0438C12.1361 5.42926 12.1687 4.7507 12.369 4.16036C12.6748 3.26044 13.3554 2.8018 14.0996 2.33887C14.0742 2.34225 14.0491 2.34296 14.0233 2.34711C12.3084 2.62329 11.1423 4.23746 11.4185 5.95243C11.4813 6.34276 11.6164 6.70266 11.8034 7.02562L9.02083 7.47376L9.3088 9.26183L7.29732 9.58577L7.85808 13.0677L23.2428 10.59L22.8051 7.87203C22.7951 8.81956 23.105 9.62222 22.2276 9.90829Z"
        fill="#008BCC"
      />
      <path d="M13.1735 16.2423L6.11621 17.3789L7.25283 24.4365L14.3102 23.2999L13.1735 16.2423Z" fill="#3FC3D8" />
      <path d="M10.3931 17.6311L9.19385 17.8242L10.0349 23.0466L11.2342 22.8535L10.3931 17.6311Z" fill="white" />
      <path d="M12.7286 19.3191L7.50635 20.1602L7.6995 21.3595L12.9217 20.5184L12.7286 19.3191Z" fill="white" />
      <path d="M26.3167 14.5346L13.9434 16.5273L14.0117 16.9516L26.385 14.9588L26.3167 14.5346Z" fill="#7A9CAB" />
      <path d="M26.5515 15.9917L14.1782 17.9844L14.2465 18.4084L26.6198 16.4156L26.5515 15.9917Z" fill="#7A9CAB" />
      <path d="M26.7864 17.4526L14.4131 19.4453L14.4814 19.8693L26.8547 17.8766L26.7864 17.4526Z" fill="#7A9CAB" />
      <path d="M27.0213 18.9096L14.6479 20.9023L14.7162 21.3263L27.0895 19.3336L27.0213 18.9096Z" fill="#7A9CAB" />
      <path d="M27.2566 20.3706L14.8833 22.3633L14.9516 22.7873L27.3249 20.7946L27.2566 20.3706Z" fill="#7A9CAB" />
      <path d="M27.9379 24.4566L7.75488 27.707L7.82309 28.1306L28.0061 24.8801L27.9379 24.4566Z" fill="#7A9CAB" />
      <path d="M28.1727 25.9136L7.98975 29.1641L8.05795 29.5876L28.2409 26.3371L28.1727 25.9136Z" fill="#7A9CAB" />
      <path d="M28.4076 27.3745L8.22461 30.625L8.29282 31.0485L28.4758 27.7981L28.4076 27.3745Z" fill="#7A9CAB" />
      <path d="M28.6425 28.8316L8.45947 32.082L8.52764 32.5053L28.7106 29.2549L28.6425 28.8316Z" fill="#7A9CAB" />
      <path d="M18.7861 31.9178L8.69482 33.543L8.76299 33.9663L18.8543 32.3411L18.7861 31.9178Z" fill="#7A9CAB" />
      <path d="M29.4017 33.5425L9.21875 36.793L9.287 37.2167L29.47 33.9663L29.4017 33.5425Z" fill="#7A9CAB" />
      <path d="M29.6366 34.9995L9.45361 38.25L9.52186 38.6738L29.7048 35.4233L29.6366 34.9995Z" fill="#7A9CAB" />
      <path d="M29.8715 36.4605L9.68848 39.7109L9.75672 40.1347L29.9397 36.8842L29.8715 36.4605Z" fill="#7A9CAB" />
      <path d="M30.1063 37.9175L9.92334 41.168L9.99159 41.5917L30.1746 38.3413L30.1063 37.9175Z" fill="#7A9CAB" />
      <path d="M20.2499 41.0037L10.1587 42.6289L10.2269 43.0527L20.3182 41.4275L20.2499 41.0037Z" fill="#7A9CAB" />
    </chakra.svg>
  );
}
