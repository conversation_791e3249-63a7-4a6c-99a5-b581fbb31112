import { getStatusColor } from "@/utils";
import { Badge, HStack, Skeleton, StackProps, Text } from "@chakra-ui/react";
import { useMemo } from "react";

interface ProviderStatusProps extends StackProps {
  variant?: "plain" | "badge";
  status: string;
  loading?: boolean;
}
export function ProviderStatus(props: ProviderStatusProps) {
  const { variant, status, loading, ...xprops } = props;

  const color = useMemo(() => getStatusColor(status), [status]);

  if (variant === "plain") {
    return (
      <HStack {...xprops}>
        <Skeleton variant="shine" loading={loading}>
          <Text fontSize="14px" fontWeight="500" color={color}>
            {status}
          </Text>
        </Skeleton>
      </HStack>
    );
  }

  return (
    <Skeleton variant="shine" loading={loading}>
      <Badge py="4px" px="8px" maxW="fit-content" color="white" textTransform="capitalize" bg={color} {...xprops}>
        {status}
      </Badge>
    </Skeleton>
  );
}
