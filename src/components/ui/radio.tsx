import { RadioGroup as ChakraRadioGroup } from "@chakra-ui/react";
import * as React from "react";

export interface RadioProps extends ChakraRadioGroup.ItemProps {
  rootRef?: React.Ref<HTMLDivElement>;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  indicatorProps?: ChakraRadioGroup.ItemControlProps;
}

export const Radio = React.forwardRef<HTMLInputElement, RadioProps>(function Radio(props, ref) {
  const { children, inputProps, rootRef, indicatorProps, ...rest } = props;
  return (
    <ChakraRadioGroup.Item w="fit-content" ref={rootRef} {...rest}>
      <ChakraRadioGroup.ItemHiddenInput ref={ref} {...inputProps} />
      <ChakraRadioGroup.ItemIndicator
        _checked={{
          borderColor: "primary",
          color: "primary",
          "& .dot": { scale: 0.8 },
        }}
        {...indicatorProps}
      />
      {children && (
        <ChakraRadioGroup.ItemText fontSize="14px" fontWeight="400">
          {children}
        </ChakraRadioGroup.ItemText>
      )}
    </ChakraRadioGroup.Item>
  );
});

export function RadioGroup(props: ChakraRadioGroup.RootProps) {
  return <ChakraRadioGroup.Root colorPalette="blue" variant="outline" {...props} />;
}
