/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Box,
  HStack,
  StackProps,
  Float,
  Circle,
  Show,
  IconButton,
  Drawer,
  Portal,
  CloseButton,
  Stack,
  SkeletonCircle,
  Span,
  Container,
} from "@chakra-ui/react";
import { useNavigate, Link as RouterLink } from "react-router";

import logo from "@/assets/svgs/logo.svg?react";
import Button from "./button";
import { Icon } from "./icon";
import { GoSignOut } from "react-icons/go";
import { Tooltip } from "./tooltip";
import { MobileTopNavItem, TopNavItem } from "./nav-item";
import { useAuth } from "@/hooks";
import { ConfirmationModal } from "./confirmation-modal";
// import { getUserProfileQueryOpts } from "@/pages/payer/profile/queries";
import { useQuery } from "@tanstack/react-query";
// import truncate from "lodash.truncate";
import { getUnreadNotificationCountQueryOpts } from "@/queries";

interface TopNavProps extends StackProps {
  is_signedin?: boolean;
}

export function TopNav(props: TopNavProps) {
  const { ...xprops } = props;
  const navigate = useNavigate();
  const { is_signedin, logout } = useAuth();

  const { data: note_data, isPending: loading_note_count } = useQuery({ ...getUnreadNotificationCountQueryOpts(), enabled: is_signedin });

  const total_unread_notes = note_data?.data?.total || 0;

  return (
    <Container maxW="100rem" py="4px" hideFrom={is_signedin ? "2sm" : "none"}>
      <HStack py="20px" justifyContent="space-between" viewTransitionName="top-nav" {...xprops}>
        <RouterLink to={is_signedin ? "/" : "https://themsmt.com"}>
          <Box as={logo} boxSize="45px" />
        </RouterLink>

        <Show when={!is_signedin}>
          <Span flex="1" />
          <HStack gap="24px" hideBelow="2sm">
            <Button variant="subtle" onClick={() => navigate("/signin")}>
              Sign in
            </Button>
            <Button onClick={() => navigate("/signup")}>
              {/* <RouterLink to="/signup">Sign up</RouterLink> */}
              Sign up
            </Button>
          </HStack>
        </Show>

        <HStack gap="16px">
          <Show when={is_signedin}>
            <HStack gap="16px">
              <Tooltip content="View notifications" showArrow positioning={{ placement: "bottom-start" }}>
                <TopNavItem boxSize="40px" to="/notifications" p="0" alignItems="center" justifyContent="center" pos="relative">
                  <Icon name="bell" boxSize="18px" />

                  <Float translate="30% -30%">
                    <SkeletonCircle variant="shine" loading={loading_note_count}>
                      <Show when={total_unread_notes > 0 && !loading_note_count}>
                        <Circle w="24px" h="16px" bg="red" color="white" fontSize="12px" fontWeight="500">
                          {total_unread_notes > 9 ? "9+" : total_unread_notes}
                        </Circle>
                      </Show>

                      <Show when={!!loading_note_count}>
                        <Circle w="24px" h="16px" bg="red" color="white" fontSize="12px" fontWeight="500">
                          9+
                        </Circle>
                      </Show>
                    </SkeletonCircle>
                  </Float>
                </TopNavItem>
              </Tooltip>

              {/* <Tooltip content="Sign out" showArrow positioning={{ placement: "bottom-end" }}> */}
              <ConfirmationModal
                title="Sign out?"
                applyBtnText="Sign out"
                description="This action would sign you out of the platform"
                onConfirm={logout}
              >
                <IconButton boxSize="50px" variant="subtle" aria-label="Sign out" bg="primary.100" hideBelow="md">
                  {/* <Icon name="external_link" color="error" /> */}
                  <GoSignOut />
                </IconButton>
              </ConfirmationModal>
              {/* </Tooltip> */}
            </HStack>
          </Show>

          <MobileMenu />
        </HStack>
      </HStack>
    </Container>
  );
}

// TODO: Add error boundary for the router error elements to show detailed error messages for good UX.
function MobileMenu() {
  const navigate = useNavigate();
  const { is_signedin, logout } = useAuth();

  return (
    <Drawer.Root>
      <Drawer.Trigger asChild hideFrom="2sm">
        <IconButton boxSize="50px" variant="subtle" aria-label="Sign out" bg="primary">
          <Icon name="menu" color="white" />
        </IconButton>
      </Drawer.Trigger>
      <Portal>
        <Drawer.Backdrop hideFrom="2sm" />
        <Drawer.Positioner hideFrom="2sm">
          <Drawer.Content p="16px" bg="menu.bkg" maxW={{ base: "100%", "2sm": "320px" } as any}>
            <Drawer.Header p="0">
              {/* <Drawer.Title>Drawer Title</Drawer.Title> */}
              <HStack w="100%" justifyContent="space-between">
                <Box as={logo} boxSize="32px" />

                <Drawer.CloseTrigger asChild pos="unset" rounded="8px">
                  <CloseButton size="sm" bg="primary" color="white" />
                </Drawer.CloseTrigger>
              </HStack>
            </Drawer.Header>
            <Drawer.Body p="0" my="26px">
              <Show when={is_signedin}>
                <Stack gap="16px" px="4px">
                  <MobileTopNavItem to="/" prefetch="intent">
                    Dashboard
                  </MobileTopNavItem>
                  <MobileTopNavItem to="/appointments" prefetch="intent">
                    Appointments
                  </MobileTopNavItem>
                  <MobileTopNavItem to="/partners" prefetch="intent">
                    Partners
                  </MobileTopNavItem>
                  <MobileTopNavItem to="/wallet" prefetch="intent">
                    Wallet
                  </MobileTopNavItem>
                  <MobileTopNavItem to="/notifications" prefetch="intent">
                    Notifications
                  </MobileTopNavItem>
                  <MobileTopNavItem to="/setup" prefetch="intent">
                    Setup
                  </MobileTopNavItem>
                  <MobileTopNavItem to="/profile" prefetch="intent">
                    Profile
                  </MobileTopNavItem>
                </Stack>
              </Show>
              <Show when={!is_signedin}>
                <Stack gap="16px" px="4px">
                  <MobileTopNavItem to="/">About</MobileTopNavItem>
                  <MobileTopNavItem to="/">Find Consultants</MobileTopNavItem>
                  <MobileTopNavItem to="/">Lorem</MobileTopNavItem>

                  <Drawer.ActionTrigger asChild>
                    <Button
                      variant="solid"
                      size="md"
                      fontSize="14px"
                      w="100%"
                      onClick={() => navigate("/signup", { viewTransition: true })}
                    >
                      Sign up
                    </Button>
                  </Drawer.ActionTrigger>

                  <Drawer.ActionTrigger asChild>
                    <Button
                      variant="subtle"
                      size="md"
                      fontSize="14px"
                      w="100%"
                      onClick={() => navigate("/signin", { viewTransition: true })}
                    >
                      Sign in
                    </Button>
                  </Drawer.ActionTrigger>
                </Stack>
              </Show>
            </Drawer.Body>

            <Show when={is_signedin}>
              <Drawer.Footer px="4px">
                <Drawer.ActionTrigger asChild>
                  <ConfirmationModal
                    title="Sign out?"
                    applyBtnText="Sign out"
                    description="This action would sign you out of the platform"
                    onConfirm={logout}
                  >
                    <Button variant="subtle" size="lg" rounded="8px" fontSize="14px" w="100%" bg="primary.300" leftIcon={<GoSignOut />}>
                      Logout
                    </Button>
                  </ConfirmationModal>
                </Drawer.ActionTrigger>
              </Drawer.Footer>
            </Show>
          </Drawer.Content>
        </Drawer.Positioner>
      </Portal>
    </Drawer.Root>
  );
}
