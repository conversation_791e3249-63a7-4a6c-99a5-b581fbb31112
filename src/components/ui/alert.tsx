/* eslint-disable @typescript-eslint/no-explicit-any */
import { Alert as Chakra<PERSON>lert, HStack } from "@chakra-ui/react";
import * as React from "react";

export interface AlertProps extends Omit<ChakraAlert.RootProps, "title"> {
  startElement?: React.ReactNode;
  endElement?: React.ReactNode;
  title?: React.ReactNode;
  icon?: React.ReactElement;
  titleProps?: ChakraAlert.TitleProps;
}

export const Alert = React.forwardRef<HTMLDivElement, AlertProps>(function Alert(props, ref) {
  const { title, children, icon, startElement, endElement, titleProps, ...rest } = props;
  return (
    <ChakraAlert.Root ref={ref} p="24px" flexDir={{ sm: "column", "4sm": "row" } as any} {...rest}>
      <HStack gap="12px" alignItems="flex-start">
        {startElement || <ChakraAlert.Indicator>{icon}</ChakraAlert.Indicator>}
        {children ? (
          <ChakraAlert.Content>
            <ChakraAlert.Title {...titleProps}>{title}</ChakraAlert.Title>
            <ChakraAlert.Description>{children}</ChakraAlert.Description>
          </ChakraAlert.Content>
        ) : (
          <ChakraAlert.Title flex="1">{title}</ChakraAlert.Title>
        )}
      </HStack>
      {endElement}
    </ChakraAlert.Root>
  );
});
