import { HStack, Text } from "@chakra-ui/react";
import { Tag, TagProps } from "./tag";
import { getStatusColor } from "@/utils";

interface FilterTagProps extends TagProps {
  name: string;
  value: string;
}

export function FilterTag(props: FilterTagProps) {
  const { name, value, ...xprops } = props;
  const color = getStatusColor(value);
  // console.log("Tag color", color);
  return (
    <Tag closable {...xprops}>
      <HStack fontSize="14px">
        <Text color="text.2" textTransform="lowercase">
          {name}:
        </Text>
        <Text color={color} textTransform="capitalize">
          {value}
        </Text>
      </HStack>
    </Tag>
  );
}
