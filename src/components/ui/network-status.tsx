/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useNetworkStatus } from "@/hooks/use-network-status";
import { Box, HStack, Text } from "@chakra-ui/react";
import { useEffect, useRef } from "react";
import { animate } from "motion";

export function NetworkStatusIndicator() {
  const { online, reconnecting } = useNetworkStatus();
  const indicatorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (indicatorRef.current) {
      if (!online) {
        // Slide in from top
        animate(
          indicatorRef.current,
          { y: ["-100%", "0%"], opacity: [0, 1] } as any,
          { duration: 0.3, easing: "ease-out" } as any
        );
      } else {
        // Slide out to top
        animate(
          indicatorRef.current,
          { y: ["0%", "-100%"], opacity: [1, 0] } as any,
          { duration: 0.3, easing: "ease-in" } as any
        ).finished.then(() => {
          if (indicatorRef.current) {
            indicatorRef.current.style.display = "none";
          }
        });
      }
    }
  }, [online]);

  if (online) return null;

  return (
    <Box
      ref={indicatorRef}
      position="fixed"
      top="0"
      left="0"
      right="0"
      bg={reconnecting ? "orange.500" : "red.500"}
      color="white"
      py="2"
      zIndex="toast"
    >
      <HStack justify="center" gap="2">
        <Box
          id="reconnect-indicator"
          w="2"
          h="2"
          borderRadius="full"
          bg="white"
        />
        <Text fontSize="sm" fontWeight="medium">
          {reconnecting
            ? "Attempting to reconnect..."
            : "You're offline. Check your connection."}
        </Text>
      </HStack>
    </Box>
  );
}
