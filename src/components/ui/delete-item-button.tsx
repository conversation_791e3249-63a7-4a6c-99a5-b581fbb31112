import { ConfirmationModal, Icon, toaster } from "@/components";
import { IUseMutationOptions, useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { IconButton, IconButtonProps, SkeletonCircle } from "@chakra-ui/react";

interface DeleteItemProps {
  confirmationTitle: string;
  confirmationDescription: string;
  mutationOpts: IUseMutationOptions;
  successMessage: string;

  triggerProps?: IconButtonProps;
  loading?: boolean;

  onSuccess?: () => void;
}

export function DeleteItem(props: DeleteItemProps) {
  const { confirmationTitle, confirmationDescription, mutationOpts, successMessage, triggerProps, loading = false, onSuccess } = props;
  const { mutateAsync, isPending: deleting } = useMutation(mutationOpts);

  const handleDelete = async () => {
    const promise = mutateAsync({});
    const result = await tryCatch(promise);

    if (result.ok) {
      toaster.success({
        title: "Success",
        description: successMessage,
      });

      onSuccess?.();
    }
  };

  return (
    <ConfirmationModal title={confirmationTitle} applyBtnText="Delete" description={confirmationDescription} onConfirm={handleDelete}>
      <SkeletonCircle variant="shine" loading={loading}>
        <IconButton
          size="sm"
          variant="outline"
          borderColor="primary.50"
          aria-label={confirmationDescription}
          loading={deleting}
          _hover={{ "--before-bg": "colors.red.500", "& *": { color: "white" } }}
          {...triggerProps}
        >
          <Icon name="trash" boxSize="16px" color="text" />
        </IconButton>
      </SkeletonCircle>
    </ConfirmationModal>
  );
}
