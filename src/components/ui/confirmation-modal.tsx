import { create<PERSON><PERSON><PERSON>, Dialog, IconButton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { Button, Icon } from "@/components";
import { ReactNode, useRef } from "react";
// interface ConfirmationModalProps extends Dialog.RootProps {
//   title: string;
//   description?: string;
//   applyBtnText?: string;
//   onConfirm?: (closeCallback?: VoidFunction) => void;
//   // onApply?: (closeCallback?: VoidFunction) => void; // deprecated
//   loading?: boolean;
//   closeOnConfirm?: boolean;
//   contentProps?: Dialog.ContentProps;
//   hideCancelBtn?: boolean;
// }

interface ConfirmationModalProps extends Omit<Dialog.RootProps, "children"> {
  title: string;
  description?: string;
  applyBtnText?: string;
  onConfirm?: (closeCallback?: VoidFunction) => void;
  loading?: boolean;
  closeOnConfirm?: boolean;
  contentProps?: Dialog.ContentProps;
  hideCancelBtn?: boolean;
  children?: ReactNode;
}

export function ConfirmationModal(props: ConfirmationModalProps) {
  const {
    title,
    description,
    applyBtnText,
    onConfirm,
    children,
    contentProps,
    loading = false,
    closeOnConfirm = true,
    hideCancelBtn = false,
    ...xprops
  } = props;

  const contentRef = useRef<HTMLDivElement | null>(null);

  const disclosure = useDisclosure();

  const handleApply = () => {
    onConfirm?.(disclosure.onClose);
    if (closeOnConfirm) disclosure.onClose();
  };

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      {!!children && <Dialog.Trigger asChild>{children}</Dialog.Trigger>}
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="400px" gap="24px" bg="white" {...contentProps}>
            <Stack gap="24px">
              <Dialog.Header p="0" justifyContent="space-between">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  {title}
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close tier modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>

              <Dialog.Description fontSize="14px" color="text">
                {description ?? "This action would suspend name [provider first name] from this platform"}
              </Dialog.Description>
            </Stack>

            {/* <Dialog.Body p="0">
              <Text fontSize="14px" color="text">
                {description ??
                  "This action would suspend name [provider first name] from this platform"}
              </Text>
            </Dialog.Body> */}

            <Dialog.Footer p="0">
              {!hideCancelBtn && (
                <Dialog.ActionTrigger asChild>
                  <Button w="50%" size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>
              )}

              <Button w="50%" size="md" onClick={handleApply} loading={loading} disabled={loading}>
                {applyBtnText ?? "Confirm"}
              </Button>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}

// eslint-disable-next-line react-refresh/only-export-components
export const confirmation_dialog = createOverlay<ConfirmationModalProps>((props) => {
  const { title, description, applyBtnText, onConfirm, loading = false, ...rest } = props;
  return (
    <ConfirmationModal
      title={title}
      description={description}
      applyBtnText={applyBtnText}
      onConfirm={onConfirm}
      loading={loading}
      {...rest}
    />
  );
});
