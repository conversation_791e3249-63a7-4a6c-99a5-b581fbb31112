/* eslint-disable @typescript-eslint/no-explicit-any */
import { Box, BoxProps } from "@chakra-ui/react";
import bgPattern0 from "@/assets/svgs/bg-pattern-0.svg?react";
import bgPattern1 from "@/assets/svgs/bg-pattern-1.svg?react";

interface Props extends BoxProps {
  bg0Props?: BoxProps;
  bg1Props?: BoxProps;
}

export function BgPattern(props: Props) {
  const { bg0Props, bg1Props, ...xprops } = props;
  return (
    <Box pos="fixed" top="0" bottom="0" left="0" right="0" w="100%" h="100%" {...xprops}>
      <Box
        as={bgPattern0}
        pos="absolute"
        top={{ base: "-100px", "2sm": "0" } as any}
        left={{ base: "-100px", "2sm": "0" } as any}
        viewTransitionName="bg-pattern-0"
        {...bg0Props}
      />
      <Box
        as={bgPattern1}
        pos="absolute"
        bottom={{ base: "-100px", "2sm": "0" } as any}
        right={{ base: "-100px", "2sm": "0" } as any}
        viewTransitionName="bg-pattern-1"
        {...bg1Props}
      />
    </Box>
  );
}
