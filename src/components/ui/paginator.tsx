/* eslint-disable @typescript-eslint/no-explicit-any */
import { ButtonGroup, HStack, IconButton, NumberInput, Pagination, Text } from "@chakra-ui/react";
import { LuChevronLeft, LuChevronRight } from "react-icons/lu";

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface PaginatorProps extends Pagination.RootProps {}

export function Paginator(props: PaginatorProps) {
  const { page: page_raw, ...xprops } = props;
  // Make sure page is a number to avoid errors in next and previous buttons
  const page = parseInt((page_raw ?? "1").toString());

  return (
    <Pagination.Root page={page} {...xprops}>
      <ButtonGroup variant="subtle" size="sm" w="full" justifyContent="space-between" flexDir={{ base: "column", "2sm": "row" } as any}>
        <HStack>
          <Text fontSize="14px" color="text.3">
            Showing page{" "}
          </Text>

          <Pagination.PageText format="compact" flex="1" />
        </HStack>

        <HStack>
          <Pagination.PrevTrigger asChild>
            <IconButton>
              <LuChevronLeft />
            </IconButton>
          </Pagination.PrevTrigger>

          <NumberInput.Root
            variant="subtle"
            size="sm"
            // min={1}
            // max={Math.ceil(pagi.total / pagi.limit)}
            // w={`min(36px, max(10px, calc(20px  * ${page.toString().length}))`}
            w="44px"
          >
            <NumberInput.Input
              bg="input"
              focusRingColor="primary"
              defaultValue={props?.page ?? 1}
              disabled
              // onChange={(e) => setPage(+e.target.value)}
            />
          </NumberInput.Root>

          <Pagination.NextTrigger asChild>
            <IconButton>
              <LuChevronRight />
            </IconButton>
          </Pagination.NextTrigger>
        </HStack>
      </ButtonGroup>
    </Pagination.Root>
  );
}

// function NextButton() {
//   const { nextPage } = usePaginationContext();
//   console.log("Next page", nextPage);
//   return (
//     <IconButton>
//       <LuChevronRight />
//     </IconButton>
//   );
// }
