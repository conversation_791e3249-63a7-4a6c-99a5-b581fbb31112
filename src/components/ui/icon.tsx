// /* eslint-disable @typescript-eslint/no-explicit-any */
import { FunctionComponent, useMemo } from "react";
import { PolymorphicProps, Image, Box, BoxProps } from "@chakra-ui/react";
import { when } from "@/utils";

// svg icons
import logo from "@/assets/svgs/logo.svg?react";
import case_sensitive from "@/assets/svgs/case-sensitive.svg?react";
import house_address from "@/assets/svgs/house-address.svg?react";
import cloud_upload from "@/assets/svgs/cloud-upload.svg?react";
import dashboard from "@/assets/svgs/dashboard.svg?react";
import email from "@/assets/svgs/email.svg?react";
import eye from "@/assets/svgs/eye.svg?react";
import eye_off from "@/assets/svgs/eye-off.svg?react";
import file from "@/assets/svgs/file.svg?react";
import number from "@/assets/svgs/number.svg?react";
import stethoscope from "@/assets/svgs/stethoscope.svg?react";
import wallet from "@/assets/svgs/wallet.svg?react";
import calendar from "@/assets/svgs/calendar.svg?react";
import calendar_check from "@/assets/svgs/calendar-check.svg?react";
import bolt from "@/assets/svgs/bolt.svg?react";
import users from "@/assets/svgs/users.svg?react";
import chart_pie from "@/assets/svgs/chart-pie.svg?react";
import bell from "@/assets/svgs/bell.svg?react";
import search from "@/assets/svgs/search.svg?react";
import download from "@/assets/svgs/download.svg?react";
import list_filter from "@/assets/svgs/list-filter.svg?react";
import arrow_left from "@/assets/svgs/arrow-left.svg?react";
import menu from "@/assets/svgs/menu.svg?react";
import clock from "@/assets/svgs/clock.svg?react";
import heart from "@/assets/svgs/heart.svg?react";
import external_link from "@/assets/svgs/external-link.svg?react";
import hand_coins from "@/assets/svgs/hand-coins.svg?react";
import plus from "@/assets/svgs/plus.svg?react";
import close from "@/assets/svgs/close.svg?react";
import list from "@/assets/svgs/list.svg?react";
import dollar_sign from "@/assets/svgs/dollar-sign.svg?react";
import shield_half from "@/assets/svgs/shield-half.svg?react";
import user from "@/assets/svgs/user-round.svg?react";
import pen from "@/assets/svgs/pen.svg?react";
import camera from "@/assets/svgs/camera.svg?react";
import step_checked from "@/assets/svgs/step-checked.svg?react";
import octagon_pause from "@/assets/svgs/octagon-pause.svg?react";
import link from "@/assets/svgs/link.svg?react";

import work from "@/assets/svgs/work.svg?react";
import phone from "@/assets/svgs/phone.svg?react";
import video from "@/assets/svgs/video.svg?react";
import trash from "@/assets/svgs/trash.svg?react";
import video_off from "@/assets/svgs/video-off.svg?react";
import audio_lines from "@/assets/svgs/audio-lines.svg?react";
import chevron_down from "@/assets/svgs/arrow-down.svg?react";
import triangle_alert from "@/assets/svgs/triangle-alert.svg?react";
import id_card from "@/assets/svgs/id-card.svg?react";
import school from "@/assets/svgs/school.svg?react";
import file_text from "@/assets/svgs/file-text.svg?react";
import clipboard_plus from "@/assets/svgs/clipboard-plus.svg?react";
import badge_check from "@/assets/svgs/badge-check.svg?react";
import log_out from "@/assets/svgs/log-out.svg?react";
import circle_cancel from "@/assets/svgs/circle-cancel.svg?react";
import circle_check from "@/assets/svgs/circle-check.svg?react";
import ship_wheel from "@/assets/svgs/ship-wheel.svg?react";
import hand_heart from "@/assets/svgs/hand-heart.svg?react";
import gender from "@/assets/svgs/gender.svg?react";
import trip_map from "@/assets/svgs/map.svg?react";

export type IconNames =
  | "logo"
  | "case_sensitive"
  | "house_address"
  | "cloud_upload"
  | "dashboard"
  | "email"
  | "eye"
  | "eye_off"
  | "file"
  | "number"
  | "wallet"
  | "calendar"
  | "calendar_check"
  | "stethoscope"
  | "bolt"
  | "chart_pie"
  | "users"
  | "bell"
  | "menu"
  | "search"
  | "arrow_left"
  | "download"
  | "list_filter"
  | "clock"
  | "heart"
  | "external_link"
  | "hand_coins"
  | "plus"
  | "close"
  | "dollar_sign"
  | "list"
  | "shield_half"
  | "user"
  | "pen"
  | "octagon_pause"
  | "camera"
  | "step_checked"
  | "video"
  | "video_off"
  | "audio_lines"
  | "chevron_down"
  | "trash"
  | "work"
  | "triangle_alert"
  | "phone"
  | "id_card"
  | "link"
  | "badge_check"
  | "clipboard_plus"
  | "file_text"
  | "school"
  | "log_out"
  | "circle_cancel"
  | "circle_check"
  | "ship_wheel"
  | "hand_heart"
  | "gender"
  | "trip_map";

export interface IconProps extends BoxProps {
  name: IconNames;
}

type SvgIconType = FunctionComponent<
  React.SVGProps<SVGSVGElement> & {
    title?: string;
  }
>;
type MapType = Record<IconNames, string | SvgIconType>;

export function Icon(props: IconProps) {
  const { name, ...xprops } = props;

  const MappedIcon = useMemo(() => {
    const map: MapType = {
      // svg icons
      logo,
      case_sensitive,
      house_address,
      cloud_upload,
      dashboard,
      email,
      eye,
      eye_off,
      file,
      number,
      wallet,
      stethoscope,
      calendar,
      calendar_check,
      bolt,
      chart_pie,
      users,
      bell,

      menu,
      download,
      arrow_left,
      search,
      list_filter,
      clock,
      heart,
      external_link,
      hand_coins,
      plus,
      close,
      dollar_sign,
      list,
      shield_half,
      pen,
      octagon_pause,
      user,
      camera,
      step_checked,

      work,
      phone,
      video,
      video_off,
      audio_lines,
      chevron_down,
      trash,
      triangle_alert,
      id_card,
      link,
      badge_check,
      clipboard_plus,
      file_text,
      school,
      log_out,
      circle_cancel,

      circle_check,
      ship_wheel,
      hand_heart,
      gender,
      trip_map,
    };

    return map[name];
  }, [name]);

  const as = when<PolymorphicProps["as"]>(typeof MappedIcon === "string", Image, MappedIcon as SvgIconType);
  const src = when(typeof MappedIcon === "string", MappedIcon as string, undefined);

  return <Box color="grey.300" as={as} boxSize="18px" objectFit="contain" {...{ src }} {...xprops} />;
}
