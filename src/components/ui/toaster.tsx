"use client";

import { Toaster as <PERSON><PERSON><PERSON>oaster, <PERSON>, Spinner, <PERSON>ack, Toast, createToaster } from "@chakra-ui/react";

// eslint-disable-next-line react-refresh/only-export-components
export const toaster = createToaster({
  placement: "bottom-end",
  pauseOnPageIdle: true,
  max: 3,
});

type UpdateToastProps = Parameters<typeof toaster.create>[0];

// eslint-disable-next-line react-refresh/only-export-components
export function upsertToast(options: Partial<UpdateToastProps & { id: string }>) {
  const { id, ...rest } = options;
  if (id) {
    return toaster.update(id, rest);
  } else {
    return toaster.create(rest);
  }
}

export const Toaster = () => {
  return (
    <Portal>
      <ChakraToaster toaster={toaster} insetInline={{ mdDown: "4" }}>
        {(toast) => (
          <Toast.Root width={{ md: "sm" }}>
            {toast.type === "loading" ? <Spinner size="sm" color="blue.solid" /> : <Toast.Indicator />}
            <Stack gap="1" flex="1" maxWidth="100%">
              {toast.title && <Toast.Title>{toast.title}</Toast.Title>}
              {toast.description && <Toast.Description>{toast.description}</Toast.Description>}
            </Stack>
            {toast.action && <Toast.ActionTrigger>{toast.action.label}</Toast.ActionTrigger>}
            {toast.meta?.closable && <Toast.CloseTrigger />}
          </Toast.Root>
        )}
      </ChakraToaster>
    </Portal>
  );
};
