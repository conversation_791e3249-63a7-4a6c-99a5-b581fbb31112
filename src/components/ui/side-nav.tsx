/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Avatar,
  Box,
  BoxProps,
  Circle,
  Float,
  Heading,
  HStack,
  Link,
  LinkProps,
  Show,
  SkeletonCircle,
  Span,
  Stack,
  Text,
} from "@chakra-ui/react";
import logo from "@/assets/svgs/logo.svg?react";
import { NavLink as RouterNavLink, NavLinkProps as RouterNavLinkProps } from "react-router";
import { Icon, IconNames } from "./icon";
import { useAuth, useUser } from "@/hooks";
import { ConfirmationModal } from "./confirmation-modal";
import { useQuery } from "@tanstack/react-query";
import { getUnreadNotificationCountQueryOpts, partnerListCountQueryOpts } from "@/queries";
import { toQueryString } from "@/utils";
import { Tooltip } from "./tooltip";
import { memo, useMemo } from "react";

export const SidebarNav = memo(() => {
  const { logout, is_signedin } = useAuth();
  const { data, isPending: loading_user } = useUser();

  const { data: partner_count_data, isPending: loading_partner_count } = useQuery(
    partnerListCountQueryOpts(toQueryString({ status: "0" }))
  );
  const { data: note_data, isPending: loading_note_count } = useQuery({ ...getUnreadNotificationCountQueryOpts(), enabled: is_signedin });

  const user = data?.data;
  const total_unread_notes = note_data?.data?.total || 0;
  const partner_count = partner_count_data?.data?.total || 0;

  return (
    <Stack
      py="24px"
      w={{ sm: "fit-content", md: "var(--sidebar-w)" }}
      gridArea="sidebar"
      h="100vh"
      bg="#fff"
      scrollBehavior="smooth"
      gap="32px"
      border="1px solid transparent"
      borderRightColor="stroke.divider"
      viewTransitionName="sidebar"
      overflow="hidden"
      pos="sticky"
      top="0"
      hideBelow="2sm"
      css={{}}
    >
      <RouterNavLink to="/" viewTransition>
        <HStack px="16px" alignItems="center" gap="4px" userSelect="none">
          <Box as={logo} boxSize="45px" />
          <Heading fontSize="32px" fontWeight="700" textTransform="uppercase" color="primary" hideBelow="4sm">
            msmt
          </Heading>
        </HStack>
      </RouterNavLink>

      <Stack>
        <NavItem label="Dashboard" icon="dashboard" to="/" />
        <NavItem label="Appointments" icon="calendar_check" to="/appointments" />
        <NavItem label="Partners" icon="link" to="/partners" itemCount={partner_count} loading_item_count={loading_partner_count} />
        <NavItem label="Wallet" icon="wallet" to="/wallet" />
        <NavItem
          label="Notifications"
          icon="bell"
          to="/notifications"
          itemCount={total_unread_notes}
          loading_item_count={loading_note_count}
        />
        <NavItem label="Setup" icon="bolt" to="/setup" />
      </Stack>

      <Span flex="1" />

      <Stack>
        <ConfirmationModal
          title="Sign out?"
          applyBtnText="Sign out"
          description="This action would sign you out of the platform"
          onConfirm={logout}
        >
          <NavItem label="Logout" icon="log_out" />
        </ConfirmationModal>

        <NavItem to="/profile" textDecoration="none !important">
          <HStack gap="8px">
            <SkeletonCircle variant="shine" loading={loading_user}>
              <Avatar.Root boxSize="40px" textDecoration="none !important">
                <Avatar.Fallback fontSize="12px" name={user?.name} />
                {user?.avatar && <Avatar.Image src={user?.avatar} />}
              </Avatar.Root>
            </SkeletonCircle>

            {!loading_user && (
              <Stack gap="2px" hideBelow="4sm">
                <Text fontSize="14px" fontWeight="500" color="text">
                  {user?.name || "N/A"}
                </Text>
                <Link asChild focusRingColor="primary">
                  <RouterNavLink to="/profile" viewTransition prefetch="intent">
                    <Text fontSize="12px" color="primary">
                      View Profile
                    </Text>
                  </RouterNavLink>
                </Link>
              </Stack>
            )}
          </HStack>
        </NavItem>
      </Stack>
    </Stack>
  );
});

interface NavItemProps extends LinkProps {
  label?: string;
  icon?: IconNames;
  to?: string;
  itemCount?: number;
  loading?: boolean;
  loading_item_count?: boolean;
}

const NavItem = memo((props: NavItemProps) => {
  const { label, icon, to, itemCount = 0, loading = false, loading_item_count = false, children, ...xprops } = props;

  const MaybeRouter = (props: { to?: string } & BoxProps) => {
    const { to, ...rest } = props;
    if (!to) return <Box w="100%" {...(rest as BoxProps)} cursor="default" />;
    return <RouterNavLink to={to} viewTransition prefetch="intent" {...(rest as Omit<RouterNavLinkProps, "to">)} />;
  };

  const memoized_children = useMemo(() => children, [children]);

  return (
    <Tooltip content={label} showArrow positioning={{ placement: "right" }} openDelay={100} contentProps={{ hideFrom: "4sm" }}>
      <Link
        asChild
        px="20px"
        py="12px"
        alignItems="center"
        justifyContent={{ sm: "center", "4sm": "flex-start" } as any}
        gap="8px"
        userSelect="none"
        focusRing="inside"
        focusVisibleRing="inside"
        focusRingColor="primary"
        borderRadius="0"
        color="text.2"
        transition="all .3s ease-in-out"
        pos="relative"
        textDecor="none"
        _hover={{
          bg: "primary.50",
          color: "primary",

          "& svg": { color: "primary" },
        }}
        className="nav-item"
        css={{
          "&.active": {
            bg: "primary.50",
            color: "primary",
            "& svg": { color: "primary" },

            "&:after": {
              content: "''",
              pos: "absolute",
              h: "100%",
              w: "4px",
              bg: "primary",
              top: "0",
              left: "-1px",
              rounded: "full",
              viewTransitionName: "side-nav-active-indicator",
            },
          },
        }}
        {...xprops}
      >
        <MaybeRouter to={to}>
          {icon && <Icon name={icon} boxSize="16px" color="text.3" />}

          {!memoized_children && label && (
            <Text fontSize="14px" fontWeight="400" textTransform="capitalize" color="inherit" hideBelow="4sm">
              {label}
            </Text>
          )}

          {memoized_children}

          <Float top="50%" translate={{ sm: "-10px -60%", "4sm": "-20px -50%" } as any} scale={{ base: 0.8, "4sm": 1 } as any}>
            <SkeletonCircle variant="shine" loading={loading_item_count}>
              <Show when={itemCount > 0 && !loading}>
                <Circle w="24px" h="16px" bg="red" color="white" fontSize="12px" fontWeight="500">
                  {itemCount > 9 ? "9+" : itemCount}
                </Circle>
              </Show>

              <Show when={!!loading_item_count}>
                <Circle w="24px" h="16px" bg="red" color="white" fontSize="12px" fontWeight="500">
                  9+
                </Circle>
              </Show>
            </SkeletonCircle>
          </Float>
        </MaybeRouter>
      </Link>
    </Tooltip>
  );
});
