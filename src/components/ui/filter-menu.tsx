/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  HStack,
  IconButton,
  Menu,
  Portal,
  RadioGroupRootProps,
  RadioGroupValueChangeDetails,
  Show,
  SkeletonCircle,
  Stack,
  Text,
  useDisclosure,
} from "@chakra-ui/react";
import { Icon } from "./icon";
import { Radio, RadioGroup } from "./radio";
import { forwardRef, SyntheticEvent, useState } from "react";
import Button from "./button";

interface FilterMenuProps extends Menu.RootProps {
  closeOnApply?: boolean;
  onApply?: () => void;
  loading?: boolean;
}

export const FilterMenu = forwardRef((props: FilterMenuProps, ref) => {
  const { children, onApply, loading = false, closeOnApply = true, ...xprops } = props;
  const { open, setOpen } = useDisclosure();

  const handleApply = (e: SyntheticEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onApply?.();

    if (closeOnApply) setOpen(false);
  };

  return (
    <Menu.Root open={open} onOpenChange={(e) => setOpen(e.open)} positioning={{ placement: "bottom-end" }} {...xprops}>
      <Menu.Trigger asChild>
        {/* <Button variant="outline">Edit</Button> */}
        <SkeletonCircle variant="shine" loading={loading}>
          <IconButton
            variant="subtle"
            aria-label="List filter"
            size="md"
            w="40px"
            h="32px"
            _hover={{
              "& :where(svg)": {
                color: "white !important",
              },
            }}
          >
            <Icon name="list_filter" color="black" />
          </IconButton>
        </SkeletonCircle>
      </Menu.Trigger>
      <Portal>
        <Menu.Positioner>
          <Menu.Content minW={{ sm: "100%", md: "fit-content" }} rounded="16px" ref={ref as any}>
            {/* <Menu.Item value="cut">
              <LuScissors />
              <Box flex="1">Cut</Box>
              <Menu.ItemCommand>⌘X</Menu.ItemCommand>
            </Menu.Item>
            <Menu.Item value="copy">
              <LuCopy />
              <Box flex="1">Copy</Box>
              <Menu.ItemCommand>⌘C</Menu.ItemCommand>
            </Menu.Item>
            <Menu.Item value="paste">
              <LuClipboardPaste />
              <Box flex="1">Paste</Box>
              <Menu.ItemCommand>⌘V</Menu.ItemCommand>
            </Menu.Item> */}

            {children}

            <HStack p="20px" justifyContent="flex-end" gap="16px">
              <Button variant="subtle" size="sm" minW="80px" onClick={() => setOpen(false)}>
                Close
              </Button>
              <Button variant="solid" size="sm" minW="80px" onClick={handleApply}>
                Apply
              </Button>
            </HStack>
          </Menu.Content>
        </Menu.Positioner>
      </Portal>
    </Menu.Root>
  );
});

interface FilterRadioGroupProps extends RadioGroupRootProps {
  heading?: string;
  items: { label: string; value: string; show_content?: boolean }[];
}

export function FilterRadioGroup(props: FilterRadioGroupProps) {
  const { heading, items, value, onValueChange, children, ...xprops } = props;

  const canShow = (value: string) => items.some((i) => i.value === value && i.show_content);
  const [show_children, setShow] = useState(value ? canShow(value) : false);

  const handleValueChange = (e: RadioGroupValueChangeDetails) => {
    // const can_show_children = items.some((i) => i.value === e.value && i.show_content);
    if (!e.value) return;
    setShow(canShow(e.value));
    onValueChange?.(e);
  };

  return (
    <Stack>
      <Text fontSize="12px" fontWeight="500" color="text.2" textTransform="uppercase">
        {heading}
      </Text>

      <RadioGroup size="sm" display="flex" flexDir="column" gap="4px" w="100%" value={value} onValueChange={handleValueChange} {...xprops}>
        {items.map((item) => (
          <Radio
            w="100%"
            py="14px"
            px="8px"
            rounded="8px"
            key={item.value}
            value={item.value}
            _checked={{ bg: "primary.50", color: "primary" }}
          >
            {item.label}
          </Radio>
        ))}
      </RadioGroup>

      <Show when={show_children && children}>{children}</Show>
    </Stack>
  );
}
