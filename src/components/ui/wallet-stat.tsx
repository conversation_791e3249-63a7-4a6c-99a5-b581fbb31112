import { Skeleton } from "@chakra-ui/react";
import { Stat, StatProps } from "./stat";

export interface WalletStatProps extends StatProps {
  loading?: boolean;
}

export function WalletStat(props: WalletStatProps) {
  const { loading = false, ...xprops } = props;

  return (
    <Skeleton asChild variant="shine" loading={loading}>
      <Stat
        py="20px"
        bg="stat"
        rounded="8px"
        alignItems="center"
        w="100%"
        css={{
          "& :where(.chakra-stat__label)": {
            color: "text.2",
            fontSize: "14px",
          },
          "& :where(.chakra-stat__valueText )": {
            color: "text",
            fontSize: "24px",
            fontWeight: "400",
          },
        }}
        {...xprops}
      />
    </Skeleton>
  );
}
