import { StatDataRo } from "@/interfaces";
import { Chart, useChart } from "@chakra-ui/charts";
import { HStack, Skeleton, Stack, Text } from "@chakra-ui/react";
import { Cell, Label, Legend, LegendProps, Pie, PieChart } from "recharts";

interface GenderDonutChartProps {
  data?: StatDataRo["demo_stat"];
  loading?: boolean;
}

export function GenderDonutChart(props: GenderDonutChartProps) {
  const { data, loading = false } = props;

  const has_data = (data?.total_female || 0) + (data?.total_male || 0) > 0;

  const chart = useChart({
    data: [
      { name: "male", value: has_data ? data?.total_male : 1, color: "actions.upcoming" },
      { name: "female", value: has_data ? data?.total_female : 1, color: "actions.stale" },
    ],
  });

  // console.log("Gender donut", data);

  return (
    <Skeleton variant="shine" rounded="8px" loading={loading}>
      <Chart.Root
        // boxSize="200px"
        w="200px"
        h="310px"
        chart={chart}
        mx="auto"
        css={{
          "& svg": { position: "relative", top: "-40px" },
          "& svg text tspan:first-of-type": {
            fontSize: "14px !important",
            fontWeight: "400 !important",
            fill: "text.2 !important",
          },
          "& svg text tspan:nth-of-type(2)": {
            fontSize: "22px !important",
            fontWeight: "500 !important",
            fill: "text !important",
          },
        }}
      >
        <PieChart>
          {/* <Tooltip
          cursor={false}
          animationDuration={100}
          content={<Chart.Tooltip hideLabel />}
        /> */}

          {/* <Legend  content={<Chart.Legend />} /> */}
          <Legend iconType="rect" content={<CustomLegend hasData={has_data} />} />

          <Pie innerRadius={92} outerRadius={100} isAnimationActive={false} data={chart.data} dataKey={chart.key("value")} nameKey="name">
            {chart.data.map((item) => {
              return <Cell key={item.name} fill={chart.color(item.color)} />;
            })}
            <Label
              className="text-label"
              content={({ viewBox }) => (
                <Chart.RadialText
                  // gap={29}
                  viewBox={viewBox}
                  title={"Total completed"}
                  description={has_data ? chart.getTotal("value").toLocaleString() : 0}
                />
              )}
            />
          </Pie>
        </PieChart>
      </Chart.Root>
    </Skeleton>
  );
}

function CustomLegend(props: LegendProps & { hasData?: boolean }) {
  const { payload, hasData } = props;

  // console.log("GEnder donut legend", { payload, hasData });

  return (
    <HStack justifyContent="space-between">
      {payload?.map((item) => (
        <HStack key={item.value} borderLeft="3px solid" borderColor={item.color} pl="8px">
          <Stack>
            <Text fontSize="12px" color="text.3" textTransform="capitalize">
              {item.value}
            </Text>
            <Text fontSize="16px" color="text">
              {hasData ? item.payload?.value : 0}
            </Text>
          </Stack>
        </HStack>
      ))}
    </HStack>
  );
}
