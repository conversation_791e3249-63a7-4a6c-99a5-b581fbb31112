import { Chart, useChart } from "@chakra-ui/charts";
import { Box, FormatNumber, HStack, Show, Skeleton, Stack, Text, VStack } from "@chakra-ui/react";
import { CartesianGrid, Line, LineChart, Tooltip, TooltipProps, XAxis } from "recharts";
import { Empty } from "../ui/empty";
import { GraphDataRo } from "@/interfaces";
import { yearMonthToFullName } from "@/utils";
import { useMemo } from "react";
import { eachMonthOfInterval, format } from "date-fns";

interface MultiLineChartProps {
  data?: GraphDataRo[];
  loading?: boolean;
}

export function MultiLineChart(props: MultiLineChartProps) {
  const { data = [], loading = false } = props;

  const transformed = data
    .sort((a, b) => {
      const a_date = new Date(a.month);
      const b_date = new Date(b.month);
      return a_date.getTime() - b_date.getTime();
    })
    .map((item) => ({
      month: yearMonthToFullName(item.month),
      completed: item?.total_completed ?? 0,
      canceled: item?.total_canceled ?? 0,
      upcoming: item?.total_upcoming ?? 0,
    }));

  const has_data = transformed.length > 0;

  const fill_in_data = useMemo(() => {
    const months = eachMonthOfInterval({
      start: new Date(new Date().getFullYear(), 0),
      end: new Date(new Date().getFullYear(), 11),
    }).map((item) => format(item, "yyyy-MM"));

    if (has_data) {
      // if (transformed.length > 1) return transformed;

      const missing_months = months.filter((month) => !transformed.find((item) => item.month === yearMonthToFullName(month)));
      return [
        ...transformed,
        ...missing_months.map((month) => ({
          month: yearMonthToFullName(month),
          completed: 0,
          canceled: 0,
          upcoming: 0,
        })),
      ].sort((a, b) => {
        const a_date = new Date(a.month);
        const b_date = new Date(b.month);
        return a_date.getTime() - b_date.getTime();
      });
    }

    return months.map((month) => ({
      month: yearMonthToFullName(month),
      completed: 0,
      canceled: 0,
      upcoming: 0,
    }));
  }, [has_data, transformed]);

  const chart = useChart({
    data: [
      // { completed: 20, upcoming: 20, canceled: 45, month: "January" },
      // { completed: 35, upcoming: 92, canceled: 52, month: "February" },
      // { completed: 48, upcoming: 78, canceled: 20, month: "March" },
      // { completed: 48, upcoming: 78, canceled: 20, month: "April" },
      // { completed: 65, upcoming: 82, canceled: 75, month: "May" },
      // { completed: 72, upcoming: 95, canceled: 40, month: "June" },
      // { completed: 72, upcoming: 95, canceled: 40, month: "July" },
      // { completed: 85, upcoming: 20, canceled: 95, month: "August" },
      // { completed: 85, upcoming: 20, canceled: 95, month: "September" },
      // { completed: 85, upcoming: 20, canceled: 95, month: "October" },
      // { completed: 85, upcoming: 20, canceled: 95, month: "November" },
      // { completed: 85, upcoming: 20, canceled: 95, month: "December" },
      ...fill_in_data,
    ],
    series: [
      { name: "completed", color: "actions.completed", label: "Completed" },
      { name: "upcoming", color: "primary", label: "Upcoming" },
      { name: "canceled", color: "actions.canceled", label: "Canceled" },
    ],
  });

  return (
    <Skeleton variant="shine" rounded="8px" loading={loading}>
      <Show when={has_data}>
        <Chart.Root minW="100%" maxH="310px" chart={chart}>
          <LineChart data={chart.data} margin={{ left: 16, right: 16 /*top: 40 */ }}>
            <CartesianGrid
              stroke={chart.color("border")}
              // stroke={"none"}
              opacity={0.3}
              strokeDasharray="3 3"
              horizontal={true}
              vertical={false}
            />
            <XAxis
              axisLine={false}
              tickLine={false}
              // label={<CustomXAxisLabel />}
              dataKey={chart.key("month")}
              tickFormatter={(value) => value.slice(0, 3)}
              stroke={chart.color("text.3")}
            />
            <Tooltip
              // position={{ x: 0, y: 0 }}
              animationDuration={100}
              cursor={{
                stroke: chart.color("text.3"),
                strokeWidth: "1",
                strokeDasharray: "8 8",
              }}
              content={<CustomTooltip />}
              cursorStyle={{
                ":hover": { stroke: chart.color("red") },
              }}
            />
            {chart.series.map((item) => (
              <Line
                key={item.name}
                type="natural"
                isAnimationActive={false}
                dataKey={chart.key(item.name)}
                stroke={chart.color(item.color)}
                strokeWidth={1.5}
                dot={false}
              />
            ))}
          </LineChart>
        </Chart.Root>
      </Show>

      <Show when={!has_data}>
        <Chart.Root minW="100%" maxH="310px" display="flex" alignItems="center" justifyContent="center" chart={chart}>
          <VStack alignItems="center" justifyContent="center" mt="-40px">
            <Empty subtitle="Chart will appear here" forCharts />
          </VStack>
        </Chart.Root>
      </Show>
    </Skeleton>
  );
}

function CustomTooltip(props: TooltipProps<string, string>) {
  const { active, payload, label } = props;
  if (!active || !payload || payload.length === 0) return null;

  return (
    <Stack minW="155px" rounded="sm" bg="black" p="8px 14px" gap="4px" shadow="0px 7px 20.6px 0px #00000040">
      <HStack>
        <Text fontSize="12px" fontWeight="500" color="blue.300">
          {label} Bookings
        </Text>
      </HStack>
      <Stack gap="4px">
        {payload.map((item) => (
          <HStack key={item.name} gap="8px">
            <Box boxSize="5px" rounded="full" bg={item.color} />
            <HStack fontSize="14px" fontWeight="400" color="white">
              <Text>
                <FormatNumber style="decimal" value={+(item.value || 0)} />
              </Text>
              <Text>{item.name}</Text>
            </HStack>
          </HStack>
        ))}
      </Stack>
    </Stack>
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function CustomXAxisLabel(props: any) {
  // const {} = props;
  console.log("Label props", props);

  return <Text color="primary">Label</Text>;
}
