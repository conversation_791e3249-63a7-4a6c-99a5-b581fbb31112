import { RatingDataRo } from "@/interfaces";
import { HStack, Progress, Show, Skeleton, Span, Stack, StackProps, Text, VStack } from "@chakra-ui/react";
import { Empty } from "../ui/empty";

interface RatingIndexChartProps extends StackProps {
  stat?: RatingDataRo["service_stat"];
  loading?: boolean;
}

export function RatingIndexChart(props: RatingIndexChartProps) {
  const { stat, loading = false, ...xprops } = props;

  // const len = 5;
  // const transformed = transformRatingStat(stat);
  // const ratings = Object.values(transformed).sort((a, b) => b.star - a.star);
  // console.log("Transformed rating stat", ratings);

  const is_empty = (stat || []).length < 1;

  return (
    <Stack gap="24px" {...xprops}>
      <Show when={is_empty && loading}>
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={`service-${i}`} variant={"shine"} loading={loading}>
            <HStack gap="8px">
              <Span alignSelf="flex-start" fontSize="12px" color="text.3">
                {i + 1}.
              </Span>
              <Progress.Root key={i} w="100%" value={2} max={100}>
                <Progress.Label mb="4px" fontSize="14px" justifyContent="space-between" display="flex" fontWeight="400">
                  <Span color="text.2" textTransform="capitalize">
                    Service
                  </Span>
                  <Span color="text" fontWeight="500">
                    25
                  </Span>
                </Progress.Label>
                <Progress.Track h="6px" bg="primary.50" borderColor="transparent" shadow="none" rounded="full">
                  <Progress.Range bg="primary" />
                </Progress.Track>
              </Progress.Root>
            </HStack>
          </Skeleton>
        ))}
      </Show>

      <Show when={is_empty && !loading}>
        <VStack alignItems="center" justifyContent="center" my="8cqh">
          <Empty subtitle="Chart will appear here" forCharts />
        </VStack>
      </Show>

      <Show when={!is_empty && !loading}>
        {(stat ?? []).map(({ service_offer_name, total_count }, i) => (
          <Skeleton key={`service-${i}`} variant={"shine"} loading={loading}>
            <HStack gap="8px">
              <Span alignSelf="flex-start" fontSize="12px" color="text.3">
                {i + 1}.
              </Span>
              <Progress.Root key={i} w="100%" value={total_count} max={100}>
                <Progress.Label mb="4px" fontSize="14px" justifyContent="space-between" display="flex" fontWeight="400">
                  <Span color="text.2" textTransform="capitalize">
                    {service_offer_name}
                  </Span>
                  <Span color="text" fontWeight="500">
                    {total_count}
                  </Span>
                </Progress.Label>
                <Progress.Track h="6px" bg="primary.50" borderColor="transparent" shadow="none" rounded="full">
                  <Progress.Range bg="primary" />
                </Progress.Track>
              </Progress.Root>
            </HStack>
          </Skeleton>
        ))}
      </Show>
    </Stack>
  );
}

interface RatingIndexTagProps extends StackProps {
  label: string;
  value: string;
  loading?: boolean;
}

export function RatingIndexTag(props: RatingIndexTagProps) {
  const { label, value, loading = false } = props;

  return (
    <Skeleton variant="shine" loading={loading}>
      <HStack
        gap="4px"
        p="8px 12px"
        bg="primary.50"
        fontSize="14px"
        fontWeight="400"
        color="primary"
        rounded="full"
        border="1px solid"
        borderColor="primary"
      >
        <Text>{label}</Text>
        <Text color="text.2">({value})</Text>
      </HStack>
    </Skeleton>
  );
}
