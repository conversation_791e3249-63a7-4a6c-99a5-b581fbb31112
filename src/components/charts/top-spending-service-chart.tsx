"use client";

import { MostBookedDaysDataRo } from "@/interfaces";
import { Chart, useChart } from "@chakra-ui/charts";
import { Box, FormatNumber, HStack, Show, Skeleton, Stack, Text, VStack } from "@chakra-ui/react";
import { Bar, BarChart, CartesianGrid, Tooltip, TooltipProps, XAxis, YAxis } from "recharts";
import { Empty } from "../ui/empty";
import groupBy from "lodash.groupby";

interface TopSpendingServiceChartProps {
  data?: MostBookedDaysDataRo[];
  loading?: boolean;
}

export function TopSpendingServiceChart(props: TopSpendingServiceChartProps) {
  const { data = [], loading = false } = props;

  // const is_prod = isProd && !isDevMode;

  // const transformed = data.map((item) => ({
  //   "Total Appointment": item?.total_appointment,
  //   service: capitalize(item?.service_offer_name || ""),
  // }));

  const days = ["Mon", "Tue", "Wed", "<PERSON>hur", "Fri", "<PERSON>t", "Sun"];

  const group = groupBy(data, "day");

  console.log("Most booked day", { group, data });
  const transformed = Array.from({ length: days.length }).map((_, i) => ({
    // value: Math.floor(Math.random() * 100),
    value: group?.[i]?.[0]?.total_count ?? 0,
    day: days[i],
  }));

  const has_data = transformed.length > 0;

  // const chart_dummy_data = [
  //   { "Total Appointment": 40, service: "Addiction" },
  //   {
  //     "Total Appointment": 15,

  //     service: "Couple Counselling",
  //   },
  //   { "Total Appointment": 70, service: "Service 3" },
  //   { "Total Appointment": 175, service: "Service 4" },
  //   { "Total Appointment": 175, service: "Service 5" },
  // ];

  // const chart_data = has_data ? transformed : is_prod ? [] : [];
  // has_data = chart_data.length > 0;
  // const has_data = chart_dummy_data.length > 0;

  const chart = useChart({
    data: [
      ////// INITIAL DATA FORMAT
      // { male: 40, female: 100, service: "Addiction" },
      // {
      //   male: 15,
      //   female: 40,
      //   service: "Couple Counselling",
      // },
      // { male: 70, female: 135, service: "Service 3" },
      // { male: 175, female: 155, service: "Service 4" },
      // { male: 175, female: 155, service: "Service 5" },

      //// UPDATED DATA FORMAT
      ...transformed,
      // ...chart_dummy_data,
    ],
    series: [
      // { name: "male", color: "actions.upcoming" },
      // { name: "female", color: "actions.stale" },
      { name: "value", color: "primary" },
    ],
  });

  return (
    <Skeleton variant="shine" rounded="8px" loading={loading}>
      <Show when={has_data}>
        <Chart.Root maxH="300px" chart={chart}>
          <BarChart data={chart.data} margin={{ left: -32, right: 0 /*top: 40 */ }}>
            <CartesianGrid stroke={chart.color("border")} strokeDasharray="6 6" vertical={false} />
            <XAxis axisLine={false} tickLine={false} dataKey={chart.key("day")} stroke={"none"} />
            <YAxis axisLine={false} tickLine={false} stroke={"none"} />
            <Tooltip cursor={{ fill: chart.color("bg.muted") }} animationDuration={100} content={<CustomTooltip />} />
            {/* <Legend
              layout="horizontal"
              //   align="top"
              verticalAlign="top"
              wrapperStyle={{ paddingLeft: 32, top: -44 }}
              //   content={<Chart.Legend orientation="horizontal" />}
              content={<TopSpendingServiceChartLegend />}
            /> */}
            {chart.series.map((item, i) => (
              <Bar
                isAnimationActive={false}
                key={`${item.name}-${i}`}
                dataKey={chart.key(item.name)}
                fill={chart.color(item.color)}
                barSize={10}
              />
            ))}
          </BarChart>
        </Chart.Root>
      </Show>

      <Show when={!has_data}>
        <Chart.Root maxH="300px" display="flex" alignItems="center" justifyContent="center" chart={chart}>
          <VStack alignItems="center" justifyContent="center" mt="-40px">
            <Empty subtitle="Top spending services will appear here" forCharts />
          </VStack>
        </Chart.Root>
      </Show>
    </Skeleton>
  );
}

function CustomTooltip(props: TooltipProps<string, string>) {
  const { active, payload, label } = props;
  if (!active || !payload || payload.length === 0) return null;

  // const total = payload.reduce((acc, curr) => acc + +(curr.value || 0), 0);

  return (
    <Stack minW="155px" rounded="sm" bg="black" p="8px 14px" gap="4px" shadow="0px 7px 20.6px 0px #00000040">
      <HStack>
        <Text fontSize="12px" fontWeight="500" color="blue.300" textTransform="capitalize">
          {label}
        </Text>
      </HStack>
      {/* <Text fontSize="14px" fontWeight="400" color="white">
        {total} Total
      </Text> */}
      <Stack gap="4px">
        {payload.map((item, i) => (
          <HStack key={`${item.name}-${i}`} gap="8px">
            <Box boxSize="5px" rounded="full" bg={item.color} />
            <HStack fontSize="14px" fontWeight="400" color="white">
              <Text>
                <FormatNumber style="decimal" value={+(item.value || 0)} />
              </Text>
              {/* <Text>{item.name}</Text> */}
            </HStack>
          </HStack>
        ))}
      </Stack>
    </Stack>
  );
}

// function TopSpendingServiceChartLegend(props: LegendProps) {
//   const { payload } = props;

//   return (
//     <HStack gap="24px">
//       {payload?.map((item) => (
//         <HStack key={item.value}>
//           <Box boxSize="8px" bg={item.color} />

//           <Stack>
//             <Text fontSize="12px" color="text.3" textTransform="capitalize">
//               {item.value}
//             </Text>
//           </Stack>
//         </HStack>
//       ))}
//     </HStack>
//   );
// }
