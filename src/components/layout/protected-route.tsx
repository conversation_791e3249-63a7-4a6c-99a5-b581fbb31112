// import { useAuthStore } from "@/stores";
import { useAuth, useEventListener } from "@/hooks";
import { useAuthStore } from "@/stores";
import { PropsWithChildren } from "react";
import { Navigate } from "react-router";

type ProtectedRouteProps = PropsWithChildren;

export const USER_PROFILE_DATA_QUERY_KEY = "user/profile-data";

export function ProtectedRoute(props: ProtectedRouteProps) {
  const { children } = props;

  const { is_signedin } = useAuth();
  // usePrefetchQuery(getUserProfileQueryOpts(is_signedin));
  const getUser = useAuthStore((store) => store.getUser);
  // console.log("Protected Route", { is_signedin });

  useEventListener("profile:refetch", async () => await getUser());

  // const is_signedin = false;

  if (is_signedin) return children;
  return <Navigate to="/signin" />;
}
