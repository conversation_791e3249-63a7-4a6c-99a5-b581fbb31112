import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, Text } from "@chakra-ui/react";
import { AnimatePresence, motion } from "motion/react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigation } from "react-router";
import { useDebouncedCallback } from "use-debounce";

const Flx = motion(Flex);

export function NavigationIndicator() {
  const navigation = useNavigation();

  // True when react router is trying to fetch route data via it's loader function
  // while the router is preparing the route data, it'd be nice to show some feedback
  // to the user. I'd be using a spinner to indicate the loading state.

  const [is_navigating, setIsNavigating] = useState(Boolean(navigation.location));

  // console.log("Next location", navigation.location);
  const next_loc_pathname = useRef(navigation.location?.pathname);

  const debounce = useDebouncedCallback((value: boolean) => {
    setIsNavigating(value);
    next_loc_pathname.current = navigation.location?.pathname;
  }, 500);

  const handleChanges = useCallback(
    (value: boolean) => {
      if (value) {
        next_loc_pathname.current = navigation.location?.pathname;
        setIsNavigating(true);
      }

      debounce(value);
    },
    [debounce, navigation.location]
  );

  useEffect(() => {
    if (is_navigating !== Boolean(navigation.location)) {
      handleChanges(Boolean(navigation.location));
    }
  }, [is_navigating, navigation.location, handleChanges]);

  return (
    <AnimatePresence>
      {is_navigating && (
        <Flx
          key="spinner"
          h="fit-content"
          bg="white"
          shadow="2xl"
          rounded="8px"
          p="22px"
          pos="fixed"
          alignItems="center"
          justifyContent="center"
          bottom="40px"
          right="40px"
          viewTransitionName="disabled"
          zIndex={999999999}
          exit={{
            opacity: 0,
            transformPerspective: "500px",
            scale: 0.9,
            rotateX: "45deg",
            y: -20,
            // transition: { bounce: 0.25, damping: 8, type: "spring" },
          }}
          animate={{
            opacity: 1,
            scale: 1,
            transformPerspective: "500px",
            rotateX: "0deg",
            y: 0,
            transition: { bounce: 0.25, damping: 8, type: "spring" },
          }}
          initial={{
            opacity: 0,
            transformPerspective: "500px",
            scale: 0.9,
            rotateX: "-45deg",
            y: 20,
            width: "auto",
            transition: { bounce: 0.25, damping: 8, type: "spring", delay: 0 },
          }}
        >
          <HStack gap="18px" alignItems="center">
            <Spinner size="sm" color="primary" />
            <Stack gap="4px">
              <Text fontFamily="monospace" fontSize="12px">
                Preparing route data
              </Text>
              <Text fontFamily="monospace" fontSize="10px">
                {next_loc_pathname.current}
              </Text>
            </Stack>
          </HStack>
        </Flx>
      )}
    </AnimatePresence>
  );
}
