/* eslint-disable @typescript-eslint/no-explicit-any */
// import { useAuthStore } from "@/stores";
import { Box, Container } from "@chakra-ui/react";
import { PropsWithChildren, useEffect, useRef } from "react";
import { BgPattern } from "../ui/bg-pattern";
import { TopNav } from "../ui/top-nav";
import { useAuth } from "@/hooks";
import { useNavigate } from "react-router";
// import { useNavigate } from "react-router";

type AuthLayoutProps = PropsWithChildren;

export function AuthLayout(props: AuthLayoutProps) {
  const { children } = props;
  const navigate = useNavigate();

  // Tells if the user has been redirected out of the auth layout which
  // composes / is wrapped around un-guarded routes.
  const is_in_app = useRef(false);
  const { is_signedin } = useAuth();

  useEffect(() => {
    if (!is_signedin) {
      is_in_app.current = false;
    }

    if (is_signedin && !is_in_app.current) {
      navigate("/", { replace: true, viewTransition: true });
      is_in_app.current = true;
    }
  }, [is_signedin, navigate, is_in_app]);

  return (
    <Box w="100%" minH="100svh" bg="bkg" pos="relative">
      <Container
        maxW={{ base: "100%", "2sm": "calc(100% - 48px)" } as any}
        pb="40px"
        bg="bkg"
        pos="relative"
      >
        <BgPattern />
        <TopNav />

        <Box>{children}</Box>
      </Container>
    </Box>
  );
}
