import { BoxProps, Grid, Stack } from "@chakra-ui/react";
import { ReactNode } from "react";
import { SidebarNav } from "../ui/side-nav";
import { TopNav } from "../ui/top-nav";
import { AppLocaleProvider } from "./locale-provider";

interface LayoutProps {
  path?: unknown;
  location?: unknown;
  _sidebar?: BoxProps;
  _main?: BoxProps;
  children: ReactNode;
}

export function MainLayout(props: LayoutProps) {
  const { children } = props;

  // prefetch the country list for the phone number field
  // useCommonList("country-list");

  return (
    <AppLocaleProvider>
      <TopNav />
      <Grid
        // py="50px"
        // px="100px"
        w="100%"
        minH="100svh"
        // maxH="100svh"
        bg="bkg"
        templateAreas={"'sidebar main'"}
        templateColumns="auto 1fr"
        css={{ "--sidebar-w": "240px" }}
        // overflow="hidden"
      >
        {/* <Container maxW="98rem" pb="40px" bg="bkg"> */}
        {/* <Box gridArea="sidebar" w="var(--sidebar-w)" h="100%" bg="text.3">
        Sidebar
      </Box> */}
        <SidebarNav />
        <Stack gridArea="main" overflowY="scroll">
          {children}
        </Stack>
        {/* </Container> */}
      </Grid>
    </AppLocaleProvider>
  );
}
