import { useUser } from "@/hooks";
import { LocaleProvider } from "@chakra-ui/react";
import { PropsWithChildren } from "react";

export function AppLocaleProvider(props: PropsWithChildren) {
  const { children } = props;

  const { data: user_data } = useUser();
  const currency = (user_data?.data?.charge_currency || "NGN").toLowerCase();

  const locale_currency_map: Record<string, string> = {
    ngn: "en-NG",
    usd: "en-US",
  };

  return <LocaleProvider locale={locale_currency_map[currency] ?? "en-NG"}>{children}</LocaleProvider>;
}
