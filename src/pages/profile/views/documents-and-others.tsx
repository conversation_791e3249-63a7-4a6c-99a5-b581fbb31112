/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON>ton, DeleteItem, Empty, FieldCard, Icon } from "@/components";
import { <PERSON>ge, Grid, HStack, Separator, Show, Stack, Text, VStack } from "@chakra-ui/react";
import { UploadDocumentModal } from "../ui/upload-document";
import { ProfileInfoCard } from "../ui/profile-info-card";
import { useQuery } from "@tanstack/react-query";
import { deleteUploadedDocumentMutationOpts, getUploadedDocumentsQueryOpts } from "@/queries";
import { format, isToday, isValid, parseISO } from "date-fns";
import { useUser } from "@/hooks";
import { ProfileInfoItem } from "../profile";
import { getStatusColor, mapField } from "@/utils";

export function DocumentsAndOthers() {
  const { data: user_data, isPending: loading_user } = useUser();
  const { data /*, isPending: loading */ } = useQuery(getUploadedDocumentsQueryOpts());

  const user = user_data?.data;
  const docs = data?.data || [];
  const is_empty = docs.length < 1;
  const is_acct_approved = user?.status === 1;

  const get_doc_status_str = (status: number) => mapField(status, "provider-doc") as string;

  const datetime = (iso: string | undefined) => {
    if (!iso || (iso && !isValid(parseISO(iso)))) return "N/A";
    const date = parseISO(iso);
    const day = isToday(date) ? "Today" : format(date, "dd / MMM / yyyy");
    return `${day} • ${format(date, "hh:mm aa")}`;
  };

  return (
    <Stack gap="24px">
      <FieldCard p="16px">
        <HStack w="100%" justifyContent="space-between">
          <Text fontSize="14px" fontWeight="600" color="text">
            Document(s)
          </Text>

          <UploadDocumentModal>
            <Button variant="subtle" size="sm" leftIcon={<Icon name="plus" color="text" />} _hover={{ "& *": { color: "white" } }}>
              Add
            </Button>
          </UploadDocumentModal>
        </HStack>

        <Show when={!is_empty}>
          <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
            {docs.map((item) => (
              <ProfileInfoCard
                key={item.document_id}
                icon="file"
                title={item.document_type}
                subtitle={datetime(item.createdAt)}
                // description={item.location}
                childrenStackProps={{ justifyContent: "center" }}
              >
                <HStack>
                  <Show when={!!(item?.status ?? "")?.toString()}>
                    <Badge color="white" bg={getStatusColor(get_doc_status_str(item?.status))}>
                      {get_doc_status_str(item?.status)}
                    </Badge>
                  </Show>

                  {!is_acct_approved && (
                    <DeleteItem
                      confirmationTitle="Delete Document?"
                      confirmationDescription={`This action will delete ${item.document_type} and is irreversible`}
                      mutationOpts={deleteUploadedDocumentMutationOpts(item.document_id)}
                      successMessage="Document has been deleted successfully"
                    />
                  )}

                  {/* <IconButton
                    size="sm"
                    variant="outline"
                    aria-label="view document"
                    borderColor="primary.50"
                    // loading={deleting}
                    _hover={{ "& *": { color: "white" } }}
                    visibility="hidden"
                  >
                    <Icon name="eye" boxSize="16px" color="text" />
                  </IconButton> */}
                </HStack>
              </ProfileInfoCard>
            ))}
          </Grid>
        </Show>

        <Show when={is_empty}>
          <VStack my="10vh">
            <Empty subtitle="Add your documents to appear here" />
          </VStack>
        </Show>
      </FieldCard>

      <FieldCard p="16px" display="none">
        <HStack w="100%" justifyContent="space-between">
          <Text fontSize="14px" fontWeight="600" color="text">
            Others
          </Text>

          <UploadDocumentModal>
            <Button variant="subtle" size="sm" leftIcon={<Icon name="pen" color="text" />} _hover={{ "& *": { color: "white" } }}>
              Edit
            </Button>
          </UploadDocumentModal>
        </HStack>

        <Stack gap="20px">
          <HStack gap={{ sm: "210px" }}>
            <ProfileInfoItem title="Diagnosis Preference" description={"Lorem ipsum dolor sit amet"} loading={loading_user} />
            <ProfileInfoItem title="Practising Since" description={user?.service_start_year || "N/A"} loading={loading_user} />
          </HStack>

          <Separator />

          <HStack py="8px" px="16px" bg="actions.lightRed" rounded="8px" justifyContent="space-between">
            <Stack gap="2px">
              <Text fontSize="14px" fontWeight="600" color="text.2">
                Delete Account
              </Text>
              <Text fontSize="14px" fontWeight="200" color="text.2">
                This would remove your details from MSMT and irreversible.{" "}
              </Text>
            </Stack>

            <Button
              variant="plain"
              color="actions.canceled"
              size="sm"
              textDecoration="underline"
              _hover={{ "--before-bg": "colors.actions.canceled", textDecor: "none", "& > *": { color: "white" } }}
            >
              Delete Account
            </Button>
          </HStack>
        </Stack>
      </FieldCard>
    </Stack>
  );
}
