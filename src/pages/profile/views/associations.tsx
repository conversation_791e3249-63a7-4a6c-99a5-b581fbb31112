/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Empty, FieldCard, Icon } from "@/components";
import { <PERSON>rid, HStack, IconButton, Show, Text, VStack } from "@chakra-ui/react";
import { ProfileInfoCard } from "../ui/profile-info-card";
import { deleteAssociationMutationOpts, getAssociationQueryOpts } from "@/queries";
import { useQuery } from "@tanstack/react-query";

import { DeleteItem } from "../ui/delete-item";
import { AddAssociationModal } from "../ui/add-association";

export function Associations() {
  const { data } = useQuery({ ...getAssociationQueryOpts() });

  const certs = data?.data || [];
  const is_empty = certs.length < 1;
  //   console.log("Certification", certs);

  return (
    <FieldCard p="16px">
      <HStack w="100%" justifyContent="space-between">
        <Text fontSize="14px" fontWeight="600" color="text">
          Association(s)
        </Text>

        <AddAssociationModal>
          <Button
            size="md"
            variant="subtle"
            leftIcon={<Icon name="plus" color="text" />}
            alignSelf="flex-start"
            _hover={{ "& *": { color: "white" } }}
          >
            Add
          </Button>
        </AddAssociationModal>
      </HStack>

      <Show when={!is_empty}>
        <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          {certs.map((item) => (
            <ProfileInfoCard
              key={item.association_id}
              icon="clipboard_plus"
              title={item.name}
              subtitle={item.member_id}
              description={item.country}
              descriptionProps={{ textTransform: "capitalize" }}
            >
              <AddAssociationModal operation="edit" addedAssociation={item}>
                <IconButton
                  size="sm"
                  variant="outline"
                  aria-label="edit certification"
                  borderColor="primary.50"
                  // loading={deleting}
                  _hover={{ "& *": { color: "white" } }}
                >
                  <Icon name="pen" boxSize="16px" color="text" />
                </IconButton>
              </AddAssociationModal>

              <DeleteItem
                confirmationTitle="Delete Association?"
                confirmationDescription={`This action will delete ${item.name} and is irreversible`}
                mutationOpts={deleteAssociationMutationOpts(item.association_id)}
                successMessage="Association has been deleted successfully"
              />
            </ProfileInfoCard>
          ))}
        </Grid>
      </Show>

      <Show when={is_empty}>
        <VStack my="10vh">
          <Empty subtitle="Add your association to appear here" />
        </VStack>
      </Show>
    </FieldCard>
  );
}
