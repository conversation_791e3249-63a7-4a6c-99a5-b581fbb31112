/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Empty, FieldCard, Icon } from "@/components";
import { <PERSON>rid, HStack, IconButton, Show, Text, VStack } from "@chakra-ui/react";
import { ProfileInfoCard } from "../ui/profile-info-card";
import { deleteEducationMutationOpts, getEducationLevelsQueryOpts } from "@/queries";
import { useQuery } from "@tanstack/react-query";
import { AddEducationModal } from "../ui/add-education";

import { DeleteItem } from "../ui/delete-item";

export function PersonalInfo() {
  const { data } = useQuery({ ...getEducationLevelsQueryOpts() });

  const levels = data?.data || [];
  const is_empty = levels.length < 1;
  //   console.log("Education levels", edu_data);

  return (
    <FieldCard p="16px">
      <HStack w="100%" justifyContent="space-between">
        <Text fontSize="14px" fontWeight="600" color="text">
          Education
        </Text>

        <AddEducationModal>
          <Button
            size="md"
            variant="subtle"
            leftIcon={<Icon name="plus" color="text" />}
            alignSelf="flex-start"
            _hover={{ "& *": { color: "white" } }}
          >
            Add
          </Button>
        </AddEducationModal>
      </HStack>

      <Show when={!is_empty}>
        <Grid templateColumns={{ sm: "1fr", "3sm": "repeat(2, 1fr)" } as any} gap="16px">
          {levels.map((item) => (
            <ProfileInfoCard
              key={item.education_dataid}
              icon="school"
              title={item.name}
              subtitle={[item.degree, `${item.level || "N/A"} - ${item.year || "N/A"}`].join(", ")}
              description={item.location}
            >
              <AddEducationModal operation="edit" addedEducation={item}>
                <IconButton
                  size="sm"
                  variant="subtle"
                  aria-label="edit education"
                  // loading={deleting}
                  _hover={{ "& *": { color: "white" } }}
                >
                  <Icon name="pen" boxSize="16px" color="text" />
                </IconButton>
              </AddEducationModal>

              <DeleteItem
                confirmationTitle="Delete Education?"
                confirmationDescription={`This action will delete ${item.name} and is irreversible`}
                mutationOpts={deleteEducationMutationOpts(item.education_dataid)}
                successMessage="Education has been deleted successfully"
              />
            </ProfileInfoCard>
          ))}
        </Grid>
      </Show>

      <Show when={is_empty}>
        <VStack my="10vh">
          <Empty subtitle="Add your education to appear here" />
        </VStack>
      </Show>
    </FieldCard>
  );
}
