/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Empty, FieldCard, Icon } from "@/components";
import { <PERSON>rid, HStack, IconButton, Show, Text, VStack } from "@chakra-ui/react";
import { ProfileInfoCard } from "../ui/profile-info-card";
import { deletePublicationMutationOpts, getPublicationsQueryOpts } from "@/queries";
import { useQuery } from "@tanstack/react-query";

import { DeleteItem } from "../ui/delete-item";
import { AddPublicationModal } from "../ui/add-publication";

export function Publications() {
  const { data } = useQuery({ ...getPublicationsQueryOpts() });

  const pubs = data?.data || [];
  const is_empty = pubs.length < 1;
  console.log("Publications", pubs);

  return (
    <FieldCard p="16px">
      <HStack w="100%" justifyContent="space-between">
        <Text fontSize="14px" fontWeight="600" color="text">
          Publications
        </Text>

        <AddPublicationModal>
          <Button
            size="md"
            variant="subtle"
            leftIcon={<Icon name="plus" color="text" />}
            alignSelf="flex-start"
            _hover={{ "& *": { color: "white" } }}
          >
            Add
          </Button>
        </AddPublicationModal>
      </HStack>

      <Show when={!is_empty}>
        <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          {pubs.map((item) => (
            <ProfileInfoCard
              key={item.publication_id}
              icon="file_text"
              title={item.name}
              subtitle={item.year.toString()}
              // description={item.location}
            >
              <AddPublicationModal operation="edit" addedPublication={item}>
                <IconButton
                  size="sm"
                  variant="subtle"
                  aria-label="edit publication"
                  // loading={deleting}
                  _hover={{ "& *": { color: "white" } }}
                >
                  <Icon name="pen" boxSize="16px" color="text" />
                </IconButton>
              </AddPublicationModal>

              <DeleteItem
                confirmationTitle="Delete Publication?"
                confirmationDescription={`This action will delete ${item.name} and is irreversible`}
                mutationOpts={deletePublicationMutationOpts(item.publication_id)}
                successMessage="Publication has been deleted successfully"
              />
            </ProfileInfoCard>
          ))}
        </Grid>
      </Show>

      <Show when={is_empty}>
        <VStack my="10vh">
          <Empty subtitle="Add your publications to appear here" />
        </VStack>
      </Show>
    </FieldCard>
  );
}
