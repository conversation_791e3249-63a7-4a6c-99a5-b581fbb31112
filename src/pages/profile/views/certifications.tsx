/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Empty, FieldCard, Icon } from "@/components";
import { Grid, HStack, Show, Text, VStack } from "@chakra-ui/react";
import { ProfileInfoCard } from "../ui/profile-info-card";
import { deleteCertificationMutationOpts, getCertificationsQueryOpts } from "@/queries";
import { useQuery } from "@tanstack/react-query";

import { DeleteItem } from "../ui/delete-item";
import { AddCertificationModal } from "../ui/add-certification";
import { joinVals } from "@/utils";

export function Certifications() {
  const { data } = useQuery({ ...getCertificationsQueryOpts() });

  const certs = data?.data || [];
  const is_empty = certs.length < 1;
  //   console.log("Certification", certs);

  return (
    <FieldCard p="16px">
      <HStack w="100%" justifyContent="space-between">
        <Text fontSize="14px" fontWeight="600" color="text">
          Certifications
        </Text>

        <AddCertificationModal>
          <Button
            size="md"
            variant="subtle"
            leftIcon={<Icon name="plus" color="text" />}
            alignSelf="flex-start"
            _hover={{ "& *": { color: "white" } }}
          >
            Add
          </Button>
        </AddCertificationModal>
      </HStack>

      <Show when={!is_empty}>
        <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          {certs.map((item) => (
            <ProfileInfoCard
              key={item.certification_id}
              icon="badge_check"
              title={item?.cert_name || "N/A"}
              subtitle={(item?.cert_year || "N/A").toString()}
              description={joinVals(", ", item?.state, item?.country)}
              descriptionProps={{ textTransform: "capitalize" }}
            >
              {/* <AddCertificationModal operation="edit" addedCertification={item}>
                <IconButton
                  size="sm"
                  variant="outline"
                  aria-label="edit certification"
                  borderColor="primary.50"
                  // loading={deleting}
                  _hover={{ "& *": { color: "white" } }}
                >
                  <Icon name="pen" boxSize="16px" color="text" />
                </IconButton>
              </AddCertificationModal> */}

              <DeleteItem
                confirmationTitle="Delete Certification?"
                confirmationDescription={`This action will delete ${item?.cert_name || "N/A"} and is irreversible`}
                mutationOpts={deleteCertificationMutationOpts(item.certification_id)}
                successMessage="Certification has been deleted successfully"
              />
            </ProfileInfoCard>
          ))}
        </Grid>
      </Show>

      <Show when={is_empty}>
        <VStack my="10vh">
          <Empty subtitle="Add your certifications to appear here" />
        </VStack>
      </Show>
    </FieldCard>
  );
}
