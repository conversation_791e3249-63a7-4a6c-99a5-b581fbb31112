/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Empty, FieldCard, Icon } from "@/components";
import { Grid, HStack, Show, Text, VStack } from "@chakra-ui/react";
import { ProfileInfoCard } from "../ui/profile-info-card";
import { deleteTrainingMutationOpts, getSpecialTrainingsQueryOpts } from "@/queries";
import { useQuery } from "@tanstack/react-query";

import { DeleteItem } from "../ui/delete-item";
import { AddTrainingModal } from "../ui/add-training";

export function ProfessionalTrainings() {
  const { data } = useQuery({ ...getSpecialTrainingsQueryOpts() });

  const trainings = data?.data || [];
  const is_empty = trainings.length < 1;
  //   console.log("Certification", trainings);

  return (
    <FieldCard p="16px">
      <HStack w="100%" justifyContent="space-between">
        <Text fontSize="14px" fontWeight="600" color="text">
          Professional Trainings (if any)
        </Text>

        <AddTrainingModal addedTrainings={trainings}>
          <Button
            size="md"
            variant="subtle"
            leftIcon={<Icon name="plus" color="text" />}
            alignSelf="flex-start"
            _hover={{ "& *": { color: "white" } }}
          >
            Add
          </Button>
        </AddTrainingModal>
      </HStack>

      <Show when={!is_empty}>
        <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          {trainings.map((item) => (
            <ProfileInfoCard
              key={item.training_id}
              icon="clipboard_plus"
              title={item.name}
              contentProps={{ alignItems: "center" }}
              //   subtitle={item..toString()}
              // description={item.location}
            >
              {/* <AddTrainingModal operation="edit" training={item}>
                <IconButton
                  size="sm"
                  variant="outline"
                  aria-label="edit professional training"
                  borderColor="primary.50"
                  // loading={deleting}
                  _hover={{ "& *": { color: "white" } }}
                >
                  <Icon name="pen" boxSize="16px" color="text" />
                </IconButton>
              </AddTrainingModal> */}

              <DeleteItem
                confirmationTitle="Delete Training?"
                confirmationDescription={`This action will delete ${item.name} and is irreversible`}
                mutationOpts={deleteTrainingMutationOpts(item.training_id)}
                successMessage="Training has been deleted successfully"
              />
            </ProfileInfoCard>
          ))}
        </Grid>
      </Show>

      <Show when={is_empty}>
        <VStack my="10vh">
          <Empty subtitle="No professional training added yet" />
        </VStack>
      </Show>
    </FieldCard>
  );
}
