/* eslint-disable @typescript-eslint/no-explicit-any */
import { Avatar, Container, Grid, Heading, HStack, Link, LinkProps, SkeletonCircle, Stack, Text } from "@chakra-ui/react";
import { Button, DetailItem, DetailItemProps, FieldCard, Icon } from "@/components";
import { useUser } from "@/hooks";
import { NavLink, Outlet, useMatch } from "react-router";
import { joinPhoneNumber } from "@/utils";
import { UpdateProfileModal } from "./ui/update-profile";

// overscroll-behavior: none;
export function ProfileIndex() {
  const { data, isPending: loading_user } = useUser();
  const user = data?.data;

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Heading as="h5" fontSize="20px" fontWeight="600" viewTransitionName="profile-title">
          Profile
        </Heading>

        <Stack gap="8px" viewTransitionName="profile-sublinks">
          <HStack px="16px" py="8px" border="1px solid" borderColor="stroke.divider" rounded="4px" gap="20px" overflowX="scroll">
            <ProfileSubLink label="Personal Information" to="/profile" whiteSpace="nowrap" />
            <ProfileSubLink label="Publications" to="/profile/publications" whiteSpace="nowrap" />
            <ProfileSubLink label="Certifications" to="/profile/certifications" whiteSpace="nowrap" />
            <ProfileSubLink label="Professional trainings" to="/profile/professional-trainings" whiteSpace="nowrap" />
            <ProfileSubLink label="Associations" to="/profile/associations" whiteSpace="nowrap" />
            <ProfileSubLink label="Documents & Others" to="/profile/docs-and-others" whiteSpace="nowrap" />
          </HStack>
        </Stack>

        <FieldCard p="16px" viewTransitionName="profile-info">
          <HStack
            w="100%"
            justifyContent="space-between"
            flexDir={{ sm: "column", "1sm": "row", "3sm": "row" } as any}
            alignItems={{ sm: "flex-start", "3sm": "center" } as any}
          >
            <HStack gap="8px">
              <SkeletonCircle variant="shine" loading={loading_user}>
                <Avatar.Root boxSize={{ "2sm": "40px", "3sm": "80px" } as any} border="4px solid" borderColor="primary.50">
                  <Avatar.Fallback fontSize="12px" name={user?.name} />
                  {user?.avatar && <Avatar.Image src={user?.avatar} />}
                </Avatar.Root>
              </SkeletonCircle>

              <Stack gap="2px">
                <Heading as="h6" fontSize="18px" fontWeight={600} color="text">
                  {user?.name || "N/A"}
                </Heading>

                <Text fontSize="14px" color="text.2">
                  {user?.email || "N/A"}
                </Text>
              </Stack>
            </HStack>

            <UpdateProfileModal>
              <Button
                size="md"
                variant="subtle"
                leftIcon={<Icon name="pen" color="text" />}
                alignSelf="flex-start"
                _hover={{ "& *": { color: "white" } }}
              >
                Edit
              </Button>
            </UpdateProfileModal>
          </HStack>

          <Grid templateColumns={{ sm: "1fr", "1sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)" } as any} gap="8px">
            <ProfileInfoItem
              title="Phone Number"
              description={joinPhoneNumber(user?.phone_prefix || "", user?.phone_number || "")}
              loading={loading_user}
            />
            <ProfileInfoItem title="Gender" description={user?.gender || "N/A"} loading={loading_user} />
            <ProfileInfoItem title="Religion" description={user?.religion || "N/A"} loading={loading_user} />
            <ProfileInfoItem title="Country of Residence" description={user?.residence_country || "N/A"} loading={loading_user} />
            <ProfileInfoItem title="Address" description={user?.residence_address || "N/A"} loading={loading_user} />
            <ProfileInfoItem title="Preferred Language" description={user?.preferred_lan?.join(", ")} loading={loading_user} />
            <ProfileInfoItem title="Practising Since" description={user?.service_start_year || "N/A"} loading={loading_user} />
          </Grid>
        </FieldCard>

        <Outlet />
      </Stack>
    </Container>
  );
}

interface ProfileSubLinkProps extends LinkProps {
  label: string;
  to: string;
}

function ProfileSubLink(props: ProfileSubLinkProps) {
  const { label, to, ...xprops } = props;

  const match = useMatch(to);

  return (
    <Link
      asChild
      color="text.3"
      fontSize="14px"
      fontWeight="400"
      _hover={{ color: "text.2" }}
      focusRingColor="primary"
      css={{
        "&.active": !!match && {
          color: "primary",
          fontWeight: "500",
        },
      }}
      {...xprops}
    >
      <NavLink to={to} viewTransition>
        {label}
      </NavLink>
    </Link>
  );
}

export function ProfileInfoItem(props: DetailItemProps) {
  return (
    <DetailItem
      orientation="vertical"
      _styles={{ description: { fontSize: "14px", color: "text", textTransform: "capitalize" } }}
      {...props}
    />
  );
}
