import { RouteObject } from "react-router";
import { PersonalInfo } from "./views/personal";
import { Publications } from "./views/publications";
import { Certifications } from "./views/certifications";
import { ProfessionalTrainings } from "./views/professional-trainings";
import { Associations } from "./views/associations";
import { DocumentsAndOthers } from "./views/documents-and-others";

export const ProfileRoutes: RouteObject[] = [
  {
    index: true,

    path: "/profile",
    element: <PersonalInfo />,
  },
  {
    path: "/profile/publications",
    element: <Publications />,
  },
  {
    path: "/profile/certifications",
    element: <Certifications />,
  },
  {
    path: "/profile/professional-trainings",
    element: <ProfessionalTrainings />,
  },
  {
    path: "/profile/associations",
    element: <Associations />,
  },
  {
    path: "/profile/docs-and-others",
    element: <DocumentsAndOthers />,
  },
];
