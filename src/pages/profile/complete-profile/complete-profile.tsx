import { Box, Container, Heading, Show, Stack, Steps, Text, VStack } from "@chakra-ui/react";
import { Education } from "./education";
import { ServiceRate } from "./service-rate";
import { useMemo } from "react";
import { useNavigate } from "react-router";
import { startViewTransition } from "@/libs";
import { flushSync } from "react-dom";
import { BgPattern, Button, FancyCheckMark, FieldCard, Icon } from "@/components";
import { PersonalInformation } from "./personal";
import { Availability } from "./availability";
import { SpecialTraining } from "./training";
import { usePartialState, useUser } from "@/hooks";
import { UploadDocument } from "./document";
import { Association } from "./association";
import { Certification } from "./certification";

export function CompleteProfileIndex() {
  const navigate = useNavigate();

  const { data } = useUser();
  // const current_progress = data?.data?.signup_stage?.provider || 0;
  const current_progress = 0;
  const [{ step }, set] = usePartialState({ step: current_progress }, [current_progress]);

  const steps = useMemo(() => {
    const steps = [
      {
        title: "Personal",
        component: PersonalInformation,
      },
      {
        title: "Education",
        component: Education,
      },
      {
        title: "Certification",
        component: Certification,
      },
      {
        title: "Professional Training",
        component: SpecialTraining,
      },
      {
        title: "Upload documents",
        component: UploadDocument,
      },
      {
        title: "Service Offer",
        component: ServiceRate,
      },
      {
        title: "Availability",
        component: Availability,
      },
      {
        title: "Association",
        component: Association,
      },
      // {
      //   title: "Date & Time",
      //   icon: <LuCalendar />,
      //   description: "Book an Appointment",
      //   component: DateAndTime,
      // },
    ];

    //   if (setup_type === "2") {
    //     steps.splice(1, 2);
    //   }

    return steps;
  }, []);

  //   const handleSubmit = (e: SyntheticEvent) => {
  //     e.preventDefault();
  //     form.handleSubmit();
  //   };

  return (
    <VStack py="40px" gap="16px" pos="relative">
      <BgPattern pos="absolute" bg0Props={{ pos: "fixed", left: "calc(--var(sidebar-w))" }} bg1Props={{ pos: "fixed" }} />
      <Steps.Root
        w="100%"
        defaultStep={0}
        count={steps.length}
        size="xs"
        gap="0px"
        step={step}
        onStepChange={(e) =>
          startViewTransition(() =>
            // flushSync would cause a flicker
            // Indicates that you're trying to force React to immediately flush updates while it's already in the middle of rendering.
            // This is problematic because React needs to complete its rendering process before accepting and applying further changes
            // To resolve this, you need to defer the flushSync call until after the current rendering cycle is complete
            queueMicrotask(() => flushSync(() => set({ step: e.step })))
          )
        }
        // as="form"
        // onSubmit={handleSubmit}
      >
        <Steps.Context>
          {(ctx) => (
            <>
              <Show when={ctx.value < ctx.count && steps.length > 1}>
                <Text textAlign="center" fontSize="14px" color="text.2" hideFrom="3sm">
                  {" "}
                  Steps {ctx.value + 1} of {ctx.count}
                </Text>

                <Steps.List
                  w="100%"
                  maxW="480px"
                  alignSelf="center"
                  //   minH="76px"
                  p="16px"
                  hideBelow="3sm"
                  viewTransitionName="step-indicator-list"
                >
                  {steps.map((_, index) => (
                    <Steps.Item key={index} index={index} gap="0" pos="relative">
                      <Steps.Indicator
                        pos="relative"
                        boxSize="16px"
                        bg="stroke.divider"
                        borderWidth="1px"
                        borderColor="transparent"
                        _complete={{
                          p: "4px",
                          bg: "primary",
                        }}
                        css={{
                          "&[data-current]": {
                            bg: "blue.200",
                          },
                          // "&[data-state=complete]": {
                          //   borderColor: "actions.completed",
                          //   bg: "actions.completed",
                          // },
                        }}
                      >
                        <Steps.Status
                          incomplete={<Box boxSize="8px" bg="white" rounded="full" />}
                          complete={<Icon name="step_checked" boxSize="8px" color="white" />}
                        />

                        {/* <Steps.Title
                          pos="absolute"
                          left="50%"
                          top="calc(50% + 16px + 8px)"
                          transform="translate(-50%, -50%)"
                          textWrap="nowrap"
                        >
                          {step.title}
                        </Steps.Title> */}
                      </Steps.Indicator>

                      <Steps.Separator
                        mx="4px"
                        bg="stroke.divider"
                        rounded="full"
                        _complete={{
                          bg: "primary",
                        }}
                      />
                    </Steps.Item>
                  ))}
                </Steps.List>
              </Show>

              {/* <form.AppForm> */}
              {steps.map((step, index) => (
                <Steps.Content key={index} index={index}>
                  {<step.component key={index} />}
                </Steps.Content>
              ))}
              {/* </form.AppForm> */}
              <Steps.CompletedContent>
                <Container maxW="400px" px="0" my="120px">
                  <FieldCard p="32px" _header={{ alignItems: "center" }} _content={{ alignItems: "center" }}>
                    <FancyCheckMark />
                    <Stack gap="4px" alignItems="center">
                      <Heading as="h3" fontSize="24px" fontWeight="700" color="text">
                        Profile Details Completed
                      </Heading>
                      {/* <Text fontSize="14px" color="text.2" textAlign="center">
                        {steps.length > 1
                          ? " You have successfully scheduled an appointment for a member. The member would be notified via email"
                          : "The appointment has been scheduled. The member will be notified via email to complete the booking process."}
                      </Text> */}
                    </Stack>
                  </FieldCard>

                  <VStack py="20px">
                    <Button onClick={() => navigate("/")}>Continue</Button>
                  </VStack>
                </Container>
              </Steps.CompletedContent>
            </>
          )}
        </Steps.Context>
      </Steps.Root>
    </VStack>
  );
}
