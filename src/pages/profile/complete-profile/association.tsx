/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ButtonGroup,
  Container,
  Grid,
  Heading,
  HStack,
  Link,
  Separator,
  Span,
  Stack,
  Steps,
  Text,
  useStepsContext,
  VStack,
} from "@chakra-ui/react";
import { formOptions } from "@tanstack/react-form";
import { SyntheticEvent, useMemo } from "react";
import { Button, FieldCard, Icon, SubmitButton, toaster, useAppForm, withForm } from "@/components";
import { CompleteProfile_AssociationDto, completeProfile_associationSchema } from "@/schemas";
import { useCommonList, useMutation } from "@/hooks";
import { addAssociationMutationOpts } from "@/queries";
import { tryCatch } from "@/utils";
import { CountryListDataRo } from "@/interfaces";

// interface Props {
//   form: AppFormType;
// }

const empty_assoc = {
  country: "",
  name: "",
  state: "",
  member_id: "",
};

const form_opts = formOptions({
  defaultValues: {
    assoc: [empty_assoc],
  } as CompleteProfile_AssociationDto,
});

export function Association() {
  //   const { form } = assocops;

  const { goToNextStep } = useStepsContext();
  const { mutateAsync, isPending: adding } = useMutation(addAssociationMutationOpts());

  const { phoneData } = useCommonList("country-list");
  const { data: country_data, isPending: loading_country_list } = phoneData;
  const country_list = useMemo(
    () =>
      (country_data?.data || []).map((item) => ({
        label: item.name,
        value: item.name.toLowerCase(),
      })),
    [country_data]
  );

  const form = useAppForm({
    ...form_opts,
    validators: {
      onSubmit: completeProfile_associationSchema,
      onChange: completeProfile_associationSchema,
      onMount: completeProfile_associationSchema,
    },
    async onSubmit({ value }) {
      const promise = mutateAsync({ data: value.assoc });
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "Association(s) has been added successfully",
        });
        goToNextStep();
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  // const errors = useStore(form.store, (store) => store.errors);
  // const values = useStore(form.store, (store) => store.values);
  // console.log("Service errors", { values, errors });

  return (
    <form.AppForm>
      <Container as="form" maxW="680px" px="0" onSubmit={handleSubmit}>
        <VStack gap="16px" px="20px">
          <VStack p="20px" gap="4px">
            <Heading as="h3" fontSize="24px" fontWeight={700} color="text" textAlign="center">
              Medical Association
            </Heading>

            <Text fontSize="14px" color="text.2" textAlign="center">
              Tell us where you're licensed to practice. This helps us connect you with the right clients.
            </Text>
          </VStack>

          <FieldCard
            p={{ base: "12px", "1smDown": "12px", "3sm": "24px" } as any}
            rounded={{ base: "8px", "1smDown": "8px", "3sm": "16px" } as any}
          >
            <form.AppField name="assoc" mode="array">
              {(field) =>
                field.state.value.map((_, index) => (
                  <Stack gap="24px">
                    {index > 0 && <Separator />}

                    <AssociationFields
                      form={form}
                      index={index}
                      key={index}
                      country_list={country_list}
                      loading_list={loading_country_list}
                      countries={country_data?.data || []}
                    />

                    <HStack justifyContent="space-between">
                      {field.state.value.length > 1 && (
                        <Link
                          color="actions.canceled"
                          fontSize="14px"
                          fontWeight="600"
                          onClick={() => form.removeFieldValue("assoc", index)}
                        >
                          Remove Association
                        </Link>
                      )}

                      {index === 0 && <Span flex="1" />}

                      {index === field.state.value.length - 1 && (
                        <Button
                          variant="subtle"
                          size="sm"
                          leftIcon={<Icon name="plus" color="text" />}
                          _hover={{ "& *": { color: "white" } }}
                          onClick={() => form.pushFieldValue("assoc", empty_assoc)}
                        >
                          Add New
                        </Button>
                      )}
                    </HStack>
                  </Stack>
                ))
              }
            </form.AppField>
          </FieldCard>
        </VStack>

        <HStack w="100%" justifyContent="center" mt="48px">
          <ButtonGroup size="md" variant="solid" justifyContent="center">
            <Steps.PrevTrigger asChild>
              <Button variant="subtle">Go Back</Button>
            </Steps.PrevTrigger>

            <SubmitButton loading={adding}>Continue</SubmitButton>
          </ButtonGroup>
        </HStack>
      </Container>
    </form.AppForm>
  );
}

const AssociationFields = withForm({
  ...form_opts,
  props: { index: 0, country_list: [] as { label: string; value: string }[], countries: [] as CountryListDataRo[], loading_list: false },
  render: ({ form, index, country_list, countries, loading_list }) => {
    return (
      <Stack gap="24px">
        <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <form.AppField name={`assoc[${index}].country`}>
            {(field) => (
              <field.Select
                label="Select Country"
                placeholder="Select Country"
                field={field}
                loading={loading_list}
                value={[field.state.value]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={country_list}
                itemLabelProps={{ textTransform: "capitalize" }}
                triggerProps={{ textTransform: "capitalize" }}
                canFilterList
              />
            )}
          </form.AppField>

          <form.Subscribe selector={(state) => state.values.assoc[index].country}>
            {(country) => {
              const states = countries.find((item) => item.name.toLowerCase() === country)?.states || [];
              const state_list = states.map((item) => ({
                label: item.name,
                value: item.name.toLowerCase(),
              }));

              return (
                <form.AppField name={`assoc[${index}].state`}>
                  {(field) => (
                    <field.Select
                      label="Select State"
                      placeholder="Select State"
                      field={field}
                      loading={loading_list}
                      value={[field.state.value]}
                      onBlur={field.handleBlur}
                      onValueChange={(e) => field.setValue(e.value[0])}
                      items={state_list}
                      itemLabelProps={{ textTransform: "capitalize" }}
                      triggerProps={{ textTransform: "capitalize" }}
                      disabled={!country}
                      canFilterList
                    />
                  )}
                </form.AppField>
              );
            }}
          </form.Subscribe>
        </Grid>

        <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <form.AppField name={`assoc[${index}].name`}>
            {(field) => (
              <field.TextField
                label="Name of Association/Licensing Board"
                // type="number"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`assoc[${index}].member_id`}>
            {(field) => (
              <field.TextField
                label="Member ID or License ID"
                // type="number"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
        </Grid>
      </Stack>
    );
  },
});
