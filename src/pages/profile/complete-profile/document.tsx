/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, ConfirmationModal, Empty, FieldCard, Icon, toaster } from "@/components";
import { ButtonGroup, Container, Grid, Heading, HStack, Link, Show, Skeleton, Spinner, Stack, Steps, Text, VStack } from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { deleteUploadedDocumentMutationOpts, getUploadedDocumentsQueryOpts } from "@/queries";
import { ProviderDocumentDataRo } from "@/interfaces";
import { useMutation, useUser } from "@/hooks";
import { tryCatch } from "@/utils";
import { UploadDocumentModal } from "../ui/upload-document";

export function UploadDocument() {
  const { data: user_data } = useUser();
  const { data, isPending: loading } = useQuery(getUploadedDocumentsQueryOpts());
  const list = data?.data || [];
  const is_empty = list.length < 1;

  const is_acct_approved = user_data?.data?.status === 1;
  // console.log("Training list", data);

  //   console.log("Docs", docs);

  return (
    <Container maxW="680px" px="0">
      <VStack gap="16px" p="20px">
        <VStack p="20px" gap="4px">
          <Heading as="h3" fontSize="24px" fontWeight={700} color="text" textAlign="center">
            Upload Your Credentials
          </Heading>

          <Text fontSize="14px" color="text.2" textAlign="center">
            Share proof of qualification and licensing. This keeps our platform secure and builds client trust.
          </Text>
        </VStack>

        <FieldCard
          p={{ base: "12px", "1smDown": "12px", "3sm": "24px" } as any}
          rounded={{ base: "8px", "1smDown": "8px", "3sm": "16px" } as any}
        >
          <HStack justifyContent="space-between">
            <Text fontSize="14px" fontWeight={600} color="text">
              Document(s)
            </Text>

            <UploadDocumentModal>
              <Button variant="subtle" size="sm" leftIcon={<Icon name="plus" color="text" />} _hover={{ "& *": { color: "white" } }}>
                Add
              </Button>
            </UploadDocumentModal>
          </HStack>

          <Stack gap="24px">
            <Show when={loading}>
              <Stack gap="16px">
                <FileUploadItem item={{} as any} loading />
                <FileUploadItem item={{} as any} loading />
                <FileUploadItem item={{} as any} loading />
              </Stack>
            </Show>

            <Show when={!is_empty && !loading}>
              <Stack gap="16px">
                {list.map((item) => (
                  <FileUploadItem key={item.document_id} item={item} is_acct_approved={is_acct_approved} />
                ))}
              </Stack>
            </Show>

            <Show when={is_empty && !loading}>
              <VStack my="10vh">
                <Empty subtitle="No Credentials Added Yet" illustrationProps={{ boxSize: "54px" }} />
              </VStack>
            </Show>
          </Stack>
        </FieldCard>
      </VStack>

      <HStack w="100%" justifyContent="center" mt="48px">
        <ButtonGroup size="md" variant="solid" justifyContent="center">
          <Steps.PrevTrigger asChild>
            <Button
              variant="subtle"
              // onClick={() => navigate(-1)}
              // disabled={creating}
            >
              Go back
            </Button>
          </Steps.PrevTrigger>
          <Steps.NextTrigger asChild disabled={is_empty}>
            <Button>Continue</Button>
          </Steps.NextTrigger>
        </ButtonGroup>
      </HStack>
    </Container>
  );
}

function FileUploadItem(props: { item: ProviderDocumentDataRo; loading?: boolean; is_acct_approved?: boolean }) {
  const { item, loading = false, is_acct_approved = false } = props;

  const { mutateAsync, isPending: deleting } = useMutation(deleteUploadedDocumentMutationOpts(item.document_id));

  const handleDelete = async () => {
    const promise = mutateAsync({});
    const result = await tryCatch(promise);

    if (result.ok) {
      toaster.success({
        title: "Success",
        description: "Document has been deleted successfully",
      });
    }
  };

  return (
    <Skeleton variant="shine" loading={loading}>
      <Stack>
        <Text fontSize="14px" color="text.2">
          {item?.document_type || "N/A"}
        </Text>

        <Grid
          templateColumns={"1fr auto"}
          gap="8px"
          bg="input"
          border="1px solid"
          borderColor="stroke.divider"
          rounded="4px"
          p="12px"
          alignItems="center"
        >
          <HStack>
            <Icon name="file" />
            <Text fontSize="14px" fontWeight={500} color="text">
              File
            </Text>
          </HStack>

          {!is_acct_approved && (
            <ConfirmationModal
              title="Delete Document"
              description="This action will delete this document and is irreversible"
              loading={deleting}
              onConfirm={async () => {
                await handleDelete();
              }}
            >
              {/* <IconButton
              size="sm"
              variant="plain"
              aria-label="delete professional training"
              loading={deleting}
              _hover={{ "--before-bg": "colors.red.500", "& *": { color: "white" } }}
            >
              <Icon name="trash" boxSize="14px" color="text.2" />
            </IconButton> */}

              <Link
                color="red.500"
                pointerEvents={deleting ? "none" : "auto"}
                opacity={deleting ? 0.5 : 1}
                cursor={deleting ? "not-allowed" : "pointer"}
              >
                {deleting && (
                  <>
                    <Spinner boxSize="12px" /> Deleting...
                  </>
                )}
                {!deleting && (
                  <>
                    <Icon name="trash" boxSize="16px" color="inherit" />
                    Delete
                  </>
                )}
              </Link>
            </ConfirmationModal>
          )}
        </Grid>
      </Stack>
    </Skeleton>
  );
}
