/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ButtonGroup,
  Container,
  Grid,
  Heading,
  HStack,
  Link,
  Separator,
  Span,
  Stack,
  Steps,
  Text,
  useStepsContext,
  VStack,
} from "@chakra-ui/react";
import { formOptions, useStore } from "@tanstack/react-form";
import { SyntheticEvent, useMemo } from "react";
import { Button, FieldCard, Icon, ShowFieldWhen, SubmitButton, toaster, useAppForm, withForm } from "@/components";
import { AddCertificationDto, CompleteProfile_CertificationDto, completeProfile_certificationSchema } from "@/schemas";
import { useCommonList, useMutation } from "@/hooks";
import { addCertificationMutationOpts } from "@/queries";
import { tryCatch } from "@/utils";
import { CountryListDataRo } from "@/interfaces";

// interface Props {
//   form: AppFormType;
// }

const empty_cert = {
  cert_name: "",
  cert_year: "",
  cert_number: "",
  country: "",
  state: "",
};

const form_opts = formOptions({
  defaultValues: {
    cert: [empty_cert],
  } as CompleteProfile_CertificationDto,
});

export function Certification() {
  //   const { form } = assocops;

  const { goToNextStep } = useStepsContext();
  const { mutateAsync, isPending: adding } = useMutation(addCertificationMutationOpts());

  const { phoneData } = useCommonList("country-list");
  const { data: cert_data, isPending: loading_cert_list } = useCommonList("provider-certification");
  // console.log("Cert list data", cert_data);

  const { data: country_data, isPending: loading_country_list } = phoneData;
  const countries = useMemo(() => country_data?.data || [], [country_data]);

  const country_list = useMemo(
    () =>
      (countries || []).map((item) => ({
        label: item.name,
        value: item.name.toLowerCase(),
      })),
    [countries]
  );

  const cert_list = useMemo(
    () =>
      (cert_data?.data || []).map((item) => ({
        label: item,
        value: item,
      })),
    [cert_data]
  );

  // const defaultValues: AddCertificationDto = {
  //     country: addedCertification?.country || "",
  //     cert_name: addedCertification?.cert_name || "",
  //     cert_year: (addedCertification?.cert_year || "").toString(),
  //     cert_number: addedCertification?.cert_number || "",
  //     state: addedCertification?.state || "",
  //   };

  const form = useAppForm({
    ...form_opts,
    validators: {
      onSubmit: completeProfile_certificationSchema,
      onChange: completeProfile_certificationSchema,
      onMount: completeProfile_certificationSchema,
    },
    async onSubmit({ value }) {
      const promise = mutateAsync({ data: value.cert });
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "Cerification(s) has been added successfully",
        });
        goToNextStep();
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  const errors = useStore(form.store, (store) => store.errors);
  const values = useStore(form.store, (store) => store.values);
  console.log("Certification errors", { values, errors });

  return (
    <form.AppForm>
      <Container as="form" maxW="680px" px="0" onSubmit={handleSubmit}>
        <VStack gap="16px" px="20px">
          <VStack p="20px" gap="4px">
            <Heading as="h3" fontSize="24px" fontWeight={700} color="text" textAlign="center">
              Cerification
            </Heading>

            <Text fontSize="14px" color="text.2" textAlign="center">
              Provide your license information to ensure we link you to the right opportunities.
            </Text>
          </VStack>

          <FieldCard
            p={{ base: "12px", "1smDown": "12px", "3sm": "24px" } as any}
            rounded={{ base: "8px", "1smDown": "8px", "3sm": "16px" } as any}
          >
            <form.AppField name="cert" mode="array">
              {(field) =>
                field.state.value.map((_, index) => (
                  <Stack gap="24px">
                    {index > 0 && <Separator />}

                    <CerificationFields
                      form={form}
                      index={index}
                      key={index}
                      cert_list={cert_list}
                      country_list={country_list}
                      countries={countries}
                      loading_list={loading_country_list}
                      loading_cert_list={loading_cert_list}
                    />

                    <HStack justifyContent="space-between">
                      {field.state.value.length > 1 && (
                        <Link
                          color="actions.canceled"
                          fontSize="14px"
                          fontWeight="600"
                          onClick={() => form.removeFieldValue("cert", index)}
                        >
                          Remove Certification
                        </Link>
                      )}

                      {index === 0 && <Span flex="1" />}

                      {index === field.state.value.length - 1 && (
                        <Button
                          variant="subtle"
                          size="sm"
                          leftIcon={<Icon name="plus" color="text" />}
                          _hover={{ "& *": { color: "white" } }}
                          onClick={() => form.pushFieldValue("cert", empty_cert)}
                        >
                          Add New
                        </Button>
                      )}
                    </HStack>
                  </Stack>
                ))
              }
            </form.AppField>
          </FieldCard>
        </VStack>

        <HStack w="100%" justifyContent="center" mt="48px">
          <ButtonGroup size="md" variant="solid" justifyContent="center">
            <Steps.PrevTrigger asChild>
              <Button variant="subtle">Go Back</Button>
            </Steps.PrevTrigger>

            <SubmitButton loading={adding}>Continue</SubmitButton>
          </ButtonGroup>
        </HStack>
      </Container>
    </form.AppForm>
  );
}

type SelectItemType = {
  label: string;
  value: string;
}[];

const CerificationFields = withForm({
  ...form_opts,
  props: {
    index: 0,
    cert_list: [] as SelectItemType,
    countries: [] as CountryListDataRo[],
    country_list: [] as SelectItemType,
    loading_list: false,
    loading_cert_list: false,
  },
  render: ({ form, index, cert_list, country_list, countries, loading_list, loading_cert_list }) => {
    return (
      <Stack gap="24px">
        <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <form.AppField name={`cert[${index}].country`}>
            {(field) => (
              <field.Select
                label="Select Country"
                field={field}
                placeholder="Select Country"
                loading={loading_list}
                value={[field.state.value]}
                onBlur={field.handleBlur}
                onValueChange={(e) => {
                  const country = e.value[0];
                  form.setFieldValue(`cert[${index}].country`, country);
                  form.setFieldValue(`cert[${index}].state`, "");
                }}
                items={country_list}
                itemLabelProps={{ textTransform: "capitalize" }}
                triggerProps={{ textTransform: "capitalize" }}
                canFilterList
              />
            )}
          </form.AppField>

          <form.AppField name={`cert[${index}].cert_name`}>
            {(field) => (
              <field.Select
                label="Select Certification"
                field={field}
                placeholder="Select Certification"
                loading={loading_cert_list}
                value={[field.state.value]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={cert_list}
                // itemLabelProps={{ textTransform: "capitalize" }}
                // triggerProps={{ textTransform: "capitalize" }}
              />
            )}
          </form.AppField>
        </Grid>

        <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <form.AppField name={`cert[${index}].cert_number`}>
            {(field) => (
              <field.TextField
                label="Certification Number"
                // type="number"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`cert[${index}].cert_year`}>
            {(field) => (
              <field.TextField
                label="Cerification Year"
                type="number"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
                minLength={4}
                maxLength={4}
              />
            )}
          </form.AppField>
        </Grid>

        <Grid templateColumns="repeat(2, 1fr)" gap="16px">
          <ShowFieldWhen when={(state) => (state.cert as AddCertificationDto[])?.[index]?.country === "united states"}>
            <form.Subscribe selector={(state) => state.values.cert?.[index]?.country}>
              {(country) => {
                const states = countries.find((item) => item.name.toLowerCase() === country)?.states || [];
                const state_list = states.map((item) => ({
                  label: item.name,
                  value: item.name.toLowerCase(),
                }));

                return (
                  <form.AppField name={`cert[${index}].state`}>
                    {(field) => (
                      <field.Select
                        label="Select State"
                        placeholder="Select State"
                        field={field}
                        loading={loading_list}
                        value={[field.state.value || ""]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        items={state_list}
                        itemLabelProps={{ textTransform: "capitalize" }}
                        triggerProps={{ textTransform: "capitalize" }}
                        disabled={!country}
                        canFilterList
                      />
                    )}
                  </form.AppField>
                );
              }}
            </form.Subscribe>
          </ShowFieldWhen>
        </Grid>
      </Stack>
    );
  },
});
