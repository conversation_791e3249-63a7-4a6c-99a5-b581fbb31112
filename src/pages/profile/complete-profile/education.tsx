/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON>, FieldCard, Icon, SubmitButton, toaster, useAppForm, withForm } from "@/components";
import { useCommonList, useMutation } from "@/hooks";
import { addEducationMutationOpts } from "@/queries";
import { CompleteProfile_EducationSchema, completeProfile_educationSchema } from "@/schemas";
import { tryCatch } from "@/utils";
import {
  ButtonGroup,
  Container,
  Grid,
  Heading,
  HStack,
  Link,
  Separator,
  Span,
  Stack,
  Steps,
  Text,
  useStepsContext,
  VStack,
} from "@chakra-ui/react";
import { formOptions } from "@tanstack/react-form";
import { SyntheticEvent } from "react";

// interface Props {
//   form: AppFormType;
// }

const empty_edu = {
  level: "",
  name: "",
  location: "",
  degree: "",
  year: "",
};

const form_opts = formOptions({
  defaultValues: {
    edu: [empty_edu],
  } as CompleteProfile_EducationSchema,
});

export function Education() {
  //   const { form } = props;

  const { goToNextStep } = useStepsContext();

  //   const navigate = useNavigate();
  const { data, isPending: loading_level_list } = useCommonList("education-level");
  const level_list = (data?.data || []).map((item) => ({
    label: item,
    value: item,
  }));

  console.log("Levels", level_list);
  const { mutateAsync, isPending: adding } = useMutation(addEducationMutationOpts());

  const form = useAppForm({
    ...form_opts,
    validators: {
      onSubmit: completeProfile_educationSchema,
      onChange: completeProfile_educationSchema,
      onMount: completeProfile_educationSchema,
    },
    async onSubmit({ value }) {
      console.log("Complete profile value", value);

      const promise = mutateAsync({ data: value.edu });
      const result = await tryCatch(promise);
      if (result.ok) {
        // navigate("/profile/complete-profile/service-rate", { viewTransition: true });
        toaster.success({
          title: "Success",
          description: "Education has been added successfully",
        });
        goToNextStep();
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <form.AppForm>
      <Container as="form" maxW="680px" px="0" onSubmit={handleSubmit}>
        <VStack gap="16px" px="20px">
          <VStack p="20px" gap="4px">
            <Heading as="h3" fontSize="24px" fontWeight={700} color="text" textAlign="center">
              Education
            </Heading>

            <Text fontSize="14px" color="text.2" textAlign="center">
              Help clients trust your expertise by sharing your education background
            </Text>
          </VStack>

          <FieldCard
            p={{ base: "12px", "1smDown": "12px", "3sm": "24px" } as any}
            rounded={{ base: "8px", "1smDown": "8px", "3sm": "16px" } as any}
          >
            <form.AppField name="edu" mode="array">
              {(field) =>
                field.state.value.map((_, index) => (
                  <Stack gap="24px">
                    {index > 0 && <Separator />}

                    <EducationFields
                      form={form}
                      index={index}
                      key={index}
                      level_list={level_list}
                      loading_level_list={loading_level_list}
                    />

                    <HStack justifyContent="space-between">
                      {field.state.value.length > 1 && (
                        <Link color="actions.canceled" fontSize="14px" fontWeight="600" onClick={() => form.removeFieldValue("edu", index)}>
                          Remove Education
                        </Link>
                      )}

                      {index === 0 && <Span flex="1" />}

                      {index === field.state.value.length - 1 && (
                        <Button
                          variant="subtle"
                          size="sm"
                          leftIcon={<Icon name="plus" color="text" />}
                          _hover={{ "& *": { color: "white" } }}
                          onClick={() => form.pushFieldValue("edu", empty_edu)}
                        >
                          Add New
                        </Button>
                      )}
                    </HStack>
                  </Stack>
                ))
              }
            </form.AppField>
          </FieldCard>
        </VStack>

        <HStack w="100%" justifyContent="center" mt="48px">
          <ButtonGroup size="md" variant="solid" justifyContent="center">
            <Steps.PrevTrigger asChild>
              <Button
                variant="subtle"
                // onClick={() => navigate(-1)}
                // disabled={creating}
              >
                Go back
              </Button>
            </Steps.PrevTrigger>
            {/* <Steps.NextTrigger asChild>
              <Button>Set your Service Rates</Button>
            </Steps.NextTrigger> */}

            <SubmitButton loading={adding}>Continue</SubmitButton>
          </ButtonGroup>
        </HStack>
      </Container>
    </form.AppForm>
  );
}

const EducationFields = withForm({
  ...form_opts,
  props: { index: 0, level_list: [] as { label: string; value: string }[], loading_level_list: false },
  render: ({ form, index, level_list, loading_level_list }) => {
    return (
      <>
        <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <form.AppField name={`edu[${index}].level`}>
            {(field) => (
              <field.Select
                label="Education level"
                placeholder="Select education level"
                loading={loading_level_list}
                field={field}
                value={[field.state.value || ""]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={level_list}
              />
            )}
          </form.AppField>
          <form.AppField name={`edu[${index}].name`}>
            {(field) => (
              <field.TextField
                label="School Name"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
        </Grid>

        <form.AppField name={`edu[${index}].location`}>
          {(field) => (
            <field.TextField
              label="School Address"
              field={field}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.setValue(e.target.value)}
            />
          )}
        </form.AppField>

        <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <form.AppField name={`edu[${index}].degree`}>
            {(field) => (
              <field.TextField
                label="Degree Obtained"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`edu[${index}].year`}>
            {(field) => (
              <field.TextField
                label="Year Obtained"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
        </Grid>
      </>
    );
  },
});
