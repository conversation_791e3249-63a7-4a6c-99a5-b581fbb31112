/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, toaster, useAppForm } from "@/components";
import { useCommonList, useMutation, useUser } from "@/hooks";
import { updateProfileInfoMutationOpts, uploadAvatarMutationOpts } from "@/queries";
import { CompleteProfile_PersonalInfoDto, completeProfile_personalInfoSchema } from "@/schemas";
import { getYearsOptions, tryCatch } from "@/utils";
import { ButtonGroup, Container, Grid, Heading, HStack, Show, Text, useStepsContext, VStack } from "@chakra-ui/react";
import { useStore } from "@tanstack/react-form";
import { SyntheticEvent, useMemo } from "react";
import { useNavigate } from "react-router";

// interface Props {
//   form: AppFormType;
// }

export function PersonalInformation() {
  //   const { form } = props;

  const navigate = useNavigate();

  const { goToNextStep } = useStepsContext();

  const { data: user_data } = useUser();
  const user = user_data?.data;
  const has_specialty = !!user?.service_cat_id && Boolean(user?.service_start_year);

  const { phoneData } = useCommonList("country-list");
  const { data: rg_data, isPending: loading_rg } = useCommonList("religion-list");
  const { data: ms_data, isPending: loading_ms } = useCommonList("marital-status");

  const { data: pl_data, isPending: loading_pl } = useCommonList("preferred-lan");
  const { data: sc_data, isPending: loading_sc } = useCommonList("service-category", !has_specialty);
  // const loading = false;

  const { data: country_data, isPending: loading_country_list } = phoneData;
  // const { data: user_data, isPending: loading_user } = useQuery(getUserProfileQueryOpts());
  const { mutateAsync, isPending: updating_profile } = useMutation(updateProfileInfoMutationOpts());
  const { mutateAsync: uploadAvatar, isPending: uploading_avatar } = useMutation(uploadAvatarMutationOpts(true));

  const updating = updating_profile || uploading_avatar;

  const countries = useMemo(() => country_data?.data || [], [country_data]);

  const country_list = useMemo(
    () =>
      (country_data?.data || []).map((item) => ({
        label: item.name,
        value: item.name.toLowerCase(),
      })),
    [country_data]
  );

  const pl_list = (pl_data?.data || []).map((item) => ({
    label: item,
    value: item,
  }));

  const rg_list = (rg_data?.data || []).map((item) => ({
    label: item,
    value: item,
  }));

  const ms_list = (ms_data?.data || []).map((item) => ({
    label: item,
    value: item,
  }));

  const sc_list = (sc_data?.data || []).map((item) => ({
    label: item?.name,
    value: item?.service_cat_id,
  }));

  const service_start_years = useMemo(() => getYearsOptions(), []);

  const form = useAppForm({
    defaultValues: {
      phone_number: "",
      phone_prefix: "234",
      residence_address: "",
      residence_country: "",
      residence_state: "",
      martial_status: "",
      service_cat_id: user?.service_cat_id || "",

      service_start_year: "",
      preferred_lan: [],
      religion: "",
      avatar_file: null,

      contact_person: {
        name: "",
        email: "",
        phone_number: "",
        phone_prefix: "234",
        job_title: "",
      },
    } as CompleteProfile_PersonalInfoDto,
    validators: {
      onSubmit: completeProfile_personalInfoSchema,
      onChange: completeProfile_personalInfoSchema,
      onMount: completeProfile_personalInfoSchema,
    },
    async onSubmit({ value }) {
      console.log("Complete profile value", value);

      if (value.avatar_file) {
        const promise = uploadAvatar({ file: value.avatar_file });
        const result = await tryCatch(promise);
        if (!result.ok) {
          toaster.error({
            title: "Error",
            description: "Failed to upload profile picture",
          });
          return;
        }
      }

      const promise = mutateAsync(value);
      const result = await tryCatch(promise);

      if (result.ok) {
        // navigate("/profile/complete-profile/education", { viewTransition: true });
        toaster.success({
          title: "Success",
          description: "Profile has been updated successfully",
        });

        goToNextStep();
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  const errors = useStore(form.store, (store) => store.errors);
  console.log("Complete profile", errors);

  return (
    <form.AppForm>
      <Container as="form" maxW="680px" px="0" onSubmit={handleSubmit}>
        <VStack gap="16px" px="20px">
          <VStack p="20px" gap="4px" textAlign="center">
            <Heading as="h3" fontSize="24px" fontWeight={700} color="text" textAlign="center">
              Profile
            </Heading>

            <Text fontSize="14px" color="text.2" textAlign="center">
              Enter your personal details and primary contact information so clients and organizations can reach you easily.
            </Text>
          </VStack>

          <form.AppField name="avatar_file">
            {(field) => (
              <field.AvatarUpload
                field={field}
                //   src={user?.avatar}
                //   name={user?.name}
                accept="image/png, image/jpeg"
                btnText="Change Profile Picture"
                onFileAccept={(e) => field.setValue(e.files[0])}
              />
            )}
          </form.AppField>

          <FieldCard
            p={{ base: "12px", "1smDown": "12px", "3sm": "24px" } as any}
            rounded={{ base: "8px", "1smDown": "8px", "3sm": "16px" } as any}
          >
            <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
              <form.AppField name="residence_country">
                {(field) => (
                  <field.Select
                    label="Country"
                    placeholder="Select Country"
                    field={field}
                    loading={loading_country_list}
                    value={[field.state.value]}
                    onBlur={field.handleBlur}
                    onValueChange={(e) => {
                      const country = e.value?.[0] ?? "";
                      // Set the country value
                      field.setValue(e.value[0]);
                      // Reset state when country changes
                      form.setFieldValue("residence_state", "");

                      const phone_prefix =
                        countries.find((item) => item.name.toLowerCase() === country)?.phone_code || form.state.values.phone_prefix;
                      form.setFieldValue("phone_prefix", phone_prefix);
                    }}
                    items={country_list}
                    canFilterList
                  />
                )}
              </form.AppField>

              <form.Subscribe selector={(state) => state.values.residence_country}>
                {(country) => {
                  const states = countries.find((item) => item.name.toLowerCase() === country)?.states || [];
                  const state_list = states.map((item) => ({
                    label: item.name,
                    value: item.name.toLowerCase(),
                  }));

                  return (
                    <form.AppField name={`residence_state`}>
                      {(field) => (
                        <field.Select
                          label="State"
                          placeholder="Select State"
                          field={field}
                          value={[field.state.value]}
                          onBlur={field.handleBlur}
                          onValueChange={(e) => field.setValue(e.value[0])}
                          items={state_list}
                          itemLabelProps={{ textTransform: "capitalize" }}
                          triggerProps={{ textTransform: "capitalize" }}
                          disabled={!country}
                          canFilterList
                        />
                      )}
                    </form.AppField>
                  );
                }}
              </form.Subscribe>
            </Grid>

            <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
              <form.AppField name="phone_number">
                {(field) => (
                  <field.PhoneNumberField
                    field={field}
                    label="Phone number"
                    value={field.state.value}
                    onValueChange={(e) => field.setValue(e)}
                    defaultPhoneCode={form.state.values?.phone_prefix}
                    onPhoneCodeChange={(e) => form.setFieldValue("phone_prefix", e)}
                  />
                )}
              </form.AppField>

              <form.AppField name="martial_status">
                {(field) => (
                  <field.Select
                    label="⁠Marital Status (optional)"
                    placeholder="Select Marital Status"
                    field={field}
                    loading={loading_ms}
                    value={[field.state.value || ""]}
                    onBlur={field.handleBlur}
                    onValueChange={(e) => field.setValue(e.value[0])}
                    items={ms_list}
                    itemLabelProps={{ textTransform: "capitalize" }}
                    triggerProps={{ textTransform: "capitalize" }}
                  />
                )}
              </form.AppField>
            </Grid>

            <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
              <form.AppField name="preferred_lan">
                {(field) => (
                  <field.Select
                    multiple
                    label="⁠⁠Preferred language (Multiple select)"
                    placeholder="Select preferred languages"
                    field={field}
                    loading={loading_pl}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onValueChange={(e) => field.setValue(e.value)}
                    items={pl_list}
                    itemLabelProps={{ textTransform: "capitalize" }}
                    triggerProps={{ textTransform: "capitalize" }}
                  />
                )}
              </form.AppField>

              <form.AppField name="religion">
                {(field) => (
                  <field.Select
                    label="Religion (optional)"
                    placeholder="Select Religion"
                    field={field}
                    loading={loading_rg}
                    value={[field.state.value || ""]}
                    onBlur={field.handleBlur}
                    onValueChange={(e) => field.setValue(e.value[0])}
                    items={rg_list}
                    itemLabelProps={{ textTransform: "capitalize" }}
                    triggerProps={{ textTransform: "capitalize" }}
                  />
                )}
              </form.AppField>
            </Grid>

            <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
              <form.AppField name="service_start_year">
                {(field) => (
                  <field.Select
                    label="Practising Since"
                    field={field}
                    placeholder="Select year"
                    value={[field.state.value || ""]}
                    onBlur={field.handleBlur}
                    onValueChange={(e) => field.setValue(e.value[0])}
                    items={service_start_years}
                  />
                )}
              </form.AppField>

              <form.AppField name="residence_address">
                {(field) => (
                  <field.TextField
                    label="Address"
                    field={field}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    // endElement={<Icon name="house_address" />}
                    onChange={(e) => field.setValue(e.target.value)}
                  />
                )}
              </form.AppField>

              <Show when={!user?.service_cat_id}>
                <form.AppField name="service_cat_id">
                  {(field) => (
                    <field.Select
                      label="Specialty area"
                      field={field}
                      loading={loading_sc}
                      placeholder="Select specialty area"
                      value={[field.state.value]}
                      items={sc_list}
                      onBlur={field.handleBlur}
                      onValueChange={(e) => field.setValue(e.value[0])}
                      itemLabelProps={{ textTransform: "capitalize" }}
                      triggerProps={{ textTransform: "capitalize" }}
                    />
                  )}
                </form.AppField>
              </Show>
            </Grid>
            {/* </FieldCard>
        <FieldCard  p={{ sm: "12px", "3sm": "24px" } as any} rounded={{sm: "8px", "3sm": "16px"} as any} gap="20px"> */}
            {/* <Separator />
            <Stack gap="4px">
              <Heading as="h6" fontSize="14px" fontWeight={600} color="primary" textTransform="uppercase">
                Contact Person details
              </Heading>

              
            </Stack> */}

            {/* <Grid templateColumns={{ sm: "1fr", "3sm": "repeat(2, 1fr)" } as any} gap="16px">
              <form.AppField name="contact_person.name">
                {(field) => (
                  <field.TextField
                    field={field}
                    label="Full Name"
                    value={field.state.value}
                    onChange={(e) => field.setValue(e.target.value)}
                  />
                )}
              </form.AppField>

              <form.AppField name="contact_person.phone_number">
                {(field) => (
                  <field.PhoneNumberField
                    field={field}
                    label="Phone number"
                    value={field.state.value}
                    onValueChange={(e) => field.setValue(e)}
                    defaultPhoneCode={form.state.values?.contact_person?.phone_prefix}
                    onPhoneCodeChange={(e) => form.setFieldValue("contact_person.phone_prefix", e)}
                  />
                )}
              </form.AppField>
            </Grid>

            <Grid templateColumns={{ sm: "1fr", "3sm": "repeat(2, 1fr)" } as any} gap="16px">
              <form.AppField name="contact_person.email">
                {(field) => (
                  <field.TextField
                    type="email"
                    field={field}
                    label="Email"
                    value={field.state.value}
                    onChange={(e) => field.setValue(e.target.value.trim())}
                  />
                )}
              </form.AppField>

              <form.AppField name="contact_person.job_title">
                {(field) => (
                  <field.TextField
                    field={field}
                    label="Job Title"
                    value={field.state.value}
                    onChange={(e) => field.setValue(e.target.value.trim())}
                  />
                )}
              </form.AppField>
            </Grid> */}

            {/* <Grid templateColumns={{ sm: "1fr", "3sm": "repeat(2, 1fr)" } as any} gap="16px">
            <TextField label="Job Title" endElement={<Icon name="number" />} />
          </Grid> */}
          </FieldCard>
        </VStack>

        <HStack w="100%" justifyContent="center" mt="48px">
          <ButtonGroup size="md" variant="solid" justifyContent="center">
            {/* <Steps.PrevTrigger asChild> */}
            <Button
              variant="subtle"
              onClick={() => navigate(-1)}
              // disabled={creating}
            >
              Cancel
            </Button>
            {/* </Steps.PrevTrigger> */}
            {/* <Steps.NextTrigger asChild>
            <Button>Setup Education</Button>
          </Steps.NextTrigger> */}

            <SubmitButton loading={updating}>Continue</SubmitButton>
          </ButtonGroup>
        </HStack>
      </Container>
    </form.AppForm>
  );
}
