/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ButtonGroup,
  Container,
  Grid,
  Heading,
  HStack,
  Link,
  Separator,
  Show,
  Skeleton,
  Span,
  Stack,
  Steps,
  Text,
  useStepsContext,
  VStack,
} from "@chakra-ui/react";
import { formOptions } from "@tanstack/react-form";
import { SyntheticEvent, useMemo } from "react";
import { Button, DeleteItem, FieldCard, Icon, SubmitButton, toaster, useAppForm, withForm } from "@/components";
import { CompleteProfile_ServiceRateDto, completeProfile_serviceRateSchema } from "@/schemas";
import { useCommonList, useMutation, useUser } from "@/hooks";
import { addServiceOfferMutationOpts, deleteServiceRateMutationOpts, getServiceRatesQueryOpts } from "@/queries";
import { tryCatch } from "@/utils";
import { useQuery } from "@tanstack/react-query";
import omitBy from "lodash.omitby";

// interface Props {
//   form: AppFormType;
// }

const empty_sr = {
  service_offer_id: "",
  amount: "",
};

const form_opts = formOptions({
  defaultValues: {
    sr: [empty_sr],
  } as CompleteProfile_ServiceRateDto,
});

export function ServiceRate() {
  //   const { form } = props;

  const { goToNextStep } = useStepsContext();

  const { data: user_data } = useUser();

  const { data, isPending: loading_rates } = useQuery(getServiceRatesQueryOpts());
  const { mutateAsync, isPending: adding } = useMutation(addServiceOfferMutationOpts());

  const { data: sc } = useCommonList("service-category");

  const rates = useMemo(() => data?.data || [], [data]);
  // const is_empty = useMemo(() => rates.length < 1, [rates]);

  // console.log("Service cat", sc);
  const categories = useMemo(() => sc?.data || [], [sc]);
  const user_service_cat_id = user_data?.data?.service_cat_id;
  const cat_service_list = useMemo(
    () => categories.find((cat) => cat?.service_cat_id === user_service_cat_id)?.service_data || [],
    [categories, user_service_cat_id]
  );

  const filtered_list = useMemo(
    () => Object.values(omitBy(cat_service_list, (item) => rates?.some((rate) => rate.service_offer_id === item.service_offer_id))),
    [cat_service_list, rates]
  );

  const service_list = filtered_list.map((item) => ({
    label: item.name,
    value: item.service_offer_id,
    description: item.description,
  }));

  const defaultValues = useMemo(() => {
    if (rates.length < 1) return form_opts.defaultValues;

    const values = (rates || []).map((rate) => ({
      service_offer_id: rate?.service_offer_id,
      amount: rate?.amount.toString(),
      service_offer_name: rate?.service_offer_name,
    }));

    return {
      sr: values,
    };
  }, [rates]);

  const form = useAppForm({
    ...form_opts,
    defaultValues,

    validators: {
      onSubmit: completeProfile_serviceRateSchema,
      onChange: completeProfile_serviceRateSchema,
      onMount: completeProfile_serviceRateSchema,
    },
    async onSubmit({ value }) {
      // console.log("Service rate value", value);
      const promise = mutateAsync({ data: value.sr });
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "Service rate(s) has been added successfully",
        });
        goToNextStep();
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  // const errors = useStore(form.store, (store) => store.errors);
  // const values = useStore(form.store, (store) => store.values);
  // console.log("Service errors", { values, errors });

  return (
    <form.AppForm>
      <Container as="form" maxW="680px" px="0" onSubmit={handleSubmit}>
        <VStack gap="16px" px="20px">
          <VStack p="20px" gap="4px" textAlign="center">
            <Heading as="h3" fontSize="24px" fontWeight={700} color="text">
              Service Rate
            </Heading>

            <Text fontSize="14px" color="text.2" textAlign="center">
              Please add your rates to your services. Note that without specifying this, we will not be able to match a member to you. Rates
              are specified on a per-hour basis.
            </Text>
          </VStack>

          <FieldCard
            p={{ base: "12px", "1smDown": "12px", "3sm": "24px" } as any}
            rounded={{ base: "8px", "1smDown": "8px", "3sm": "16px" } as any}
          >
            <Show when={loading_rates}>
              <Grid templateColumns={{ sm: "1fr", "3sm": "repeat(2, 1fr)" } as any} gap="24px">
                {Array.from({ length: 4 })
                  .fill(0)
                  .map((_, i) => (
                    <ServiceRateFields
                      key={`${i}-loading`}
                      form={form as any}
                      index={0}
                      id={""}
                      service_name={""}
                      service_list={[]}
                      loading
                      removeFromList={() => {}}
                    />
                  ))}
              </Grid>
            </Show>

            <form.Subscribe selector={(state) => state.values.sr}>
              {(sr) =>
                sr.map((val, index) => (
                  <Stack gap="24px">
                    {index > 0 && <Separator />}

                    <ServiceRateFields
                      form={form}
                      index={index}
                      key={index}
                      id={val?.service_offer_id}
                      service_name={val?.service_offer_name || ""}
                      service_list={service_list}
                      removeFromList={() => form.removeFieldValue("sr", index)}
                      loading={false}
                    />

                    <HStack justifyContent="space-between">
                      {sr.length > 1 && !val?.service_offer_id && (
                        <Link color="actions.canceled" fontSize="14px" fontWeight="600" onClick={() => form.removeFieldValue("sr", index)}>
                          Remove Service
                        </Link>
                      )}

                      {index === 0 && <Span flex="1" />}

                      {index === sr.length - 1 && (
                        <Button
                          variant="subtle"
                          size="sm"
                          leftIcon={<Icon name="plus" color="text" />}
                          _hover={{ "& *": { color: "white" } }}
                          onClick={() => form.pushFieldValue("sr", empty_sr)}
                        >
                          Add New
                        </Button>
                      )}
                    </HStack>
                  </Stack>
                ))
              }
            </form.Subscribe>
          </FieldCard>
        </VStack>

        <HStack w="100%" justifyContent="center" mt="48px">
          <ButtonGroup size="md" variant="solid" justifyContent="center">
            <Steps.PrevTrigger asChild>
              <Button
                variant="subtle"
                // onClick={() => navigate(-1)}
                // disabled={creating}
              >
                Go back
              </Button>
            </Steps.PrevTrigger>
            {/* <Steps.NextTrigger asChild>
              <Button>Set your Service Rates</Button>
            </Steps.NextTrigger> */}

            <SubmitButton loading={adding}>Continue</SubmitButton>
          </ButtonGroup>
        </HStack>
      </Container>
    </form.AppForm>
  );
}

const ServiceRateFields = withForm({
  ...form_opts,
  props: {
    index: -1,
    id: "",
    service_name: "",
    loading: false,
    service_list: [] as { label: string; value: string; description: string }[],
    removeFromList: () => {},
  },
  render: ({ form, index, id, service_name, loading, service_list, removeFromList }) => {
    if (!!id && !!service_name) {
      return (
        <Stack>
          <Skeleton variant="shine" loading={loading}>
            <Text fontSize="14px" fontWeight={500} color="text.2" textTransform="capitalize">
              {service_name}
            </Text>
          </Skeleton>

          <Grid templateColumns="1fr auto" gap="16px">
            <Skeleton variant="shine" loading={loading}>
              <form.AppField name={`sr[${index}].amount`}>
                {(field) => (
                  <field.TextField
                    label="Amount"
                    type="number"
                    min={1}
                    field={field}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.setValue(e.target.value)}
                    hideLabel
                    formatNumber
                  />
                )}
              </form.AppField>
            </Skeleton>

            <DeleteItem
              loading={loading}
              confirmationTitle="Delete Service Rate?"
              confirmationDescription={`This action will delete ${service_name} and is irreversible`}
              mutationOpts={deleteServiceRateMutationOpts(id)}
              successMessage="Service rate has been deleted successfully"
              triggerProps={{ alignSelf: "center", variant: "subtle", size: "md" }}
              onSuccess={removeFromList}
            />
          </Grid>
        </Stack>
      );
    }

    return (
      <>
        <Grid templateColumns={{ "1sm": "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <form.AppField name={`sr[${index}].service_offer_id`}>
            {(field) => (
              <field.Select
                label="Service"
                placeholder="Select service"
                field={field}
                value={[field.state.value]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={service_list}
                itemLabelProps={{ textTransform: "capitalize" }}
                triggerProps={{ textTransform: "capitalize" }}
              />
            )}
          </form.AppField>
          <form.AppField name={`sr[${index}].amount`}>
            {(field) => (
              <field.TextField
                label="Amount"
                type="number"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
                formatNumber
              />
            )}
          </form.AppField>
        </Grid>
      </>
    );
  },
});
