/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, ConfirmationModal, Empty, FieldCard, Icon, toaster } from "@/components";
import { ButtonGroup, Container, Grid, Heading, HStack, IconButton, Show, Skeleton, Stack, Steps, Text, VStack } from "@chakra-ui/react";
import { AddTrainingModal } from "../ui/add-training";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { deleteTrainingMutationOpts, getSpecialTrainingsQueryOpts } from "@/queries";
import { SpecialTrainingDataRo } from "@/interfaces";
import { useMutation } from "@/hooks";
import { tryCatch } from "@/utils";

export function SpecialTraining() {
  const qc = useQueryClient();
  // const { goToNextStep } = useStepsContext();
  const { data, isPending: loading } = useQuery(getSpecialTrainingsQueryOpts());
  // const { mutateAsync, isPending: adding } = useMutation(addTrainingMutationOpts());

  // console.log("Training list", data);

  // const [training_list, setTrainingList] = useState<SpecialTrainingDataRo[]>([]);
  const list = data?.data || [];
  const is_empty = list.length < 1;

  // const handleAdd = async () => {
  //   const promise = mutateAsync({ data: training_list });
  //   const result = await tryCatch(promise);
  //   if (result.ok) {
  //     toaster.success({
  //       title: "Success",
  //       description: "Professional training has been added successfully",
  //     });
  //     goToNextStep();
  //   }
  // };

  const handleContinue = () => {
    qc.invalidateQueries({ queryKey: ["user/get-profile"] });
  };

  return (
    <Container maxW="680px" px="0">
      <VStack gap="16px" px="20px">
        <VStack p="20px" gap="4px">
          <Heading as="h3" fontSize="24px" fontWeight={700} color="text" textAlign="center">
            Professional Training
          </Heading>

          <Text fontSize="14px" color="text.2" textAlign="center">
            Help clients trust your expertise by sharing your professional trainings
          </Text>
        </VStack>

        <FieldCard
          p={{ base: "12px", "1smDown": "12px", "3sm": "24px" } as any}
          rounded={{ base: "8px", "1smDown": "8px", "3sm": "16px" } as any}
        >
          <HStack justifyContent="space-between">
            <Text fontSize="14px" fontWeight={600} color="text">
              Professional Training
            </Text>

            <AddTrainingModal addedTrainings={list}>
              <Button variant="subtle" size="sm" leftIcon={<Icon name="plus" color="text" />} _hover={{ "& *": { color: "white" } }}>
                Add New
              </Button>
            </AddTrainingModal>
          </HStack>

          <Stack gap="24px">
            <Show when={loading}>
              <Stack gap="16px">
                <SpecialTrainingItem item={{} as any} loading />
                <SpecialTrainingItem item={{} as any} loading />
                <SpecialTrainingItem item={{} as any} loading />
              </Stack>
            </Show>

            <Show when={!is_empty && !loading}>
              <Stack gap="16px">
                {list.map((item) => (
                  <SpecialTrainingItem key={item.training_id} item={item} />
                ))}
              </Stack>
            </Show>

            <Show when={is_empty && !loading}>
              <VStack my="10vh">
                <Empty subtitle="No professional training added yet" illustrationProps={{ boxSize: "54px" }} />
              </VStack>
            </Show>
          </Stack>
        </FieldCard>
      </VStack>

      <HStack w="100%" justifyContent="center" mt="48px">
        <ButtonGroup size="md" variant="solid" justifyContent="center">
          <Steps.PrevTrigger asChild>
            <Button
              variant="subtle"
              // onClick={() => navigate(-1)}
              // disabled={creating}
            >
              Go back
            </Button>
          </Steps.PrevTrigger>
          <Steps.NextTrigger asChild disabled={is_empty}>
            <Button onClick={handleContinue}>Continue</Button>
          </Steps.NextTrigger>

          {/* <Button onClick={handleAdd} loading={adding}>
            Continue
          </Button> */}
        </ButtonGroup>
      </HStack>
    </Container>
  );
}

function SpecialTrainingItem(props: { item: SpecialTrainingDataRo; loading?: boolean }) {
  const { item, loading = false } = props;

  const { mutateAsync, isPending: deleting } = useMutation(deleteTrainingMutationOpts(item.training_id));

  const handleDelete = async () => {
    const promise = mutateAsync({});
    const result = await tryCatch(promise);

    if (result.ok) {
      toaster.success({
        title: "Success",
        description: "Professional training has been deleted successfully",
      });
    }
  };

  return (
    <Skeleton variant="shine" loading={loading}>
      <Grid templateColumns={"1fr auto"} gap="8px" border="1px solid" borderColor="stroke.divider" rounded="8px" p="12px">
        <Stack gap="8px">
          <Text fontSize="14px" fontWeight={500} color="text">
            {item.name}
          </Text>
          <Text fontSize="12px" color="text.2">
            {item?.description || "N/A"}
            {/* My organisation would like to onboard its employees to consult professionals on this platform. */}
          </Text>
        </Stack>

        <ConfirmationModal
          title="Delete Professional Training"
          description="Are you sure you want to delete this professional training?"
          loading={deleting}
          onConfirm={async () => {
            await handleDelete();
          }}
        >
          <IconButton
            size="sm"
            variant="plain"
            aria-label="delete professional training"
            loading={deleting}
            _hover={{ "--before-bg": "colors.red.500", "& *": { color: "white" } }}
          >
            <Icon name="trash" boxSize="14px" color="text.2" />
          </IconButton>
        </ConfirmationModal>
      </Grid>
    </Skeleton>
  );
}
