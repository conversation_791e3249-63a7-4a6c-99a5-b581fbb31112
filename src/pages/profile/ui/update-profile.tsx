import { useMemo, useRef } from "react";
import { Dialog, IconButton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { Button, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import { UpdatePersonalInfoDto, updatePersonalInfoSchema } from "@/schemas";
import { useCommonList, useMutation, useUser } from "@/hooks";
import { getChangedFields, tryCatch } from "@/utils";
import { updateProfileInfoMutationOpts } from "@/queries";

interface UpdateProfileModalProps extends Dialog.RootProps {
  onAdd?: (data: string) => void;
}

export function UpdateProfileModal(props: UpdateProfileModalProps) {
  const { children, ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  const disclosure = useDisclosure();

  const { data } = useUser();
  const { phoneData } = useCommonList("country-list");
  const { data: rg_data, isPending: loading_rg } = useCommonList("religion-list");
  const { data: pl_data, isPending: loading_pl } = useCommonList("preferred-lan");
  const { mutateAsync, isPending: loading } = useMutation(updateProfileInfoMutationOpts());

  //   console.log("Preferred", pl_data);

  const user = data?.data;
  const { data: country_data, isPending: loading_country_list } = phoneData;

  const country_list = useMemo(
    () =>
      (country_data?.data || []).map((item) => ({
        label: item.name,
        value: item.name.toLowerCase(),
      })),
    [country_data]
  );

  const rg_list = (rg_data?.data || []).map((item) => ({
    label: item,
    value: item,
  }));

  const pl_list = (pl_data?.data || []).map((item) => ({
    label: item,
    value: item,
  }));

  const defaultValues: UpdatePersonalInfoDto = {
    name: user?.name || "",
    religion: user?.religion || "",
    phone_number: user?.phone_number || "",
    phone_prefix: user?.phone_prefix || "",
    residence_country: user?.residence_country || "",
    gender: user?.gender || "",
    residence_address: user?.residence_address || "",
    preferred_lan: user?.preferred_lan || [],
  };

  const form = useAppForm({
    defaultValues,

    validators: {
      onChange: updatePersonalInfoSchema,
      onSubmit: updatePersonalInfoSchema,
    },
    async onSubmit({ value }) {
      const dto = getChangedFields(defaultValues, value);

      if ((!!dto?.phone_number && !dto?.phone_prefix) || (!!dto?.phone_prefix && !dto?.phone_number)) {
        dto.phone_prefix = value?.phone_prefix;
        dto.phone_number = value?.phone_number;
      }

      const promise = mutateAsync(dto);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Personal information has been updated successfully`,
        });
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  Update Personal details
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close association modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name={`name`}>
                    {(field) => (
                      <field.TextField
                        label="Preferred Name"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name="phone_number">
                    {(field) => (
                      <field.PhoneNumberField
                        field={field}
                        label="Phone number"
                        value={field.state.value}
                        portalContainerRef={contentRef}
                        onValueChange={(e) => field.setValue(e)}
                        defaultPhoneCode={form.state.values?.phone_prefix}
                        onPhoneCodeChange={(e) => form.setFieldValue("phone_prefix", e)}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name="religion">
                    {(field) => (
                      <field.Select
                        label="Religion (optional)"
                        placeholder="Select Religion"
                        field={field}
                        loading={loading_rg}
                        value={[field.state.value || ""]}
                        onBlur={field.handleBlur}
                        portalContainerRef={contentRef}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        items={rg_list}
                        itemLabelProps={{ textTransform: "capitalize" }}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name="gender">
                    {(field) => (
                      <field.Select
                        label="Gender"
                        field={field}
                        placeholder="Select gender"
                        value={[field.state.value]}
                        portalContainerRef={contentRef}
                        items={[
                          { label: "Male", value: "male" },
                          { label: "Female", value: "female" },
                        ]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => field.setValue(e.value[0])}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name="residence_country">
                    {(field) => (
                      <field.Select
                        label="Country"
                        placeholder="Select Country"
                        field={field}
                        portalContainerRef={contentRef}
                        loading={loading_country_list}
                        value={[field.state.value]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        items={country_list}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`residence_address`}>
                    {(field) => (
                      <field.TextField
                        label="Address"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name="preferred_lan">
                    {(field) => (
                      <field.Select
                        multiple
                        label="⁠⁠Preferred language (Multiple select)"
                        placeholder="Select preferred languages"
                        field={field}
                        loading={loading_pl}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        portalContainerRef={contentRef}
                        onValueChange={(e) => field.setValue(e.value)}
                        items={pl_list}
                        itemLabelProps={{ textTransform: "capitalize" }}
                        triggerProps={{ textTransform: "capitalize" }}
                      />
                    )}
                  </form.AppField>
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={loading}>
                  Update
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
