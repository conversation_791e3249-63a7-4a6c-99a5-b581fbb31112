import { useRef } from "react";
import { Dialog, Icon<PERSON>utton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { Button, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import { AddTrainingDto, addTrainingSchema } from "@/schemas";
import { useCommonList, useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { addTrainingMutationOpts } from "@/queries";
import omitBy from "lodash.omitby";
import { SpecialTrainingDataRo } from "@/interfaces";

interface AddTrainingModalProps extends Dialog.RootProps {
  // operation?: "add" | "edit";
  onAdd?: (data: { name: string; training_id: string }) => void;
  addedTrainings?: { name: string; training_id: string; description?: string }[];
  training?: SpecialTrainingDataRo;
}

export function AddTrainingModal(props: AddTrainingModalProps) {
  const { children, addedTrainings, training, onAdd, ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  const would_defer_request = !!onAdd;
  const disclosure = useDisclosure();

  // const mutationOpts = operation === "add" ? addTrainingMutationOpts() : updateTrainingMutationOpts(training?.training_id || "");
  const { mutateAsync, isPending: loading } = useMutation(addTrainingMutationOpts());
  const { data, isPending: loading_trainings } = useCommonList("provider-special-training");
  //   const loading_trainings = false;

  const trainings = Object.values(
    omitBy(data?.data || [], (item) => addedTrainings?.some((added) => added.training_id === item.training_id))
  );

  const list = trainings.map((item) => ({
    label: item?.name,
    value: item?.training_id,
    description: item?.description,
  }));

  const form = useAppForm({
    defaultValues: {
      name: "",
      training_id: training?.training_id,
    } as AddTrainingDto,
    validators: {
      onChange: addTrainingSchema,
      onSubmit: addTrainingSchema,
      onMount: addTrainingSchema,
    },
    async onSubmit({ value }) {
      // console.log("training value", value);

      if (would_defer_request) {
        onAdd?.({ ...value, name: value?.name || "" });
        form.reset();
        disclosure.onClose();
        return;
      }

      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Professional training has been added successfully`,
        });
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    e.stopPropagation();
    form.handleSubmit();
  };

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  Add New Training
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close professional training modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name="training_id">
                    {(field) => (
                      <field.Select
                        field={field}
                        label="Professional Training"
                        loading={loading_trainings}
                        value={[field.state.value]}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        placeholder="Select a training"
                        portalContainerRef={contentRef}
                        items={list}
                      />
                    )}
                  </form.AppField>

                  {/* <LabelledSwitch label="Toggle the switch to Invite all providers on MSMT" /> */}
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={loading}>
                  Add Training
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
