import { useRef } from "react";
import { Di<PERSON>, Icon<PERSON>utton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { Button, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import { AddEducationDto, addEducationSchema } from "@/schemas";
import { useCommonList, useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { addEducationMutationOpts, updateEducationMutationOpts } from "@/queries";
import { EducationLevelDataRo } from "@/interfaces";

interface AddEducationModalProps extends Dialog.RootProps {
  operation?: "add" | "edit";
  onAdd?: (data: string) => void;
  addedEducation?: EducationLevelDataRo;
}

export function AddEducationModal(props: AddEducationModalProps) {
  const { children, addedEducation, operation = "add", ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  const disclosure = useDisclosure();

  const mutationOpts =
    operation === "add" ? addEducationMutationOpts() : updateEducationMutationOpts(addedEducation?.education_dataid || "");
  const schema = operation === "add" ? addEducationSchema : addEducationSchema;
  const { mutateAsync, isPending: loading } = useMutation(mutationOpts);
  const { data, isPending: loading_level_list } = useCommonList("education-level");
  //   const loading_trainings = false;

  const defaultValues: AddEducationDto = {
    level: addedEducation?.level || "",
    name: addedEducation?.name || "",
    location: addedEducation?.location || "",
    degree: addedEducation?.degree || "",
    year: (addedEducation?.year || "").toString(),
  };

  const level_list = (data?.data || []).map((item) => ({
    label: item,
    value: item,
  }));

  const form = useAppForm({
    defaultValues,

    validators: {
      onChange: schema,
      onSubmit: schema,
    },
    async onSubmit({ value }) {
      //   console.log("Education value", value);
      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Education has been ${operation === "add" ? "added" : "updated"} successfully`,
        });
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  {operation === "add" ? "Add New" : "Update"} Education
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close education modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name={`level`}>
                    {(field) => (
                      <field.Select
                        label="Education level"
                        placeholder="Select education level"
                        loading={loading_level_list}
                        field={field}
                        portalContainerRef={contentRef}
                        value={[field.state.value || ""]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        items={level_list}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`name`}>
                    {(field) => (
                      <field.TextField
                        label="School Name"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`location`}>
                    {(field) => (
                      <field.TextField
                        label="School Address"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`degree`}>
                    {(field) => (
                      <field.TextField
                        label="Degree Obtained"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>
                  <form.AppField name={`year`}>
                    {(field) => (
                      <field.TextField
                        label="Year Obtained"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={loading}>
                  {operation === "add" ? "Add" : "Update"} Education
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
