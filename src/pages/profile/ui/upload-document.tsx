import { useRef } from "react";
import { <PERSON><PERSON>, Icon<PERSON>utton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { Button, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import { UploadDocumentDto, uploadDocumentSchema } from "@/schemas";
import { useCommonList, useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { uploadDocumentMutationOpts } from "@/queries";
import { SpecialTrainingDataRo } from "@/interfaces";
import { useStore } from "@tanstack/react-form";

interface UploadDocumentModalProps extends Dialog.RootProps {
  // operation?: "add" | "edit";
  onAdd?: (data: string) => void;
  training?: SpecialTrainingDataRo;
  addedDocuments?: string[];
}

export function UploadDocumentModal(props: UploadDocumentModalProps) {
  const { children, ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  const disclosure = useDisclosure();

  // const mutationOpts = operation === "add" ? addTrainingMutationOpts() : updateTrainingMutationOpts(training?.training_id || "");
  const { mutateAsync, isPending: loading } = useMutation(uploadDocumentMutationOpts());
  const { data: pdl, isPending: loading_pdl } = useCommonList("provider-doc-list");
  //   const loading_pdl = false

  const doc_list = pdl?.data || [];

  const list = doc_list.map((item) => ({
    label: item,
    value: item,
  }));

  //   console.log("Upload Progress", progress);

  const form = useAppForm({
    defaultValues: {
      doc_name: "",
      file: null,
    } as UploadDocumentDto,
    validators: {
      onChange: uploadDocumentSchema,
      onSubmit: uploadDocumentSchema,
      onMount: uploadDocumentSchema,
    },
    async onSubmit({ value }) {
      // console.log("training value", value);

      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Professional training has been added successfully`,
        });
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    e.stopPropagation();
    form.handleSubmit();
  };

  const values = useStore(form.store, (store) => store.values);
  console.log("Upload doc values", values);

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  Add New Document
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close doc upload modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name="doc_name">
                    {(field) => (
                      <field.Select
                        field={field}
                        label="Select Document"
                        loading={loading_pdl}
                        value={[field.state.value]}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        placeholder="Select a document"
                        portalContainerRef={contentRef}
                        items={list}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name="file">
                    {(field) => (
                      <field.FileUploadField
                        field={field}
                        accept="application/pdf, image/png, image/jpeg, image/jpg"
                        maxFiles={1}
                        label="Upload Document"
                        onFileAccept={(e) => field.setValue(e.files[0])}
                        // onChange={(e) => field.setValue(e.target.value)}
                        onFileRemoved={() => field.setValue(null)}
                      />
                    )}
                  </form.AppField>
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={loading}>
                  Add Document
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
