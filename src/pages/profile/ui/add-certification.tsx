import { useMemo, useRef } from "react";
import { <PERSON><PERSON>, IconButton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { <PERSON><PERSON>, <PERSON><PERSON>, ShowField<PERSON>hen, SubmitButton, toaster, useAppForm } from "@/components";
import { AddCertificationDto, addCertificationSchema } from "@/schemas";
import { useCommonList, useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { addCertificationMutationOpts, updateCertificationMutationOpts } from "@/queries";
import { CertificationDataRo } from "@/interfaces";
import { useStore } from "@tanstack/react-form";

interface AddCertificationModalProps extends Dialog.RootProps {
  operation?: "add" | "edit";
  onAdd?: (data: string) => void;
  addedCertification?: CertificationDataRo;
}

export function AddCertificationModal(props: AddCertificationModalProps) {
  const { children, addedCertification, operation = "add", ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  const disclosure = useDisclosure();
  const { phoneData } = useCommonList("country-list");
  const { data: cert_data, isPending: loading_cert_list } = useCommonList("provider-certification");

  const mutationOpts =
    operation === "add" ? addCertificationMutationOpts() : updateCertificationMutationOpts(addedCertification?.certification_id || "");
  const schema = operation === "add" ? addCertificationSchema : addCertificationSchema;
  const { mutateAsync, isPending: loading } = useMutation(mutationOpts);

  const { data: country_data, isPending: loading_country_list } = phoneData;
  const countries = useMemo(() => country_data?.data || [], [country_data]);
  const country_list = useMemo(
    () =>
      countries.map((item) => ({
        label: item.name,
        value: item.name.toLowerCase(),
      })),
    [countries]
  );

  const cert_list = useMemo(
    () =>
      (cert_data?.data || []).map((item) => ({
        label: item,
        value: item,
      })),
    [cert_data]
  );

  const defaultValues: AddCertificationDto = {
    country: addedCertification?.country || "",
    cert_name: addedCertification?.cert_name || "",
    cert_year: (addedCertification?.cert_year || "").toString(),
    cert_number: addedCertification?.cert_number || "",
    state: addedCertification?.state || "",
  };

  const form = useAppForm({
    defaultValues,

    validators: {
      onChange: schema,
      onSubmit: schema,
    },
    async onSubmit({ value }) {
      //   console.log("Education value", value);
      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Certification has been ${operation === "add" ? "added" : "updated"} successfully`,
        });
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  const values = useStore(form.store, (store) => store.values);
  const errors = useStore(form.store, (store) => store.errors);
  console.log("Cert values", { values, errors });

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  {operation === "add" ? "Add New" : "Update"} Certification
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close certification modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name={`country`}>
                    {(field) => (
                      <field.Select
                        label="Select Country"
                        field={field}
                        placeholder="Select Country"
                        loading={loading_country_list}
                        portalContainerRef={contentRef}
                        value={[field.state.value]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => {
                          const country = e.value[0];
                          form.setFieldValue(`country`, country);
                          form.setFieldValue(`state`, "");
                        }}
                        items={country_list}
                        itemLabelProps={{ textTransform: "capitalize" }}
                        triggerProps={{ textTransform: "capitalize" }}
                        canFilterList
                      />
                    )}
                  </form.AppField>

                  <ShowFieldWhen when={(state) => state?.country === "united states"}>
                    <form.Subscribe selector={(state) => state.values.country}>
                      {(country) => {
                        const states = countries.find((item) => item.name.toLowerCase() === country)?.states || [];
                        const state_list = states.map((item) => ({
                          label: item.name,
                          value: item.name.toLowerCase(),
                        }));

                        return (
                          <form.AppField name={`state`}>
                            {(field) => (
                              <field.Select
                                label="Select State"
                                placeholder="Select State"
                                field={field}
                                loading={loading_country_list}
                                value={[field.state.value || ""]}
                                onBlur={field.handleBlur}
                                onValueChange={(e) => field.setValue(e.value[0])}
                                items={state_list}
                                itemLabelProps={{ textTransform: "capitalize" }}
                                triggerProps={{ textTransform: "capitalize" }}
                                disabled={!country}
                                canFilterList
                              />
                            )}
                          </form.AppField>
                        );
                      }}
                    </form.Subscribe>
                  </ShowFieldWhen>

                  <form.AppField name={`cert_name`}>
                    {(field) => (
                      <field.Select
                        label="Select Certification"
                        field={field}
                        placeholder="Select Certification"
                        loading={loading_cert_list}
                        portalContainerRef={contentRef}
                        value={[field.state.value]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        items={cert_list}
                        // itemLabelProps={{ textTransform: "capitalize" }}
                        // triggerProps={{ textTransform: "capitalize" }}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`cert_number`}>
                    {(field) => (
                      <field.TextField
                        label="Certification Number"
                        // type="number"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`cert_year`}>
                    {(field) => (
                      <field.TextField
                        label="Certification Year"
                        type="number"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                        minLength={4}
                        maxLength={4}
                      />
                    )}
                  </form.AppField>
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={loading}>
                  {operation === "add" ? "Add" : "Update"} Certification
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
