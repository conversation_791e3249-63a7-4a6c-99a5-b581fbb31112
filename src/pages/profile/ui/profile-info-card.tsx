import { FieldCard, FieldCardProps, Icon, IconNames } from "@/components";
import { Flex, Grid, HStack, Stack, StackProps, Text, TextProps } from "@chakra-ui/react";

export interface ProfileInfoCardProps extends FieldCardProps {
  title: string;
  subtitle?: string;
  description?: string;

  icon?: IconNames;

  descriptionProps?: TextProps;
  contentProps?: StackProps;

  childrenStackProps?: StackProps;
}

export function ProfileInfoCard(props: ProfileInfoCardProps) {
  const { title, subtitle, description, children, icon = "file", descriptionProps, contentProps, childrenStackProps, ...xprops } = props;

  return (
    <FieldCard p="16px" {...xprops}>
      <Grid templateColumns="1fr auto">
        <HStack alignItems="flex-start" {...contentProps}>
          <Flex alignItems="center" alignSelf="flex-start" rounded="full" justifyContent="center" boxSize="32px" bg="bkg3">
            <Icon name={icon} boxSize="16px" />
          </Flex>

          <Stack gap="4px">
            <Text fontSize="14px" fontWeight="500" color="text">
              {title}
            </Text>

            {!!subtitle && (
              <Text fontSize="14px" fontWeight="400" color="text.2">
                {subtitle}
              </Text>
            )}

            {!!description && (
              <Text fontSize="12px" fontWeight="400" color="text.2" {...descriptionProps}>
                {description}
              </Text>
            )}
          </Stack>
        </HStack>

        <Stack gap="16px" {...childrenStackProps}>
          {children}
        </Stack>
      </Grid>
    </FieldCard>
  );
}
