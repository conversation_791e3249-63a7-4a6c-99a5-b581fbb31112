import { useMemo, useRef } from "react";
import { <PERSON><PERSON>, IconButton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { Button, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import { AddAssociationDto, addAssociationSchema } from "@/schemas";
import { useCommonList, useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { addAssociationMutationOpts, updateAssociationMutationOpts } from "@/queries";
import { AssociationDataRo } from "@/interfaces";

interface AddAssociationModalProps extends Dialog.RootProps {
  operation?: "add" | "edit";
  onAdd?: (data: string) => void;
  addedAssociation?: AssociationDataRo;
}

export function AddAssociationModal(props: AddAssociationModalProps) {
  const { children, addedAssociation, operation = "add", ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  const disclosure = useDisclosure();

  const mutationOpts =
    operation === "add" ? addAssociationMutationOpts() : updateAssociationMutationOpts(addedAssociation?.association_id || "");
  const schema = operation === "add" ? addAssociationSchema : addAssociationSchema;
  const { phoneData } = useCommonList("country-list");
  const { mutateAsync, isPending: loading } = useMutation(mutationOpts);

  const { data: country_data, isPending: loading_country_list } = phoneData;

  const countries = useMemo(() => country_data?.data || [], [country_data]);
  const country_list = useMemo(
    () =>
      countries.map((item) => ({
        label: item.name,
        value: item.name.toLowerCase(),
      })),
    [countries]
  );

  const defaultValues: AddAssociationDto = {
    name: addedAssociation?.name || "",
    country: addedAssociation?.country || "",
    member_id: addedAssociation?.member_id || "",
    state: addedAssociation?.state || "",
  };

  const form = useAppForm({
    defaultValues,

    validators: {
      onChange: schema,
      onSubmit: schema,
      onMount: schema,
    },
    async onSubmit({ value }) {
      //   console.log("Education value", value);
      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Association has been ${operation === "add" ? "added" : "updated"} successfully`,
        });
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  {operation === "add" ? "Add New" : "Update"} Association
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close association modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name="country">
                    {(field) => (
                      <field.Select
                        label="Country"
                        placeholder="Select Country"
                        field={field}
                        portalContainerRef={contentRef}
                        loading={loading_country_list}
                        value={[field.state.value]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        items={country_list}
                        canFilterList
                      />
                    )}
                  </form.AppField>

                  <form.Subscribe selector={(state) => state.values.country}>
                    {(country) => {
                      const states = countries.find((item) => item.name.toLowerCase() === country)?.states || [];
                      const state_list = states.map((item) => ({
                        label: item.name,
                        value: item.name.toLowerCase(),
                      }));

                      return (
                        <form.AppField name={`state`}>
                          {(field) => (
                            <field.Select
                              label="Select State"
                              placeholder="Select State"
                              field={field}
                              portalContainerRef={contentRef}
                              loading={loading_country_list}
                              value={[field.state.value]}
                              onBlur={field.handleBlur}
                              onValueChange={(e) => field.setValue(e.value[0])}
                              items={state_list}
                              itemLabelProps={{ textTransform: "capitalize" }}
                              triggerProps={{ textTransform: "capitalize" }}
                              disabled={!country}
                              canFilterList
                            />
                          )}
                        </form.AppField>
                      );
                    }}
                  </form.Subscribe>

                  <form.AppField name={`name`}>
                    {(field) => (
                      <field.TextField
                        label="Association Name"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`member_id`}>
                    {(field) => (
                      <field.TextField
                        label="Member ID or License ID"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={loading}>
                  {operation === "add" ? "Add" : "Update"} Association
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
