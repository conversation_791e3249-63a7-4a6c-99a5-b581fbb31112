import { ConfirmationModal, Icon, toaster } from "@/components";
import { IUseMutationOptions, useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { IconButton } from "@chakra-ui/react";

interface DeleteItemProps {
  confirmationTitle: string;
  confirmationDescription: string;
  mutationOpts: IUseMutationOptions;
  successMessage: string;
}

export function DeleteItem(props: DeleteItemProps) {
  const { confirmationTitle, confirmationDescription, mutationOpts, successMessage } = props;
  const { mutateAsync, isPending: deleting } = useMutation(mutationOpts);

  const handleDelete = async () => {
    const promise = mutateAsync({});
    const result = await tryCatch(promise);

    if (result.ok) {
      toaster.success({
        title: "Success",
        description: successMessage,
      });
    }
  };

  return (
    <ConfirmationModal title={confirmationTitle} applyBtnText="Delete" description={confirmationDescription} onConfirm={handleDelete}>
      <IconButton
        size="sm"
        variant="outline"
        borderColor="primary.50"
        aria-label={confirmationDescription}
        loading={deleting}
        _hover={{ "--before-bg": "colors.red.500", "& *": { color: "white" } }}
      >
        <Icon name="trash" boxSize="16px" color="text" />
      </IconButton>
    </ConfirmationModal>
  );
}
