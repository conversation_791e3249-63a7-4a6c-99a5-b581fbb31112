import { useRef } from "react";
import { <PERSON><PERSON>, Icon<PERSON>utton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { But<PERSON>, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import { AddPublicationDto, addPublicationSchema } from "@/schemas";
import { useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { addPublicationMutationOpts, updatePublicationMutationOpts } from "@/queries";
import { PublicationDataRo } from "@/interfaces";

interface AddPublicationModalProps extends Dialog.RootProps {
  operation?: "add" | "edit";
  onAdd?: (data: string) => void;
  addedPublication?: PublicationDataRo;
}

export function AddPublicationModal(props: AddPublicationModalProps) {
  const { children, addedPublication, operation = "add", ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  const disclosure = useDisclosure();

  const mutationOpts =
    operation === "add" ? addPublicationMutationOpts() : updatePublicationMutationOpts(addedPublication?.publication_id || "");
  const schema = operation === "add" ? addPublicationSchema : addPublicationSchema;
  const { mutateAsync, isPending: loading } = useMutation(mutationOpts);

  const defaultValues: AddPublicationDto = {
    name: addedPublication?.name || "",
    year: (addedPublication?.year || "").toString(),
  };

  const form = useAppForm({
    defaultValues,

    validators: {
      onChange: schema,
      onSubmit: schema,
    },
    async onSubmit({ value }) {
      //   console.log("Education value", value);
      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Publication has been ${operation === "add" ? "added" : "updated"} successfully`,
        });
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  {operation === "add" ? "Add New" : "Update"} Publication
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close publication modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name={`name`}>
                    {(field) => (
                      <field.TextField
                        label="Publication Name"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`year`}>
                    {(field) => (
                      <field.TextField
                        label="Publication Year"
                        type="number"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={loading}>
                  {operation === "add" ? "Add" : "Update"} Publication
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
