/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigate } from "react-router";
import { AuthLayout, ResendOTP, SubmitButton, useAppForm } from "@/components";
import { chakra, Container, Heading, Link, Span, Stack, Text, VStack } from "@chakra-ui/react";
import { SyntheticEvent } from "react";
import { verifyEmailSchema } from "@/schemas";
import { useAuthStore } from "@/stores";

type FormType = typeof verifyEmailSchema.infer;

export function VerifyEmail() {
  const navigate = useNavigate();
  const email = new URLSearchParams(window.location.search).get("email") || "";

  /**
   * Indicates the source (page) of the verification
   * @ValidValues signup | forgot-password
   *
   */
  const source = new URLSearchParams(window.location.search).get("source") || "";

  const loading = useAuthStore((store) => store.loading);
  const timer = useAuthStore((store) => store.timer);
  const verifyEmail = useAuthStore((store) => store.verifyEmail);
  const completeSignup = useAuthStore((store) => store.completeSignup);

  // const loading = false;

  const form = useAppForm({
    defaultValues: {
      email,
      otp: ["", "", "", "", ""],
    } as FormType,
    validators: {
      onSubmit: verifyEmailSchema,
      onChange: verifyEmailSchema,
      onMount: verifyEmailSchema,
    },
    async onSubmit({ value }) {
      if (source === "signup") {
        const result = await completeSignup(value);
        if (result.ok) {
          return navigate("/", { replace: true });
        }
      }

      if (source === "forgot-password") {
        const result = await verifyEmail(value);
        if (result.ok) {
          const safe_email = encodeURIComponent(value.email);
          return navigate(`/set-password?email=${safe_email}&code=${value.otp.join("")}`);
        }
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <form.AppForm>
      <AuthLayout>
        <Container px={{ base: "0", md: "10px" }} maxW={{ base: "100%", "3sm": "450px" } as any}>
          <VStack as="form" my={{ base: "10vh", md: "300px" }} gap="24px" onSubmit={handleSubmit}>
            <VStack gap="4px">
              <Heading as="h3" color="text" fontSize="24px" fontWeight="700">
                Verify your Email
              </Heading>
              {/* <Text
                color="text.2"
                textAlign="center"
                fontSize="14px"
                fontWeight={400}
              >
                Access your dashboard and connect with clients.
              </Text> */}
            </VStack>

            <Stack
              w="100%"
              rounded="16px"
              gap="24px"
              p={{ base: "0", "2sm": "16px" } as any}
              bg={{ base: "transparent", "2sm": "white" } as any}
              border={{ base: "none", "2sm": "1px solid" } as any}
              borderColor={{ base: "transparent", "2sm": "stroke.divider" } as any}
            >
              <Stack gap="4px">
                <Text color="text" fontSize="12px" fontWeight="400">
                  Please enter the five digit code has been sent to <Span fontWeight="600">{email}</Span>{" "}
                  <Link asChild display="inline" color="primary" textDecorationColor="primary">
                    <chakra.button
                      type="button"
                      focusRingColor="primary"
                      onClick={(e) => {
                        e.preventDefault();
                        navigate(-1);
                      }}
                    >
                      Edit email
                    </chakra.button>
                  </Link>
                </Text>
              </Stack>

              <form.AppField name="otp">
                {(field) => (
                  <field.PinInput
                    // mask
                    count={5}
                    blurOnComplete
                    field={field}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onValueChange={(e) => {
                      field.setValue(e.value);
                    }}
                  />
                )}
              </form.AppField>

              <ResendOTP email={email} timeStart={timer?.otp?.start ?? 0} requestType="otp" />
            </Stack>

            {/* <Button type="submit" size="md">
            Sign into your Account
          </Button> */}

            <SubmitButton size="md" w="100%" loading={loading}>
              Verify Email
            </SubmitButton>

            {/* <chakra.span fontWeight="600" fontSize="14px">
              Already have an account{" "}
              <Link asChild color="primary" textDecorationColor="primary">
                <RouterLink to="/signin">Sign in Instead</RouterLink>
              </Link>
            </chakra.span> */}
          </VStack>
        </Container>
      </AuthLayout>
    </form.AppForm>
  );
}
