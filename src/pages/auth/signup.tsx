/* eslint-disable @typescript-eslint/no-explicit-any */
import { Link as RouterLink, useNavigate } from "react-router";
import { AuthLayout, PasswordFieldValidationChecks, useAppForm } from "@/components";
import { chakra, Container, Field, Grid, Heading, Link, Stack, Text, VStack } from "@chakra-ui/react";
import { SyntheticEvent } from "react";
import { registerAccountSchema } from "@/schemas";
import { useCommonList } from "@/hooks";
import { useAuthStore } from "@/stores";
import { add } from "date-fns";
import configs from "@/config";

type FormType = typeof registerAccountSchema.infer;

export function Signup() {
  const navigate = useNavigate();

  const signup = useAuthStore((store) => store.signup);
  const loading = useAuthStore((store) => store.loading);
  const setTimer = useAuthStore((store) => store.setTimer);
  const temp_signup_data = useAuthStore((store) => store.temp_signup_data);

  const { data, isPending: loading_cat } = useCommonList("service-category");
  // const loading = false;

  const list = (data?.data || []).map((item) => ({
    label: item?.name,
    value: item?.service_cat_id,
  }));

  // console.log("Service category", data);

  const form = useAppForm({
    defaultValues: {
      first_name: "",
      last_name: "",
      gender: "",
      email: "",
      password: "",
      service_cat_id: "",
      acceptTerms: false,
      ...temp_signup_data,
    } as FormType,

    validators: {
      onSubmit: registerAccountSchema.and({ acceptTerms: "true" }),
      onChange: registerAccountSchema,
      onMount: registerAccountSchema.and({ acceptTerms: "true" }),
    },
    async onSubmit({ value }) {
      const result = await signup(value);
      if (result.ok) {
        setTimer("otp", {
          start: add(new Date(), {
            seconds: configs.OTP_RESEND_TIMEOUT_SECS,
          }).getTime(),
        });

        const safe_email = encodeURIComponent(value.email);
        return navigate(`/verify-email?source=signup&email=${safe_email}`, { viewTransition: true });
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <form.AppForm>
      <AuthLayout>
        <Container px={{ base: "0", md: "10px" }} maxW={{ base: "100%", "3sm": "650px" } as any}>
          <VStack as="form" my={{ base: "10vh", md: "100px" }} gap="24px" onSubmit={handleSubmit}>
            <VStack gap="4px">
              <Heading as="h3" color="text" fontSize="24px" fontWeight="700">
                Join as a Provider
              </Heading>
              <Text color="text.2" textAlign="center" fontSize="14px" fontWeight={400}>
                Offer expert care, manage sessions, and grow your practice.
              </Text>
            </VStack>

            <Stack
              w="100%"
              rounded="16px"
              gap="24px"
              p={{ base: "0", "2sm": "16px" } as any}
              bg={{ base: "transparent", "2sm": "white" } as any}
              border={{ base: "none", "2sm": "1px solid" } as any}
              borderColor={{ base: "transparent", "2sm": "stroke.divider" } as any}
            >
              <Grid templateColumns={{ base: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
                <form.AppField name="first_name">
                  {(field) => (
                    <field.TextField
                      label="First name"
                      field={field}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.setValue(e.target.value)}
                    />
                  )}
                </form.AppField>

                <form.AppField name="last_name">
                  {(field) => (
                    <field.TextField
                      label="Last name"
                      field={field}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.setValue(e.target.value)}
                    />
                  )}
                </form.AppField>
              </Grid>

              <Grid templateColumns={{ base: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
                <form.AppField name="email">
                  {(field) => (
                    <field.TextField
                      type="email"
                      label="Email"
                      field={field}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.setValue(e.target.value)}
                    />
                  )}
                </form.AppField>

                <form.AppField name="service_cat_id">
                  {(field) => (
                    <field.Select
                      label="Specialty area"
                      field={field}
                      loading={loading_cat}
                      placeholder="Select specialty area"
                      value={[field.state.value]}
                      items={list}
                      onBlur={field.handleBlur}
                      onValueChange={(e) => field.setValue(e.value[0])}
                      itemLabelProps={{ textTransform: "capitalize" }}
                      triggerProps={{ textTransform: "capitalize" }}
                    />
                  )}
                </form.AppField>
              </Grid>

              <Grid templateColumns={{ base: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
                <form.AppField name="gender">
                  {(field) => (
                    <field.Select
                      label="Gender"
                      field={field}
                      placeholder="Select gender"
                      value={[field.state.value]}
                      items={[
                        { label: "Male", value: "male" },
                        { label: "Female", value: "female" },
                      ]}
                      onBlur={field.handleBlur}
                      onValueChange={(e) => field.setValue(e.value[0])}
                    />
                  )}
                </form.AppField>

                <form.AppField name="password">
                  {(field) => (
                    <field.PasswordField
                      label="Password"
                      field={field}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.setValue(e.target.value)}
                    />
                  )}
                </form.AppField>
              </Grid>

              <form.AppField name="password">
                {(field) => <PasswordFieldValidationChecks fieldname="password" field={field} />}
              </form.AppField>

              <form.AppField name="acceptTerms">
                {(field) => (
                  <Field.Root invalid={field.state.meta.errors.length > 0}>
                    <field.LabelledSwitch
                      field={field}
                      label={
                        <Text>
                          I agree to the{" "}
                          <Link color="primary" focusRingColor="primary" href="https://themsmt.com/terms-of-service/" target="_blank">
                            Terms of Service
                          </Link>{" "}
                          and{" "}
                          <Link
                            color="primary"
                            focusRingColor="primary"
                            href="https://msmtavatarstorage.blob.core.windows.net/msmt-liveupload/others/MSMT_Service_Provision_Agreement.pdf"
                            target="_blank"
                          >
                            Service Provision Agreement
                          </Link>{" "}
                          of this service
                        </Text>
                      }
                      switchProps={{
                        checked: field.state.value,
                        onBlur: field.handleBlur,
                        onCheckedChange: (e) => field.setValue(e.checked),
                      }}
                    />

                    {/* <FieldInfo field={field} /> */}
                  </Field.Root>
                )}
              </form.AppField>
            </Stack>

            {/* <Button type="submit" size="md">
            Sign into your Account
          </Button> */}

            <form.AppForm>
              <form.SubmitButton size="lg" loading={loading}>
                Sign Up
              </form.SubmitButton>
            </form.AppForm>

            <chakra.span fontWeight="600" fontSize="14px">
              Already have an account?{" "}
              <Link asChild color="primary" textDecorationColor="primary" focusRingColor="primary">
                <RouterLink to="/signin" viewTransition>
                  Sign in Instead
                </RouterLink>
              </Link>
            </chakra.span>
          </VStack>
        </Container>
      </AuthLayout>
    </form.AppForm>
  );
}
