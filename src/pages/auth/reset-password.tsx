/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigate } from "react-router";
import { AuthLayout, useAppForm } from "@/components";
import { Container, Heading, Stack, Text, VStack } from "@chakra-ui/react";
import { SyntheticEvent } from "react";
import { forgotPasswordSchema } from "@/schemas";
import { useAuthStore } from "@/stores";
import { add } from "date-fns";
import configs from "@/config";

export function ResetPassword() {
  const navigate = useNavigate();

  // const loading = false;
  const loading = useAuthStore((store) => store.loading);
  const setTimer = useAuthStore((store) => store.setTimer);
  const forgotPassword = useAuthStore((store) => store.forgotPassword);
  const temp_forgotPassword_data = useAuthStore((store) => store.temp_forgotPassword_data);

  const form = useAppForm({
    defaultValues: {
      email: "",
      ...temp_forgotPassword_data,
    },
    validators: {
      onSubmit: forgotPasswordSchema,
      onChange: forgotPasswordSchema,
    },
    async onSubmit({ value }) {
      const result = await forgotPassword(value.email);
      if (result.ok) {
        setTimer("otp", {
          start: add(new Date(), {
            seconds: configs.OTP_RESEND_TIMEOUT_SECS,
          }).getTime(),
        });
        const safe_email = encodeURIComponent(value.email);
        navigate(`/verify-email?source=forgot-password&email=${safe_email}`, { viewTransition: true });
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <form.AppForm>
      <AuthLayout>
        <Container px={{ base: "0", md: "10px" }} maxW={{ base: "100%", "3sm": "450px" } as any}>
          <VStack as="form" my={{ base: "10vh", md: "200px" }} gap="24px" onSubmit={handleSubmit}>
            <VStack gap="4px">
              <Heading as="h3" color="text" fontSize="24px" fontWeight="700">
                Enter Your Email
              </Heading>
              <Text color="text.2" textAlign="center" fontSize="14px" fontWeight={400}>
                Enter the email associated with your account. We would send password reset instructions to your email
              </Text>
            </VStack>

            <Stack
              w="100%"
              rounded="16px"
              gap="24px"
              p={{ base: "0", "2sm": "16px" } as any}
              bg={{ base: "transparent", "2sm": "white" } as any}
              border={{ base: "none", "2sm": "1px solid" } as any}
              borderColor={{ base: "transparent", "2sm": "stroke.divider" } as any}
            >
              <form.AppField name="email">
                {(field) => (
                  <field.TextField
                    label="Email"
                    field={field}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.setValue(e.target.value)}
                  />
                )}
              </form.AppField>
            </Stack>

            <form.SubmitButton size="md" w="100%" loading={loading}>
              Send Password
            </form.SubmitButton>

            {/* <chakra.span fontWeight="600" fontSize="14px">
              Do not have an account{" "}
              <Link
                asChild
                color="primary"
                textDecorationColor="primary"
                focusRingColor="primary"
              >
                <RouterLink to="/signup" viewTransition>
                  Sign up Instead
                </RouterLink>
              </Link>
            </chakra.span> */}
          </VStack>
        </Container>
      </AuthLayout>
    </form.AppForm>
  );
}
