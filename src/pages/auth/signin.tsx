/* eslint-disable @typescript-eslint/no-explicit-any */
import { Link as RouterLink, useNavigate } from "react-router";
import { AuthLayout, useAppForm } from "@/components";
import { chakra, Container, Heading, HStack, Link, Stack, Text, VStack } from "@chakra-ui/react";
import { SyntheticEvent } from "react";
import { useAuthStore } from "@/stores";
import { loginSchema } from "@/schemas";

export function Signin() {
  const navigate = useNavigate();
  const signin = useAuthStore((store) => store.signin);
  const loading = useAuthStore((store) => store.loading);

  // const loading = false;

  const form = useAppForm({
    defaultValues: {
      email: "",
      password: "",
    },
    validators: {
      onSubmit: loginSchema,
      onChange: loginSchema,
      onMount: loginSchema,
    },
    async onSubmit({ value }) {
      const result = await signin(value);
      console.log("Signin value", value);
      if (result.ok) {
        //   // console.log("Signin result", result.data);
        navigate(`/`, { replace: true, viewTransition: true });
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <AuthLayout>
      <Container px={{ base: "0", md: "10px" }} maxW={{ base: "100%", "3sm": "450px" } as any}>
        <VStack as="form" my={{ base: "10vh", md: "200px" }} gap="24px" onSubmit={handleSubmit}>
          <VStack gap="4px">
            <Heading as="h3" color="text" fontSize="24px" fontWeight="700">
              Sign In
            </Heading>
            <Text color="text.2" textAlign="center" fontSize="14px" fontWeight={400}>
              Access your dashboard and connect with clients.
            </Text>
          </VStack>

          <Stack
            w="100%"
            rounded="16px"
            gap="24px"
            p={{ base: "0", "2sm": "16px" } as any}
            bg={{ base: "transparent", "2sm": "white" } as any}
            border={{ base: "none", "2sm": "1px solid" } as any}
            borderColor={{ base: "transparent", "2sm": "stroke.divider" } as any}
          >
            <form.AppField name="email">
              {(field) => (
                <field.TextField
                  label="Email"
                  field={field}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.setValue(e.target.value)}
                />
              )}
            </form.AppField>

            <form.AppField name="password">
              {(field) => (
                <field.PasswordField
                  label="Password"
                  field={field}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.setValue(e.target.value)}
                />
              )}
            </form.AppField>

            <HStack w="100%" justifyContent="flex-end">
              <Link
                asChild
                mt="4px"
                w="fit-content"
                color="primary"
                fontSize="14px"
                fontWeight="600"
                focusRingColor="primary"
                textDecorationColor="primary"
              >
                <RouterLink to="/reset-password">Forgotten password</RouterLink>
              </Link>
            </HStack>
          </Stack>

          {/* <Button type="submit" size="md">
            Sign into your Account
          </Button> */}

          <form.AppForm>
            <form.SubmitButton size="md" w="100%" loading={loading}>
              Sign into your Account
            </form.SubmitButton>
          </form.AppForm>

          <chakra.span fontWeight="600" fontSize="14px">
            Don't have an account?{" "}
            <Link asChild color="primary" textDecorationColor="primary" focusRingColor="primary">
              <RouterLink to="/signup" viewTransition>
                Signup
              </RouterLink>
            </Link>
          </chakra.span>
        </VStack>
      </Container>
    </AuthLayout>
  );
}
