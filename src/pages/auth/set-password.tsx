/* eslint-disable @typescript-eslint/no-explicit-any */
import { useNavigate } from "react-router";
import { AuthLayout, PasswordFieldValidationChecks, useAppForm } from "@/components";
import { Container, Heading, Stack, Text, VStack } from "@chakra-ui/react";
import { SyntheticEvent } from "react";
import { setPasswordSchema } from "@/schemas";
import { useAuthStore } from "@/stores";

type FormType = typeof setPasswordSchema.infer;

export function SetPassword() {
  const navigate = useNavigate();

  const email = new URLSearchParams(window.location.search).get("email") || "";
  const otp = new URLSearchParams(window.location.search).get("code");

  const loading = useAuthStore((store) => store.loading);
  const setPassword = useAuthStore((store) => store.setPassword);

  // const loading = false;

  const form = useAppForm({
    defaultValues: {
      email,
      otp,
      password: "",
      confirm_password: "",
    } as FormType,
    validators: {
      onSubmit: setPasswordSchema,
      onChange: setPasswordSchema,
    },
    async onSubmit({ value }) {
      const result = await setPassword(value);
      if (result.ok) {
        return navigate("/", { replace: true, viewTransition: true });
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <form.AppForm>
      <AuthLayout>
        <Container px={{ base: "0", md: "10px" }} maxW={{ base: "100%", "3sm": "450px" } as any}>
          <VStack as="form" my={{ base: "10vh", md: "200px" }} gap="24px" onSubmit={handleSubmit}>
            <VStack gap="4px">
              <Heading as="h3" color="text" fontSize="24px" fontWeight="700">
                Set New Password
              </Heading>
              <Text color="text.2" textAlign="center" fontSize="14px" fontWeight={400}>
                Create a new password to keep your account secure.
              </Text>
            </VStack>

            <Stack
              w="100%"
              rounded="16px"
              gap="24px"
              p={{ base: "0", "2sm": "16px" } as any}
              bg={{ base: "transparent", "2sm": "white" } as any}
              border={{ base: "none", "2sm": "1px solid" } as any}
              borderColor={{ base: "transparent", "2sm": "stroke.divider" } as any}
            >
              <form.AppField name="password">
                {(field) => (
                  <field.PasswordField
                    label="Password"
                    field={field}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.setValue(e.target.value)}
                  />
                )}
              </form.AppField>

              <Stack gap="20px">
                <form.AppField name="confirm_password">
                  {(field) => (
                    <field.PasswordField
                      label="Confirm Password"
                      field={field}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.setValue(e.target.value)}
                    />
                  )}
                </form.AppField>

                <form.AppField name="password">
                  {(field) => <PasswordFieldValidationChecks fieldname="password" field={field} />}
                </form.AppField>
              </Stack>
            </Stack>

            <form.SubmitButton size="md" w="100%" loading={loading}>
              Set Password
            </form.SubmitButton>
          </VStack>
        </Container>
      </AuthLayout>
    </form.AppForm>
  );
}
