import { useRef } from "react";
import { Checkbox, Dialog, Field, IconButton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { <PERSON><PERSON>, FieldErrorInfo, Icon, ShowFieldWhen, SubmitButton, toaster, useAppForm } from "@/components";
import { UpdateBankInfoDto, updateBankInfoSchema } from "@/schemas";
import { useCommonList, useMutation, useUser } from "@/hooks";
import { getChangedFields, tryCatch } from "@/utils";
import { resolveBankAccountNoMutationOpts, updateProfileInfoMutationOpts } from "@/queries";

import { BankInfo } from "./bank-info";
import omit from "lodash.omit";

interface AddBankInfoModalProps extends Dialog.RootProps {
  onAdded?: () => void;
}

export function AddBankInfoModal(props: AddBankInfoModalProps) {
  const { onAdded, children, ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  const temp_values = useRef<Partial<UpdateBankInfoDto>>({});
  const disclosure = useDisclosure();

  const { data: user_data } = useUser();
  const { data: bl } = useCommonList("bank-list");
  const { mutateAsync, isPending: updating } = useMutation(updateProfileInfoMutationOpts());
  const { mutateAsync: resolveAccNo, isPending: resolving } = useMutation(resolveBankAccountNoMutationOpts());

  const banks = bl?.data || [];
  const collection = banks.map((item) => ({
    label: item.name,
    value: item.code,
  }));
  const bank_data = user_data?.data?.bank_data;

  const form = useAppForm({
    defaultValues: {
      account_number: bank_data?.account_number || "",
      account_name: bank_data?.account_name || "",
      bank_code: bank_data?.bank_code || "",
      confirmed: false,
    } as UpdateBankInfoDto,

    validators: {
      onChange: updateBankInfoSchema,
      onSubmit: updateBankInfoSchema,
      onMount: updateBankInfoSchema,
      onChangeAsyncDebounceMs: 500,
      onChangeAsync: async ({ value }) => {
        const { account_number, bank_code, account_name } = value;
        console.log("Bank info value", value);
        // if (confirmed && !!account_name) return;
        if (!(account_number && bank_code)) return;
        if (!!account_number && account_number.length < 10) return;

        const changes = omit(getChangedFields(temp_values.current, value), ["confirmed", "account_name"]);
        if (account_name?.length > 0 && !Object.keys(changes).length) return;

        const promise = resolveAccNo({ account_number, bank_code });
        const result = await tryCatch(promise);
        temp_values.current = value;
        console.log("Bank Info value: Resolve account number", result);
        if (result.ok) {
          form.setFieldValue("account_name", result.data?.data?.account_name || "");
          // form.setFieldValue("confirmed", true);
        }
      },
    },
    async onSubmit({ value }) {
      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Bank info has been updated successfully`,
        });
        onAdded?.();
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();

    e.stopPropagation();
    form.handleSubmit();
  };

  return (
    <Dialog.Root trapFocus placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  Update Bank Details
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close bank info modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name={`bank_code`}>
                    {(field) => (
                      <field.Select
                        label="Bank"
                        placeholder="Select bank"
                        field={field}
                        value={[field.state.value]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        items={collection}
                        portalContainerRef={contentRef}
                        itemLabelProps={{ textTransform: "capitalize" }}
                        triggerProps={{ textTransform: "capitalize" }}
                        canFilterList
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`account_number`}>
                    {(field) => (
                      <field.TextField
                        label="Account Number"
                        type="number"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  {/* <form.AppField name={`account_name`}>
                    {(field) => (
                      <field.TextField
                        label="Account Name"
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField> */}

                  {/* TODO: Make sure account name is not empty and show an error message when we are unable to resolve the account number */}
                  <ShowFieldWhen when={(state) => (state?.account_number as string)?.length > 9 && state.bank_code}>
                    <form.Subscribe selector={(state) => state.values}>
                      {(vals) => {
                        const selected_bank = banks.find((item) => item.code === vals?.bank_code);

                        return (
                          <form.AppField name={`confirmed`}>
                            {(field) => {
                              const invalid = field.state.meta.errors.length > 0;
                              // const f = form.fieldInfo.account_name.instance?.state.meta.errors;
                              // console.log("Bank info value: Confirm account", { f });

                              return (
                                <Field.Root invalid={invalid}>
                                  <BankInfo
                                    bg="bkg2"
                                    info={{
                                      account_number: vals.account_number,
                                      account_name: vals.account_name,
                                      bank_name: selected_bank?.name || "",
                                      //   bank_code: values.bank_code,
                                    }}
                                    loading={resolving}
                                  >
                                    <Checkbox.Root
                                      checked={field.state.value}
                                      onCheckedChange={(e) => field.setValue(e.checked as boolean)}
                                    >
                                      <Checkbox.HiddenInput />
                                      <Checkbox.Control
                                        boxSize="16px"
                                        rounded="full"
                                        css={{
                                          "&[data-state=checked]": {
                                            bg: "primary",
                                            borderColor: "primary",

                                            // "& svg": {
                                            //   strokeWidth: "6px",
                                            //   transform: "scale(1.5)",
                                            // },
                                          },
                                        }}
                                      />

                                      <Checkbox.Label
                                        fontSize="14px"
                                        color="text"
                                        fontWeight="600"
                                        _checked={{ color: "primary" }}
                                        _invalid={{ color: "stroke.error" }}
                                      >
                                        Confirm Account
                                      </Checkbox.Label>
                                    </Checkbox.Root>
                                  </BankInfo>

                                  <FieldErrorInfo field={field} />
                                </Field.Root>
                              );
                            }}
                          </form.AppField>
                        );
                      }}
                    </form.Subscribe>
                  </ShowFieldWhen>

                  {/* <ShowFieldWhen when={(state) => (state?.account_number as string)?.length > 9 && state.bank_code}>
                    <form.AppField name="account_name">
                      {(field) => (
                        <Field.Root invalid={field.state.meta.errors.length > 0}>
                          <FieldErrorInfo field={field} />
                        </Field.Root>
                      )}
                    </form.AppField>
                  </ShowFieldWhen> */}
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={updating}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={updating}>
                  Update Bank Details
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
