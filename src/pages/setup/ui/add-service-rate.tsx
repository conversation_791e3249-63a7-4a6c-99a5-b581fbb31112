import { useRef } from "react";
import { <PERSON>alog, IconButton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { Button, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import { AddServiceRateDto, addServiceRateSchema } from "@/schemas";
import { useCommonList, useMutation, useUser } from "@/hooks";
import { tryCatch } from "@/utils";
import { addServiceOfferMutationOpts } from "@/queries";
import { ServiceRateDataRo } from "@/interfaces";
import omitBy from "lodash.omitby";

interface AddServiceRateModalProps extends Omit<Dialog.RootProps, "children"> {
  onAdded?: () => void;
  addedRates?: ServiceRateDataRo[];
  disclosure: ReturnType<typeof useDisclosure>;
}

export function AddServiceRateModal(props: AddServiceRateModalProps) {
  const { disclosure, addedRates, onAdded, ...xprops } = props;
  const contentRef = useRef<HTMLDivElement | null>(null);

  //   const disclosure = useDisclosure();

  const { data: user_data } = useUser();
  const { data: sc } = useCommonList("service-category");
  const { mutateAsync, isPending: adding } = useMutation(addServiceOfferMutationOpts());

  // console.log("Service cat", sc);
  const categories = sc?.data || [];
  const user_service_cat_id = user_data?.data?.service_cat_id;
  const cat_service_list = categories.find((cat) => cat?.service_cat_id === user_service_cat_id)?.service_data || [];
  const filtered_list = Object.values(
    omitBy(cat_service_list, (item) => addedRates?.some((rate) => rate.service_offer_id === item.service_offer_id))
  );

  const service_list = filtered_list.map((item) => ({
    label: item.name,
    value: item.service_offer_id,
    description: item.description,
  }));

  const form = useAppForm({
    defaultValues: {
      service_offer_id: "",
      amount: "",
    } as AddServiceRateDto,

    validators: {
      onChange: addServiceRateSchema,
      onSubmit: addServiceRateSchema,
    },
    async onSubmit({ value }) {
      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: `Service rate has been added successfully`,
        });
        onAdded?.();
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();

    e.stopPropagation();
    form.handleSubmit();
  };

  //   const values = useStore(form.store, (store) => store.values);
  //   const errors = useStore(form.store, (store) => store.errors);
  //   console.log("Add service rate values", { values, errors });

  return (
    <Dialog.Root trapFocus placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      {/* <Dialog.Trigger asChild>{children}</Dialog.Trigger> */}
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="578px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Dialog.Header p="0" justifyContent="space-between" alignItems="center">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  Add New Service Rate
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close service rate modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name={`service_offer_id`}>
                    {(field) => (
                      <field.Select
                        label="Service"
                        placeholder="Select service"
                        field={field}
                        value={[field.state.value]}
                        onBlur={field.handleBlur}
                        onValueChange={(e) => field.setValue(e.value[0])}
                        items={service_list}
                        portalContainerRef={contentRef}
                        itemLabelProps={{ textTransform: "capitalize" }}
                        triggerProps={{ textTransform: "capitalize" }}
                      />
                    )}
                  </form.AppField>

                  <form.AppField name={`amount`}>
                    {(field) => (
                      <field.TextField
                        label="Charge/hr"
                        type="number"
                        min={1}
                        field={field}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.setValue(e.target.value)}
                        formatNumber
                      />
                    )}
                  </form.AppField>
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle" disabled={adding}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md" onClick={() => onAdd?.("dummy data")}>
                  Invite Provider
                </Button> */}

                <SubmitButton size="md" loading={adding}>
                  Add Service Rate
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
