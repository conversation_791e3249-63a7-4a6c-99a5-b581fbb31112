import { UserDataRo } from "@/interfaces";
import { HStack, Show, Skeleton, Stack, StackProps, Text } from "@chakra-ui/react";

export interface BankInfoProps extends StackProps {
  info?: Partial<UserDataRo["bank_data"]>;
  loading?: boolean;
}

export function BankInfo(props: BankInfoProps) {
  const { info, children, loading = false, ...xprops } = props;
  const { account_number, account_name, bank_name } = info || {};

  const pending = !(account_number && account_name && bank_name) && loading;

  return (
    <HStack w="100%" bg="input" rounded="8px" p="12px" gap="4px" alignItems="center" justifyContent="space-between" {...xprops}>
      <Show when={pending}>
        <Skeleton variant="shine" h="4px" w="20%" loading={pending} />
        <Skeleton variant="shine" h="4px" w="40%" loading={pending} />
      </Show>

      <Show when={!pending}>
        <Stack gap="4px">
          <Text fontSize="16px" fontWeight="600" color="text">
            {account_number}
          </Text>
          <Text
            fontSize="14px"
            fontWeight="500"
            color="text.2"
            maxW="30ch"
            textOverflow="ellipsis"
            overflow="hidden"
            whiteSpace="nowrap"
            title={`${account_name} | ${bank_name}`}
          >
            {account_name} | {bank_name}
          </Text>
        </Stack>
      </Show>

      {children}
    </HStack>
  );
}
