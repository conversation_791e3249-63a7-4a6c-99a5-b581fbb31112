/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  <PERSON>ton,
  Empty,
  FieldCard,
  Icon,
  InfoTip,
  PasswordFieldValidationChecks,
  SubmitButton,
  Switch,
  toaster,
  Tooltip,
  useAppForm,
} from "@/components";
import { useMutation, useUser } from "@/hooks";
import { AvailabilityDataRo, ServiceRateDataRo } from "@/interfaces";
import {
  changePasswordMutationOpts,
  getAvailabilityQueryOpts,
  getExclusiveServicesQueryOpts,
  getServiceRatesQueryOpts,
  updateAvailabilityStatusMutationOpts,
} from "@/queries";
import { changePasswordSchema } from "@/schemas";
import { formatTimeRange, tryCatch } from "@/utils";
import {
  Box,
  Container,
  FormatNumber,
  Grid,
  Heading,
  HStack,
  Popover,
  Portal,
  Show,
  Skeleton,
  Span,
  Spinner,
  Stack,
  StackProps,
  Text,
  useDisclosure,
  VStack,
} from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { SyntheticEvent } from "react";
import { useNavigate } from "react-router";
import { AddBankInfoModal } from "./ui/add-bank-info";
import { BankInfo } from "./ui/bank-info";
import { EsBadge } from "./views/exclusive-services";

export function SetupIndex() {
  const navigate = useNavigate();

  const { data: user_data } = useUser();
  const { data } = useQuery({ ...getServiceRatesQueryOpts() });
  const { data: av_data } = useQuery({ ...getAvailabilityQueryOpts() });
  const { data: es_data /*, isPending: loading_es*/ } = useQuery(getExclusiveServicesQueryOpts());

  const rates = data?.data || [];
  const avs = av_data?.data || [];
  const es_list = es_data?.data || [];

  const bank = user_data?.data?.bank_data;
  const has_bank_data = Object.entries(bank || {}).every(([, value]) => Boolean(value));

  //   console.log("Availability", av_data);

  const { mutateAsync, isPending: changing_password } = useMutation(changePasswordMutationOpts());

  const form = useAppForm({
    defaultValues: {
      new_password: "",
      old_password: "",
      confirm_password: "",
    },
    validators: {
      onChange: changePasswordSchema,
      onSubmit: changePasswordSchema,
    },
    async onSubmit({ value }) {
      // console.log("Change password", value);

      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: "Password has been changed successfully",
        });
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Heading as="h5" fontSize="20px" fontWeight="600">
          Setup
        </Heading>

        <FieldCard p="16px">
          <HStack w="100%" justifyContent="space-between">
            <Stack gap="2px">
              <Text fontSize="16px" fontWeight="600" color="text">
                Service rates
              </Text>
              <Text fontSize="12px" color="text.2">
                Set your session fee so clients know what to expect.
              </Text>
            </Stack>

            <Button
              variant="subtle"
              size="md"
              leftIcon={<Icon name="pen" color="text" />}
              _hover={{ "& *": { color: "white" } }}
              onClick={() => navigate("/setup/service-rates", { viewTransition: true })}
            >
              Edit
            </Button>
          </HStack>

          <Show when={rates.length < 1}>
            <VStack my="10px">
              <Empty subtitle="Add your service rates to appear here" />
            </VStack>
          </Show>

          <Show when={rates.length > 0}>
            <HStack gap="16px" flexWrap="wrap">
              {rates.map((rate) => (
                <ServiceRateBadge key={rate.service_offer_id} rate={rate} />
              ))}
            </HStack>
          </Show>
        </FieldCard>

        <FieldCard p="16px">
          <HStack w="100%" justifyContent="space-between">
            <Stack gap="2px">
              <Text fontSize="16px" fontWeight="600" color="text">
                <Span>Exclusive services</Span>&nbsp; (<FormatNumber value={es_list.at(0)?.amount ?? 0} currency="NGN" style="currency" />)
              </Text>
              <Text fontSize="12px" color="text.2">
                Set up exclusive services you offer.
              </Text>
            </Stack>

            <Button
              variant="subtle"
              size="md"
              leftIcon={<Icon name="pen" color="text" />}
              _hover={{ "& *": { color: "white" } }}
              onClick={() => navigate("/setup/exclusive-services", { viewTransition: true })}
            >
              {es_list.length < 1 ? "Add" : "Edit"}
            </Button>
          </HStack>

          <Show when={es_list.length < 1}>
            <VStack my="10px">
              <Empty subtitle="Add your exclusive services to appear here" />
            </VStack>
          </Show>

          <Show when={es_list.length > 0}>
            <HStack gap="16px" flexWrap="wrap">
              {es_list.map((item) => (
                <EsBadge key={item.service_exclusive_id} loading={false} item={item} fontSize="14px" px="12px" size="lg" />
              ))}
            </HStack>
          </Show>
        </FieldCard>

        <FieldCard p="16px">
          <HStack w="100%" justifyContent="space-between">
            <Stack gap="2px">
              <Text fontSize="16px" fontWeight="600" color="text">
                Availability
              </Text>
              <Text fontSize="12px" color="text.2">
                Let clients know when you’re available for sessions. Update anytime to fit your schedule.
              </Text>
            </Stack>

            <Button
              variant="subtle"
              size="md"
              leftIcon={<Icon name="pen" color="text" />}
              _hover={{ "& *": { color: "white" } }}
              onClick={() => navigate("/setup/availability", { viewTransition: true })}
            >
              Edit
            </Button>
          </HStack>

          <Show when={avs.length < 1}>
            <VStack my="10px">
              <Empty subtitle="Add your availability to appear here" />
            </VStack>
          </Show>

          <Show when={avs.length > 0}>
            <Grid
              templateColumns={
                {
                  base: "1fr",
                  "2sm": "repeat(2, 1fr)",
                  "3sm": "repeat(3, 1fr)",
                  "4sm": "repeat(4, 1fr)",
                  mdx: "repeat(5, 1fr)",
                } as any
              }
              gap="16px"
            >
              {avs.map((av) => (
                <AvailabilityInfo key={av.av_day} item={av} />
              ))}
            </Grid>
          </Show>
        </FieldCard>

        <FieldCard p="16px">
          <HStack w="100%" justifyContent="space-between">
            <Stack gap="2px">
              <Text fontSize="16px" fontWeight="600" color="text">
                Bank Information
              </Text>
              <Text fontSize="12px" color="text.2">
                Provide the bank account details where you would like to have your earnings deposited.
              </Text>
            </Stack>

            <AddBankInfoModal>
              <Button
                variant="subtle"
                size="md"
                leftIcon={<Icon name="pen" color="text" />}
                _hover={{ "& *": { color: "white" } }}
                // onClick={() => navigate("/profile/service-rate", { viewTransition: true })}
              >
                Edit
              </Button>
            </AddBankInfoModal>
          </HStack>

          <Show when={!has_bank_data}>
            <VStack my="16px">
              <Empty subtitle="Add your bank information to appear here" />
            </VStack>
          </Show>

          <Show when={has_bank_data}>
            <BankInfo
              info={{
                account_number: bank?.account_number,
                account_name: bank?.account_name,
                bank_name: bank?.bank_name || "",
                //   bank_code: values.bank_code,
              }}
            />
          </Show>
        </FieldCard>

        <form.AppForm>
          <FieldCard as="form" py="16px" px={{ base: "14px", md: "24px" }} rounded="8px" gap="16px" onSubmit={handleSubmit}>
            <HStack alignItems="flex-start" justifyContent="space-between">
              <Stack>
                <Stack gap="2px">
                  <Text fontSize="16px" fontWeight="600" color="text">
                    Security
                  </Text>
                </Stack>
              </Stack>

              {/* <Button
              variant={"subtle"}
              size="md"
              leftIcon={<Icon name="pen" />}
              _hover={{ "& *": { color: "white" } }}
            >
              Edit
            </Button> */}
            </HStack>

            <Grid
              templateColumns={
                {
                  base: "1fr",
                  "2sm": "repeat(2, 1fr)",
                  md: "repeat(3, 1fr)",
                } as any
              }
              gap="24px"
            >
              <form.AppField name="old_password">
                {(field) => (
                  <field.PasswordField
                    label="Current Password"
                    field={field}
                    value={field.state.value}
                    onChange={(e) => field.setValue(e.target.value.trim())}
                  />
                )}
              </form.AppField>
              <form.AppField name="new_password">
                {(field) => (
                  <field.PasswordField
                    label="New Password"
                    field={field}
                    value={field.state.value}
                    onChange={(e) => field.setValue(e.target.value.trim())}
                  />
                )}
              </form.AppField>
              {/* <form.AppField name="confirm_password">
                {(field) => (
                  <field.PasswordField
                    label="Confirm New Password"
                    field={field}
                    value={field.state.value}
                    onChange={(e) => field.setValue(e.target.value.trim())}
                  />
                )}
              </form.AppField> */}
            </Grid>

            {/* <Button variant="subtle" size="md" w="fit-content">
              Update Password
            </Button> */}

            <form.AppField name="new_password">
              {(field) => <PasswordFieldValidationChecks fieldname="new_password" field={field} />}
            </form.AppField>

            <SubmitButton size="md" w="fit-content" loading={changing_password}>
              Update Password
            </SubmitButton>
          </FieldCard>
        </form.AppForm>
      </Stack>
    </Container>
  );
}

interface ServiceRateBadgeProps extends StackProps {
  rate?: ServiceRateDataRo;
  loading?: boolean;
}

export function ServiceRateBadge(props: ServiceRateBadgeProps) {
  const { rate, loading = false, ...xprops } = props;

  return (
    <Skeleton variant="shine" rounded="full" loading={loading}>
      <HStack py="4px" pl="12px" pr="4px" border="1px solid" borderColor="stroke.divider" rounded="full" gap="16px" {...xprops}>
        <Text fontSize="14px" fontWeight={500} textTransform="capitalize">
          {rate?.service_offer_name}
        </Text>
        <Text px="12px" py="4px" fontSize="14px" fontWeight="500" color="primary" bg="primary.50" rounded="full" textAlign="center">
          <FormatNumber style="currency" value={rate?.amount ?? 0} currency="NGN" /> /hr
        </Text>
      </HStack>
    </Skeleton>
  );
}

interface AvailabilityInfoProps extends StackProps {
  item: AvailabilityDataRo;
  loading?: boolean;
}

function AvailabilityInfo(props: AvailabilityInfoProps) {
  const { item, loading = false, ...xprops } = props;

  const is_time_empty = item.time_data.length < 1;
  const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

  return (
    <Stack bg="input" rounded="4px" p="12px" gap="8px" {...xprops}>
      <HStack justifyContent="space-between">
        <Skeleton variant="shine" loading={loading}>
          <Text fontSize="14px" fontWeight={600} textTransform="capitalize">
            {days[item.av_day]}
          </Text>
        </Skeleton>

        <Skeleton variant="shine" loading={loading}>
          <InfoTip
            showArrow
            content="If you need to turn a time off or on, click on the time and toggle the switch"
            contentProps={{ w: "200px" }}
          />
        </Skeleton>
      </HStack>

      <Show when={is_time_empty && !loading}>
        <VStack my="1vh">
          <Text fontSize="12px" fontWeight="500" color="text" textAlign="center">
            Not set
          </Text>
          <Text fontSize="10px" color="text.2" textAlign="center">
            Clients would see this day as unavailable
          </Text>
        </VStack>
      </Show>

      <Show when={!is_time_empty && !loading}>
        <Stack h="100%" gap="10px" color="text.2">
          {item.time_data
            .sort((a, b) => b.start_time - a.start_time)
            .map((time) => (
              <ScheduleTime key={time.schedule_id} time={time} loading={loading} />
            ))}

          {/* <Span flex="1" /> */}
        </Stack>
      </Show>
    </Stack>
  );
}

function ScheduleTime(props: { time: AvailabilityDataRo["time_data"][0]; loading?: boolean }) {
  const { time, loading } = props;

  const disc = useDisclosure();
  const { mutateAsync, isPending: updating } = useMutation(updateAvailabilityStatusMutationOpts(time?.schedule_id));

  const handleToggle = async (e: boolean) => {
    const result = await tryCatch(mutateAsync({ status: Number(e).toString() }));
    if (result.ok) {
      toaster.success({
        title: "Success",
        description: "Availability has been updated successfully",
      });
      disc.onClose();
    }
  };

  return (
    <Popover.Root size="xs" open={disc.open} onOpenChange={(e) => disc.setOpen(e.open)}>
      <Popover.Trigger _hover={{ bg: "text.2/10" }} rounded="4px" p="2px 4px" cursor="pointer">
        <HStack justifyContent="space-between">
          <HStack alignItems="center">
            <Icon name="clock" boxSize="16px" color="text.2" />

            <Skeleton variant="shine" loading={loading}>
              <Text fontSize="12px" fontWeight={500} textTransform="capitalize" textAlign="left" color="inherit">
                {formatTimeRange(time).formattedRange}
              </Text>
            </Skeleton>
          </HStack>

          <Tooltip content={time?.is_active ? "Available" : "Not available"} showArrow openDelay={100}>
            {updating ? <Spinner size="xs" /> : <Box boxSize="10px" bg={time?.is_active ? "green.500" : "red.500"} rounded="full" />}
          </Tooltip>
        </HStack>
      </Popover.Trigger>

      <Portal>
        <Popover.Positioner>
          <Popover.Content w="fit-content">
            <Popover.Arrow />
            <Popover.Body>
              <Switch
                size="xs"
                alignSelf="start"
                labelProps={{ fontSize: "12px" }}
                disabled={updating}
                checked={!!time?.is_active}
                onCheckedChange={(e) => handleToggle(e.checked)}
              >
                Toggle Availability
              </Switch>
            </Popover.Body>
          </Popover.Content>
        </Popover.Positioner>
      </Portal>
    </Popover.Root>
  );
}
