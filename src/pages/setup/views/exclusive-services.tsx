/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON><PERSON><PERSON>b, Button, FieldCard, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import { getExclusiveServicesQueryOpts, updateExclusiveServicesMutationOpts } from "@/queries";
import { UpdateExclusiveServiceDto, updateExclusiveServiceSchema } from "@/schemas";
import { Badge, BadgeProps, ButtonGroup, Container, HStack, IconButton, Show, Skeleton, SkeletonText, Stack, Text } from "@chakra-ui/react";
import { formOptions } from "@tanstack/react-form";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router";
import { useMemo } from "react";
import { tryCatch } from "@/utils";
import { useCommonList, useMutation } from "@/hooks";
import { ExclusiveServiceDataRo, ExclusiveServiceDbDataRo } from "@/interfaces";

const form_opts = formOptions({
  defaultValues: {
    data: [""],
    amount: "",
  } as UpdateExclusiveServiceDto,
});

export function SetupExclusiveServices() {
  const navigate = useNavigate();

  const { data: es_data, isPending: loading_eslist } = useCommonList("exclusive-service");
  const { data, isPending: loading_es } = useQuery(getExclusiveServicesQueryOpts());
  const { mutateAsync, isPending: updating } = useMutation(updateExclusiveServicesMutationOpts());

  const es_list = useMemo(() => es_data?.data || [], [es_data]);
  const items = useMemo(() => data?.data || [], [data]);
  const is_empty = useMemo(() => items.length < 1, [items]);

  const defaultValues = useMemo(() => {
    if (items.length < 1) return form_opts.defaultValues;

    const es_ids = (items || []).map((item) => item?.service_exclusive_id);
    const amount = ((items[0] || {})?.amount || "").toString();

    return {
      data: es_ids,
      amount,
    };
  }, [items]);

  const form = useAppForm({
    defaultValues,
    validators: {
      onChange: updateExclusiveServiceSchema,
      onSubmit: updateExclusiveServiceSchema,
    },
    async onSubmit({ value }) {
      //   console.log("Update service rate", value);

      //   const changes = getChangedFields(defaultValues, value);
      //   if (!changes) return;

      const promise = mutateAsync({ data: value.data.filter(Boolean), amount: value.amount });
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "Exclusive service(s) has been updated successfully",
        });
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  //   useEffect(() => {
  //     if (!isEqual(defaultValues.sr, form.store.state.values.sr)) {
  //       form.setFieldValue("sr", defaultValues.sr);
  //     }
  //   }, [defaultValues, form]);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Setup", to: "/setup", is_first: true },
            { title: "Exclusive Service", to: "#" },
          ]}
        />

        {/* <Heading as="h5" fontSize="20px" fontWeight="600">
          Setup
        </Heading> */}

        <form.AppForm>
          <Stack as="form" onSubmit={handleSubmit} gap="40px">
            <FieldCard p="16px" _content={{ gap: "34px" }}>
              <HStack
                w="100%"
                gap="16px"
                justifyContent="space-between"
                flexDir={{ sm: "column", "3sm": "row" } as any}
                alignItems={{ sm: "flex-start", "3sm": "center" } as any}
              >
                {loading_es && <SkeletonText variant="shine" maxW="20%" noOfLines={2} _last={{ maxW: "40%" }} loading />}

                {!loading_es && (
                  <Stack gap="2px">
                    <Text fontSize="16px" fontWeight="600" color="text">
                      Exclusive Services
                    </Text>
                    <Text fontSize="12px" color="text.2">
                      {/* Set your session fee so clients know what to expect. */}
                      Set up exclusive services you offer.
                    </Text>
                  </Stack>
                )}
                {/* 
                <Skeleton variant="shine" rounded="full" loading={loading_es}>
                  <Button
                    variant="subtle"
                    size="md"
                    leftIcon={<Icon name="plus" color="text" />}
                    _hover={{ "& *": { color: "white" } }}
                    onClick={disclosure.onOpen}
                  >
                    Add New Service
                  </Button>
                </Skeleton> */}
              </HStack>

              {/* <Show when={is_empty && !loading_es}>
                <VStack my="10px">
                  <Empty subtitle="Add your service rates to appear here" />
                </VStack>
              </Show> */}

              {/* <Show when={!is_empty && !loading_es}>
                <Grid templateColumns={{ sm: "1fr", "3sm": "repeat(2, 1fr)" } as any} gap="24px">
                  <form.AppField name="sr" mode="array">
                    {(field) =>
                      (field.state.value || []).map((_, index) => (
                        <ServiceRateField
                          form={form as any}
                          index={index}
                          key={index}
                          loading={false}
                          id={rates?.[index]?.service_offer_id}
                          service_name={rates?.[index]?.service_offer_name}
                        />
                      ))
                    }
                  </form.AppField>
                </Grid>
              </Show> */}

              <Skeleton variant="shine" loading={loading_es}>
                <form.AppField name="data">
                  {(field) => (
                    <field.Select
                      multiple
                      label="Exclusive Services"
                      placeholder="Select exclusive services"
                      loading={loading_eslist}
                      field={field}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onValueChange={(e) => field.setValue(e.value)}
                      items={es_list.map((item) => ({
                        label: item.title,
                        value: item.service_exclusive_id,
                      }))}
                      canFilterList
                    />
                  )}
                </form.AppField>
              </Skeleton>

              <Skeleton variant="shine" loading={loading_es}>
                <form.AppField name="amount">
                  {(field) => (
                    <field.TextField
                      label="Amount"
                      type="number"
                      field={field}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.setValue(e.target.value)}
                      formatNumber
                    />
                  )}
                </form.AppField>
              </Skeleton>

              <Show when={!loading_es && !is_empty}>
                <form.Subscribe selector={(state) => state.values}>
                  {({ data }) => {
                    const selected = es_list.reduce((acc, curr) => {
                      if (data.includes(curr.service_exclusive_id)) {
                        acc.push({ ...curr, index: data.indexOf(curr.service_exclusive_id) });
                      }
                      return acc;
                    }, [] as ((typeof es_list)[0] & { index: number })[]);

                    return (
                      <HStack flexWrap="wrap">
                        {selected.map((item) => (
                          <EsBadge
                            key={item?.service_exclusive_id}
                            item={item}
                            loading={false}
                            removeEs={(index) => form.removeFieldValue("data", index)}
                          />
                        ))}
                      </HStack>
                    );
                  }}
                </form.Subscribe>
              </Show>

              <Show when={loading_es}>
                <HStack flexWrap="wrap">
                  {Array.from({ length: 10 })
                    .fill(0)
                    .map((_, i) => (
                      <EsBadge key={i} loading />
                    ))}
                </HStack>
              </Show>
            </FieldCard>

            <ButtonGroup w="100%" gap="24px" justifyContent="flex-end">
              <Button variant="subtle" size="md" onClick={() => navigate(-1)}>
                Cancel
              </Button>

              <form.Subscribe selector={(state) => state.isTouched}>
                {(isTouched) => (
                  <SubmitButton size="md" loading={updating} disabled={!isTouched}>
                    Update Exclusive Services
                  </SubmitButton>
                )}
              </form.Subscribe>
            </ButtonGroup>
          </Stack>
        </form.AppForm>
      </Stack>
    </Container>
  );
}

export function EsBadge(
  props: {
    item?: (ExclusiveServiceDataRo & { index: number }) | ExclusiveServiceDbDataRo;
    loading?: boolean;
    removeEs?: (index: number) => void;
  } & BadgeProps
) {
  const { item, loading, removeEs, ...xprops } = props;

  const title = (item as ExclusiveServiceDataRo)?.title || (item as ExclusiveServiceDbDataRo)?.name;
  const index = (item as ExclusiveServiceDataRo & { index: number })?.index;

  return (
    <Skeleton variant="shine" w={loading ? "200px" : "fit-content"} rounded="full" loading={loading}>
      <Badge pl="12px" rounded="full" bg="black/90" color="white" {...xprops}>
        {title}

        {!!removeEs && (
          <IconButton
            size="sm"
            variant="outline"
            aria-label="remove"
            borderColor="transparent"
            bg="none"
            color="white"
            _hover={{ "--before-bg": "colors.transparent", "& *": { color: "red.500" } }}
            onClick={() => removeEs?.(index ?? 0)}
          >
            <Icon name="trash" boxSize="16px" color="inherit" />
          </IconButton>
        )}
      </Badge>
    </Skeleton>
  );
}
