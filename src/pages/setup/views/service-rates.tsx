/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON><PERSON><PERSON>b, Button, DeleteItem, Empty, FieldCard, Icon, SubmitButton, toaster, useAppForm, withForm } from "@/components";
import { addServiceOfferMutationOpts, deleteServiceRateMutationOpts, getServiceRatesQueryOpts } from "@/queries";
import { UpdateServiceRateDto, updateServiceRateSchema } from "@/schemas";
import { ButtonGroup, Container, Grid, HStack, Show, Skeleton, SkeletonText, Stack, Text, useDisclosure, VStack } from "@chakra-ui/react";
import { formOptions } from "@tanstack/react-form";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router";
import { AddServiceRateModal } from "../ui/add-service-rate";
import { useEffect, useMemo } from "react";
import isEqual from "lodash.isequal";
import { tryCatch } from "@/utils";
import { useMutation } from "@/hooks";

const form_opts = formOptions({
  defaultValues: {
    sr: [{ service_offer_id: "", amount: "", service_offer_name: "" }],
  } as UpdateServiceRateDto,
});

export function SetupServiceRates() {
  const navigate = useNavigate();

  const disclosure = useDisclosure();

  const { data, isPending: loading_rates } = useQuery(getServiceRatesQueryOpts());
  const { mutateAsync, isPending: updating } = useMutation(addServiceOfferMutationOpts());

  const rates = useMemo(() => data?.data || [], [data]);
  const is_empty = useMemo(() => rates.length < 1, [rates]);

  const defaultValues = useMemo(() => {
    if (rates.length < 1) return form_opts.defaultValues;

    const values = (rates || []).map((rate) => ({
      service_offer_id: rate?.service_offer_id,
      amount: rate?.amount.toString(),
      service_offer_name: rate?.service_offer_name,
    }));

    return {
      sr: values,
    };
  }, [rates]);

  const form = useAppForm({
    defaultValues,
    validators: {
      onChange: updateServiceRateSchema,
      onSubmit: updateServiceRateSchema,
    },
    async onSubmit({ value }) {
      console.log("Update service rate", value);

      //   const changes = getChangedFields(defaultValues, value);
      //   if (!changes) return;

      const promise = mutateAsync({ data: value.sr });
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "Service rate(s) has been updated successfully",
        });
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  useEffect(() => {
    if (!isEqual(defaultValues.sr, form.store.state.values.sr)) {
      form.setFieldValue("sr", defaultValues.sr);
    }
  }, [defaultValues, form]);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Setup", to: "/setup", is_first: true },
            { title: "Service rate", to: "#" },
          ]}
        />

        {/* <Heading as="h5" fontSize="20px" fontWeight="600">
          Setup
        </Heading> */}

        <form.AppForm>
          <Stack as="form" onSubmit={handleSubmit} gap="40px">
            <FieldCard p="16px" _content={{ gap: "34px" }}>
              <HStack
                w="100%"
                gap="16px"
                justifyContent="space-between"
                flexDir={{ sm: "column", "3sm": "row" } as any}
                alignItems={{ sm: "flex-start", "3sm": "center" } as any}
              >
                {loading_rates && <SkeletonText variant="shine" maxW="20%" noOfLines={2} _last={{ maxW: "40%" }} loading />}

                {!loading_rates && (
                  <Stack gap="2px">
                    <Text fontSize="16px" fontWeight="600" color="text">
                      Service rates
                    </Text>
                    <Text fontSize="12px" color="text.2">
                      {/* Set your session fee so clients know what to expect. */}
                      Please add your rates to your services. Note that without specifying this, we will not be able to match a member to
                      you. Rates are specified on a per-hour basis.
                    </Text>
                  </Stack>
                )}

                <Skeleton variant="shine" rounded="full" loading={loading_rates}>
                  <Button
                    variant="subtle"
                    size="md"
                    leftIcon={<Icon name="plus" color="text" />}
                    _hover={{ "& *": { color: "white" } }}
                    onClick={disclosure.onOpen}
                  >
                    Add New Service
                  </Button>
                </Skeleton>
              </HStack>

              <Show when={loading_rates}>
                <Grid templateColumns={{ sm: "1fr", "3sm": "repeat(2, 1fr)" } as any} gap="24px">
                  <ServiceRateField form={form as any} index={0} id={""} service_name={""} loading />
                  <ServiceRateField form={form as any} index={0} id={""} service_name={""} loading />
                  <ServiceRateField form={form as any} index={0} id={""} service_name={""} loading />
                  <ServiceRateField form={form as any} index={0} id={""} service_name={""} loading />
                </Grid>
              </Show>

              <Show when={is_empty && !loading_rates}>
                <VStack my="10px">
                  <Empty subtitle="Add your service rates to appear here" />
                </VStack>
              </Show>

              <Show when={!is_empty && !loading_rates}>
                <Grid templateColumns={{ sm: "1fr", "3sm": "repeat(2, 1fr)" } as any} gap="24px">
                  <form.AppField name="sr" mode="array">
                    {(field) =>
                      (field.state.value || []).map((_, index) => (
                        <ServiceRateField
                          form={form as any}
                          index={index}
                          key={index}
                          loading={false}
                          id={rates?.[index]?.service_offer_id}
                          service_name={rates?.[index]?.service_offer_name}
                        />
                      ))
                    }
                  </form.AppField>
                </Grid>
              </Show>
            </FieldCard>

            <ButtonGroup w="100%" gap="24px" justifyContent="flex-end">
              <Button variant="subtle" size="md" onClick={() => navigate(-1)}>
                Cancel
              </Button>

              <form.Subscribe selector={(state) => state.isTouched}>
                {(isTouched) => (
                  <SubmitButton size="md" loading={updating} disabled={!isTouched}>
                    Update Service Rate
                  </SubmitButton>
                )}
              </form.Subscribe>
            </ButtonGroup>

            <AddServiceRateModal disclosure={disclosure} addedRates={rates} />
          </Stack>
        </form.AppForm>
      </Stack>
    </Container>
  );
}

const ServiceRateField = withForm({
  ...form_opts,
  props: {
    id: "",
    index: 0,
    service_name: "",
    loading: false,
  },
  render: ({ form, index, id, service_name, loading }) => {
    return (
      <Stack>
        <Skeleton variant="shine" loading={loading}>
          <Text fontSize="14px" fontWeight={500} color="text.2" textTransform="capitalize">
            {service_name}
          </Text>
        </Skeleton>

        <Grid templateColumns="1fr auto" gap="16px">
          <Skeleton variant="shine" loading={loading}>
            <form.AppField name={`sr[${index}].amount`}>
              {(field) => (
                <field.TextField
                  label="Amount"
                  type="number"
                  min={1}
                  field={field}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.setValue(e.target.value)}
                  hideLabel
                  formatNumber
                />
              )}
            </form.AppField>
          </Skeleton>

          <DeleteItem
            loading={loading}
            confirmationTitle="Delete Service Rate?"
            confirmationDescription={`This action will delete ${service_name} and is irreversible`}
            mutationOpts={deleteServiceRateMutationOpts(id)}
            successMessage="Service rate has been deleted successfully"
            triggerProps={{ alignSelf: "center", variant: "subtle", size: "md" }}
          />
        </Grid>
      </Stack>
    );
  },
});
