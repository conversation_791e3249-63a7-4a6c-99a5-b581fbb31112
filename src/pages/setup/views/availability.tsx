/* eslint-disable @typescript-eslint/no-explicit-any */
import { Breadcrumb, Button, DeleteItem, FieldCard, Icon, SubmitButton, toaster, useAppForm, withForm } from "@/components";
import { useMutation } from "@/hooks";
import { addAvailabilityMutationOpts, deleteAvailabilityMutationOpts, getAvailabilityQueryOpts } from "@/queries";
import { AddAvailabilityDto, addAvailabilitySchema } from "@/schemas";
import { getArrayChanges, getLocalTimeFromUTC, tryCatch } from "@/utils";
import { ButtonGroup, Container, Grid, HStack, IconButton, Separator, Show, Span, Stack, Text } from "@chakra-ui/react";
import { formOptions, useStore } from "@tanstack/react-form";
import { useQuery } from "@tanstack/react-query";
import { SyntheticEvent, useMemo } from "react";
import { useNavigate } from "react-router";

const empty_av = (av: number) => ({
  start_time: "",
  end_time: "",
  schedule_id: "",
  av_day: av.toString(),
});

const form_opts = formOptions({
  defaultValues: {
    // Initialize the form with 7 days of empty availability values
    av: [],
    time_zone: new Date().getTimezoneOffset().toString(),
  } as AddAvailabilityDto,
});

type GroupedValueObjType = (AddAvailabilityDto["av"][0] & { index: number })[];

const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

export function SetupAvailability() {
  const navigate = useNavigate();
  const { data: av_data } = useQuery(getAvailabilityQueryOpts());
  const { mutateAsync, isPending: updating } = useMutation(addAvailabilityMutationOpts());

  const av_list = useMemo(() => av_data?.data || [], [av_data]);

  const transformed = useMemo(
    () =>
      av_list.reduce((result, curr) => {
        curr.time_data.forEach((time) => {
          result.push({
            schedule_id: time?.schedule_id,
            av_day: curr.av_day.toString(),
            // start_time: adjustHourForTimezone(time?.start_time ?? 0).toString(),
            // end_time: adjustHourForTimezone(time?.end_time ?? 0).toString(),
            start_time: getLocalTimeFromUTC(undefined, time?.start_time ?? 0)?.hr,
            end_time: getLocalTimeFromUTC(undefined, time?.end_time ?? 0)?.hr,
          });
        });
        return result;
      }, [] as AddAvailabilityDto["av"]),
    [av_list]
  );

  const defaultValues = useMemo(() => {
    const opts = form_opts.defaultValues;
    if (transformed.length < 1) return opts;

    return {
      ...opts,
      av: transformed,
    };
  }, [transformed]);

  const form = useAppForm({
    defaultValues,
    validators: {
      onSubmit: addAvailabilitySchema,
      onChange: addAvailabilitySchema,
    },
    async onSubmit({ value }) {
      //   console.log("Availability value", value);

      const changes = getArrayChanges(defaultValues.av, value.av, "schedule_id");
      // console.log("Changes", changes);
      //   if (!changes.added.length && !changes.removed.length && !changes.modified.length) return;

      const promise = mutateAsync({ data: changes.added, time_zone: value.time_zone });
      const result = await tryCatch(promise);
      if (result.ok) {
        form.reset();
        toaster.success({
          title: "Success",
          description: "Availability has been added successfully",
        });
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  const avs = useStore(form.store, (store) => store.values.av);
  //   const errors = useStore(form.store, (store) => store.errors);
  //   const grouped_avs = groupBy(avs, "av_day");

  const grouped_avs_entries = useMemo(() => {
    const initial_values = { 0: [], 1: [], 2: [], 3: [], 4: [], 5: [], 6: [] };
    const grouped = avs.reduce((result, curr, currIdx) => {
      if (!result[curr.av_day]) {
        result[curr.av_day] = [];
      }
      result[curr.av_day].push({ ...curr, index: currIdx });
      return result;
    }, initial_values as Record<string, GroupedValueObjType>);

    return Object.entries(grouped);
  }, [avs]);

  // Dynamically generate the time list
  const time_list = useMemo(() => {
    const times = [];
    for (let hour = 0; hour < 24; hour++) {
      // Format the label in 12-hour format with AM/PM
      const period = hour < 12 ? "AM" : "PM";
      const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
      const formattedHour = displayHour.toString().padStart(2, "0");

      times.push({
        label: `${formattedHour}:00 ${period}`,
        value: hour.toString(),
      });
    }
    return times;
  }, []);

  return (
    <form.AppForm>
      <Container maxW="100rem" py="24px">
        <Stack gap="16px">
          <Breadcrumb
            items={[
              { title: "Setup", to: "/setup", is_first: true },
              { title: "Edit Availability", to: "#" },
            ]}
          />

          <Stack as="form" onSubmit={handleSubmit} gap="40px">
            <FieldCard p={{ sm: "12px", "3sm": "16px" } as any} _content={{ gap: "34px" }}>
              {grouped_avs_entries.map(([, values], index) => (
                <Stack gap="24px">
                  {index > 0 && <Separator />}

                  <AvailabilityFields
                    key={`${index}-group`}
                    form={form}
                    index={index}
                    loading={false}
                    time_list={time_list}
                    values={values}
                  />
                </Stack>
              ))}
            </FieldCard>

            <ButtonGroup w="100%" gap="24px" justifyContent="flex-end">
              <Button variant="subtle" size="md" onClick={() => navigate(-1)}>
                Cancel
              </Button>

              <form.Subscribe selector={(state) => state.isTouched}>
                {(isTouched) => (
                  <SubmitButton size="md" loading={updating} disabled={!isTouched}>
                    Update Availability
                  </SubmitButton>
                )}
              </form.Subscribe>
            </ButtonGroup>
          </Stack>
        </Stack>
      </Container>
    </form.AppForm>
  );
}

const AvailabilityFields = withForm({
  ...form_opts,
  props: { index: 0, values: [] as GroupedValueObjType, loading: false, time_list: [] as { label: string; value: string }[] },
  render: ({ form, index, values, loading, time_list }) => {
    return (
      <Stack gap="14px">
        <HStack justifyContent="space-between">
          <Text fontWeight="600" color="text">
            {days[index]}s
          </Text>

          <Button
            variant="subtle"
            size="sm"
            leftIcon={<Icon name="plus" color="text" />}
            _hover={{ "& *": { color: "white" } }}
            onClick={() => form.pushFieldValue("av", empty_av(index))}
          >
            Add New
          </Button>
        </HStack>
        <Stack>
          {values.map(({ index: subfield_idx, schedule_id }, i) => {
            const end_time_list = time_list.filter((item) => {
              const start_time = values.find((v) => v.index === subfield_idx)?.start_time;
              return !start_time || +item.value > +start_time;
            });

            const getLabel = (value: string) => time_list.find((item) => item.value === value)?.label || "";

            return (
              <Stack key={`${subfield_idx}-${i}`} gap="16px">
                <Grid key={`${subfield_idx}-${i}`} templateColumns={{ "1smDown": "1fr auto", sm: "1fr auto 1fr auto" } as any} gap="16px">
                  <Show when={!schedule_id}>
                    <form.AppField name={`av[${subfield_idx}].start_time`}>
                      {(field) => (
                        <field.Select
                          label="Start time"
                          placeholder="Select start time"
                          field={field}
                          value={[field.state.value]}
                          onBlur={field.handleBlur}
                          onValueChange={(e) => field.setValue(e.value[0])}
                          items={time_list}
                          disabled={!!schedule_id}
                        />
                      )}
                    </form.AppField>
                  </Show>

                  <Show when={!!schedule_id}>
                    <form.AppField name={`av[${subfield_idx}].start_time`}>
                      {(field) => (
                        <field.TextField
                          label="Start time"
                          // placeholder="Select start time"
                          field={field}
                          value={getLabel(field.state.value)}
                          onBlur={field.handleBlur}
                          // onValueChange={(e) => field.setValue(e.value[0])}

                          disabled={!!schedule_id}
                        />
                      )}
                    </form.AppField>
                  </Show>

                  <Span alignSelf="center">-</Span>

                  <Show when={!schedule_id}>
                    <form.AppField name={`av[${subfield_idx}].end_time`}>
                      {(field) => (
                        <field.Select
                          label="End time"
                          placeholder="Select end time"
                          field={field}
                          value={[field.state.value]}
                          onBlur={field.handleBlur}
                          onValueChange={(e) => field.setValue(e.value[0])}
                          items={end_time_list}
                        />
                      )}
                    </form.AppField>
                  </Show>

                  <Show when={!!schedule_id}>
                    <form.AppField name={`av[${subfield_idx}].end_time`}>
                      {(field) => (
                        <field.TextField
                          label="End time"
                          // placeholder="Select end time"
                          field={field}
                          value={getLabel(field.state.value)}
                          onBlur={field.handleBlur}
                          // onValueChange={(e) => field.setValue(e.value[0])}
                          // items={end_time_list}
                          disabled={!!schedule_id}
                        />
                      )}
                    </form.AppField>
                  </Show>

                  {!schedule_id && (
                    <IconButton
                      variant="subtle"
                      boxSize="32px"
                      alignSelf="center"
                      _hover={{ "--before-bg": "colors.red.500", "& *": { color: "white" } }}
                      onClick={() => form.removeFieldValue("av", subfield_idx)}
                    >
                      <Icon name="trash" boxSize="16px" color="text" />
                    </IconButton>
                  )}

                  {!!schedule_id && schedule_id.length && (
                    <DeleteItem
                      loading={loading}
                      confirmationTitle="Delete Availability?"
                      confirmationDescription={`This action will delete this availability and is irreversible`}
                      mutationOpts={deleteAvailabilityMutationOpts(schedule_id)}
                      successMessage="Availability has been deleted successfully"
                      triggerProps={{ alignSelf: "center", variant: "subtle", size: "md" }}
                    />
                  )}
                </Grid>

                {i < values.length - 1 && <Separator maxW="100%" w="100%" alignSelf="center" hideFrom="2sm" />}
              </Stack>
            );
          })}

          <Show when={values.length < 1}>
            <Text w="100%" p="14px" rounded="8px" fontSize="16px" bg="actions.lightRed" color="actions.canceled" textAlign="center">
              {/* Unavailable (No time set for this day) */}
              No time set for this day
            </Text>
          </Show>
        </Stack>
      </Stack>
    );
  },
});
