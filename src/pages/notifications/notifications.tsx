/* eslint-disable @typescript-eslint/no-explicit-any */
import { Container, Head<PERSON>, HStack, Stack } from "@chakra-ui/react";
import { useLoaderData } from "react-router";
import { useListFilter, useMutation } from "@/hooks";
import { toQueryString, tryCatch } from "@/utils";
import { useQuery } from "@tanstack/react-query";
import { NotificationLoaderDataType } from "./loader";
import { NotificationList } from "./ui/notification-list";
import { Button, Icon, toaster } from "@/components";
import {
  getNotificationListCountQueryOpts,
  getNotificationListQueryOpts,
  getUnreadNotificationCountQueryOpts,
  markAllNotificationAsReadMutationOpts,
} from "@/queries";

export function NotificationsIndex() {
  const [initial_notes, initial_count] = useLoaderData() as NotificationLoaderDataType;

  const filters = useListFilter({ page: 1, item_per_page: 10, q: "" });
  const { filter, setFilter } = filters;

  const {
    data: list,
    isPending,
    isFetching,
  } = useQuery({ ...getNotificationListQueryOpts(toQueryString(filter)), initialData: initial_notes });
  const { data: count } = useQuery({ ...getNotificationListCountQueryOpts(toQueryString(filter)), initialData: initial_count });
  const { data: note_data } = useQuery(getUnreadNotificationCountQueryOpts());

  const { mutateAsync, isPending: marking } = useMutation(markAllNotificationAsReadMutationOpts());

  const total_unread_notes = note_data?.data?.total || 0;

  const handleMarkAllAsRead = async () => {
    const promise = mutateAsync({});
    const result = await tryCatch(promise);

    if (result.ok) {
      toaster.success({
        title: "Success",
        description: "All notifications has been marked as read",
      });
    }
  };

  const handlePageChange = (page: number) => {
    setFilter({ page });
  };

  console.log("Notifications", initial_notes);
  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <HStack
          justifyContent="space-between"
          flexDir={{ smDown: "column", "2sm": "row" } as any}
          alignItems={{ sm: "flex-start", "2sm": "center" } as any}
        >
          <Heading as="h5" fontSize="20px" fontWeight="600">
            Notifications
          </Heading>

          {total_unread_notes > 0 && (
            <Button
              w="fit-content"
              leftIcon={<Icon name="badge_check" color="inherit" />}
              onClick={handleMarkAllAsRead}
              disabled={marking}
              loading={marking}
            >
              Mark all as read
            </Button>
          )}
        </HStack>

        {/* <FieldCard
          flex=".7"
          gap="20px"
          heading="Notifications"
          viewTransitionName="notifications"
          _header={{
            css: {
              "& > #heading": {
                fontWeight: 700,
              },
            },
          }}
          headingProps={{
            viewTransitionName: "notification-heading",
          }}
        > */}
        <HStack justifyContent="space-between">
          {/* <InputGroup maxW="300px" startElement={<Icon name="search" />}>
            <Input variant="subtle" placeholder="Search" />
          </InputGroup> */}

          {/* <HStack>
            <IconButton
              variant="plain"
              aria-label="Download appointments"
              size="md"
              w="40px"
              h="32px"
              viewTransitionName="bell-image"
              _hover={{
                "& :where(svg)": {
                  color: "white !important",
                },
              }}
            >
              <Icon name="download" color="black" />
            </IconButton>
          </HStack> */}
        </HStack>

        <NotificationList
          filter={filter}
          onPageChange={handlePageChange}
          loading={isFetching && isPending}
          list={{ ...list, ...count?.data }}
        />
        {/* </FieldCard> */}
      </Stack>
    </Container>
  );
}
