import { Empty, NotificationTableDataType, Paginator, toNotificationTableData } from "@/components";
import { IListFilter, NotificationListRo } from "@/interfaces";
import {
  Box,
  HStack,
  Separator,
  Show,
  Skeleton,
  SkeletonCircle,
  SkeletonText,
  Stack,
  StackProps,
  Table,
  Text,
  VStack,
} from "@chakra-ui/react";
import { useMemo } from "react";

interface NotificationListProps {
  list?: NotificationListRo;
  filter?: IListFilter;

  onPageChange?: (page: number) => void;
  loading?: boolean;
}

export function NotificationList(props: NotificationListProps) {
  const { list, filter, loading = false, onPageChange } = props;

  const total = useMemo(() => list?.total || 1, [list]);
  const items = useMemo(() => (list ? toNotificationTableData(list?.data ?? []) : raw_items), [list]);
  const isEmpty = useMemo(() => !list || !list.data || list.data.length === 0, [list]);

  //   const navigate = useNavigate();

  return (
    <Stack gap="20px">
      <Table.ScrollArea hideBelow="3sm">
        <Table.Root interactive>
          <Table.Header>
            <Table.Row
              bg="input"
              rounded="4px"
              color="text.2"
              css={{
                "& > th": {
                  borderBottom: "none",
                  _first: {
                    borderTopLeftRadius: "4px",
                    borderBottomLeftRadius: "4px",
                  },
                  _last: {
                    borderTopRightRadius: "4px",
                    borderBottomRightRadius: "4px",
                  },
                },
              }}
            >
              <Table.ColumnHeader w="15%">
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Date & Time Added
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Message
                </SkeletonText>
              </Table.ColumnHeader>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {items.map((item) => (
              <Table.Row
                key={item.id}
                border="none"
                py="100px"
                css={{
                  "& > td": {
                    color: "text.2",
                    borderBottom: "none",
                    py: "14px",
                    _first: {
                      borderTopLeftRadius: "4px",
                      borderBottomLeftRadius: "4px",
                    },
                    _last: {
                      borderTopRightRadius: "4px",
                      borderBottomRightRadius: "4px",
                    },
                  },

                  _hover: {
                    bg: "primary.50",
                    cursor: loading ? "default" : "pointer",
                  },
                }}
                // onClick={() =>
                //   navigate(`/appointments/${item.id}`, { state: item })
                // }
              >
                <Table.Cell>
                  <HStack>
                    {!item?.read && (
                      <SkeletonCircle variant="shine" loading={loading}>
                        <Box boxSize="8px" bg="primary" rounded="full" />
                      </SkeletonCircle>
                    )}

                    <Skeleton variant="shine" h={loading ? "16px" : "fit-content"} loading={loading}>
                      {item?.datetime}
                    </Skeleton>
                  </HStack>
                </Table.Cell>
                <Table.Cell>
                  <Skeleton variant="shine" h={loading ? "16px" : "fit-content"} loading={loading}>
                    {item?.message || "N/A"}
                  </Skeleton>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </Table.ScrollArea>

      <Stack gap="8px" hideFrom="3sm">
        {items.map((item) => (
          <NotificationMobileListItem key={item.id} note={item as NotificationTableDataType} />
        ))}
      </Stack>

      <Show when={isEmpty && !loading}>
        <VStack w="100%" my="22vh">
          <Empty subtitle="Notifications will appear here" />
        </VStack>
      </Show>

      <Show when={filter}>
        <Paginator
          count={total}
          pageSize={filter?.item_per_page || 10}
          defaultPage={1}
          page={filter?.page}
          onPageChange={(e) => onPageChange?.(e.page)}
        />
      </Show>
    </Stack>
  );
}

export interface NotificationMobileListItemProps extends Omit<StackProps, "id"> {
  note: NotificationTableDataType;
  loading?: boolean;
}

export function NotificationMobileListItem(props: NotificationMobileListItemProps) {
  const { note, ...xprops } = props;
  const { id, datetime, message, read } = note;
  const loading = false;

  //   const navigate = useNavigate();

  console.log("Notification id", id);

  return (
    <Stack
      py="16px"
      px="12px"
      rounded="4px"
      gap="8px"
      role="button"
      cursor="pointer"
      focusRingColor="primary"
      border="1px solid"
      borderColor="stroke.divider"
      //   onClick={() =>
      //     navigate(`/appointments/${id}`, {
      //       state: { id, consultant, bookedby, status, member },
      //     })
      //   }
      {...xprops}
    >
      <HStack justifyContent="space-between">
        <SkeletonText variant="shine" noOfLines={1} loading={loading}>
          <Text fontSize="12px" fontWeight="500" color="text">
            {/* Today • 12:34pm */}
            {datetime}
          </Text>
        </SkeletonText>

        <Skeleton variant="shine" loading={loading}>
          {!read && <Box boxSize="8px" bg="primary" rounded="full" />}
        </Skeleton>
      </HStack>

      <SkeletonText variant="shine" h="4px" noOfLines={1} loading={loading}>
        <Separator borderColor="stroke.divider" />
      </SkeletonText>

      <Skeleton variant="shine" loading={loading}>
        <Text fontSize="12px" fontWeight="400" color="text.2">
          {message}
        </Text>
      </Skeleton>
    </Stack>
  );
}

const raw_items = [
  {
    id: 1,
    datetime: "Today • 12:34pm",
    read: false,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
  {
    id: 2,
    datetime: "Today • 12:34pm",
    read: false,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
  {
    id: 3,
    datetime: "Today • 12:34pm",
    read: false,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
  {
    id: 4,
    datetime: "Today • 12:34pm",
    read: true,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
  {
    id: 5,
    datetime: "Today • 12:34pm",
    read: true,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
  {
    id: 6,
    datetime: "Today • 12:34pm",
    read: true,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
  {
    id: 7,
    datetime: "Today • 12:34pm",
    read: true,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
  {
    id: 8,
    datetime: "Today • 12:34pm",
    read: true,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
  {
    id: 9,
    datetime: "Today • 12:34pm",
    read: true,
    message:
      "Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin. Sample notification Lorem ipsum dolor sit amet consectetur. Elit in id odio ut faucibus. Gravida sit quis cras sollicitudin.",
  },
];
