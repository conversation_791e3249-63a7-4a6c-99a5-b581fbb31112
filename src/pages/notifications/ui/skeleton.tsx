import { FieldCard, Icon, <PERSON>Field } from "@/components";
import { HStack, IconButton, SkeletonCircle, Stack } from "@chakra-ui/react";
import { NotificationList } from "./notification-list";

export function NotificationsIndexSkeleton() {
  return (
    <Stack gap="24px">
      <FieldCard
        flex=".7"
        gap="20px"
        heading="Notifications"
        loading
        viewTransitionName="notifications"
        _header={{
          css: {
            "& > #heading": {
              fontWeight: 700,
            },
          },
        }}
        headingProps={{
          viewTransitionName: "notification-heading",
        }}
      >
        <HStack justifyContent="space-between">
          <SearchField loading />

          <HStack>
            <SkeletonCircle variant="shine" loading>
              <IconButton
                variant="plain"
                aria-label="Download appointments"
                size="md"
                w="40px"
                h="32px"
                viewTransitionName="bell-image"
                _hover={{
                  "& :where(svg)": {
                    color: "white !important",
                  },
                }}
              >
                <Icon name="download" color="black" />
              </IconButton>
            </SkeletonCircle>
          </HStack>
        </HStack>

        <NotificationList loading />
      </FieldCard>
    </Stack>
  );
}
