import { QueryClient } from "@tanstack/react-query";
import { getNotificationListCountQueryOpts, getNotificationListQueryOpts } from "@/queries";
import { LoaderFunctionArgs } from "react-router";
import { ensureQueryData } from "@/libs";

export function notificationIndexLoader(query_client: QueryClient) {
  return async ({ request }: LoaderFunctionArgs) => {
    const fetch = ensureQueryData(query_client);
    const search = new URL(request.url).search;
    const params = new URLSearchParams(search);
    if (!params.has("page")) params.set("page", "1");
    if (!params.has("item_per_page")) params.set("item_per_page", "10");

    return await Promise.all([
      fetch(getNotificationListQueryOpts(params.toString())),
      fetch(getNotificationListCountQueryOpts(params.toString())),
    ]);
  };
}

export type NotificationLoaderDataType = Awaited<ReturnType<ReturnType<typeof notificationIndexLoader>>>;
