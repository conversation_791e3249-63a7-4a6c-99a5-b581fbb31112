/* eslint-disable @typescript-eslint/no-explicit-any */
import { AppliedListFilters, Icon, WalletStat } from "@/components";
import { Container, FormatNumber, Heading, HStack, IconButton, Span, Stack } from "@chakra-ui/react";
import { TableFilter } from "./ui/table-filter";
import { useLoaderData } from "react-router";
import { useQuery } from "@tanstack/react-query";
import { walletListCountQueryOpts, walletListQueryOpts, walletStatsQueryOpts } from "@/queries";
import { useListFilter } from "@/hooks";
import { toQueryString } from "@/utils";
import { WalletList } from "./ui/wallet-list";
// import { WithdrawFundModal } from "./ui/withdraw-fund-modal";
import { WalletIndexLoaderDataType } from "./loader";

export function WalletIndex() {
  const [initial_list, initial_stats, initial_count] = useLoaderData() as WalletIndexLoaderDataType;

  const filters = useListFilter({ page: 1, item_per_page: 10, q: "" });
  const { filter, setFilter } = filters;

  /// useQuery is required to fetch updates when the wallet list & stats changes
  const { data: list, isFetching, isPending } = useQuery({ ...walletListQueryOpts(toQueryString(filter)), initialData: initial_list });
  const { data: count } = useQuery({ ...walletListCountQueryOpts(toQueryString(filter)), initialData: initial_count });
  const { data: stats } = useQuery({ ...walletStatsQueryOpts(), initialData: initial_stats });

  const stat = stats?.data;

  const handlePageChange = (page: number) => {
    setFilter({ page });
  };

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Heading as="h5" fontSize="20px" fontWeight="600">
          Wallet
        </Heading>

        {/* <FieldCard p={{ base: "8px", "3sm": "20px" } as any}> */}
        <HStack flexDir={{ base: "column", sm: "column", "3sm": "row", md: "row" } as any} gap="16px">
          <WalletStat bg="stat.2" label="Earnings till date" valueText={<FormatNumber value={stat?.total_credit || 0} />} />
          <WalletStat bg="stat.3" label="Withdrawal till date" valueText={<FormatNumber value={stat?.total_debit || 0} />} />
          <WalletStat label="Current Balance" valueText={<FormatNumber value={stat?.total_balance || 0} />} />
        </HStack>
        {/* </FieldCard> */}

        <Stack gap="16px">
          <HStack justifyContent="space-between">
            <Heading as="h6" fontSize="16px" fontWeight="700">
              Transaction history
            </Heading>

            <Span flex="1" />

            <HStack>
              <IconButton
                variant="plain"
                aria-label="Download transactions"
                size="md"
                w="40px"
                h="32px"
                _hover={{
                  "& :where(svg)": {
                    color: "white !important",
                  },
                }}
                display="none"
              >
                <Icon name="download" color="black" />
              </IconButton>

              <TableFilter filters={filter} onFilterChange={setFilter} />

              {/* <WithdrawFundModal /> */}
            </HStack>
          </HStack>

          <AppliedListFilters keyPath="wallet" loading={isFetching && isPending} {...filters} />

          <WalletList
            filter={filter}
            onPageChange={handlePageChange}
            loading={isFetching && isPending}
            list={{ ...list, ...count?.data }}
          />
        </Stack>
      </Stack>
    </Container>
  );
}
