import {
  Format<PERSON><PERSON>ber,
  HStack,
  LocaleProvider,
  Separator,
  Show,
  Skeleton,
  SkeletonText,
  Stack,
  StackProps,
  Table,
  Text,
  VStack,
} from "@chakra-ui/react";

import { useMemo } from "react";

import { ProviderStatus, Icon, Paginator, Empty, toWalletTableData, WalletTableDataType } from "@/components";
import { IListFilter, WalletListRo } from "@/interfaces";
import { getStatusColor } from "@/utils";

interface WalletListProps {
  list?: WalletListRo;
  filter?: IListFilter;

  onPageChange?: (page: number) => void;
  loading?: boolean;
}

export function WalletList(props: WalletListProps) {
  // const navigate = useNavigate();
  const { list, filter, loading = false, onPageChange } = props;

  const total = useMemo(() => list?.total || 1, [list]);
  const items = useMemo(() => (list ? toWalletTableData(list?.data ?? []) : raw_items), [list]);
  const isEmpty = useMemo(() => !list || !list.data || list.data.length === 0, [list]);

  // console.log("Wallet list", { total });

  return (
    <Stack gap="20px" minH="68svh" justifyContent="space-between">
      <Table.ScrollArea hideBelow="3sm">
        <Table.Root interactive>
          <Table.Header>
            <Table.Row
              bg="input"
              rounded="4px"
              color="text.2"
              css={{
                "& > th": {
                  borderBottom: "none",
                  _first: {
                    borderTopLeftRadius: "4px",
                    borderBottomLeftRadius: "4px",
                  },
                  _last: {
                    borderTopRightRadius: "4px",
                    borderBottomRightRadius: "4px",
                  },
                },
              }}
            >
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Date & Time
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Description
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Unit
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Type
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Status
                </SkeletonText>
              </Table.ColumnHeader>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {items.map((item) => (
              <Table.Row
                key={item.id}
                // border="none"
                py="100px"
                viewTransitionName={`member-${item?.id}`}
                border="1px solid transparent"
                borderBottomColor="stroke.divider"
                _last={{ border: "none" }}
                css={{
                  "& > td": {
                    color: "text.2",
                    borderBottom: "none",
                    py: "14px",
                    _first: {
                      borderTopLeftRadius: "4px",
                      borderBottomLeftRadius: "4px",
                    },
                    _last: {
                      borderTopRightRadius: "4px",
                      borderBottomRightRadius: "4px",
                    },
                  },

                  _hover: {
                    bg: "primary.50",
                    cursor: "pointer",
                  },
                }}
                //   onClick={() => navigate(`/wallet/${item.id}`, { state: item })}
              >
                <Table.Cell>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.datetime}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.description}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    <FormatNumber value={item?.unit || 0} />
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.type}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell color={`${getStatusColor(item.status as string)} !important`}>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.status}
                  </SkeletonText>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </Table.ScrollArea>

      <Stack gap="8px" hideFrom="3sm">
        {items.map((item) => (
          <WalletMobileListItem
            key={item.id}
            wallet={item}
            loading={loading}
            // onClick={() => navigate(`/wallet/${item.id}`, { state: item })}
          />
        ))}
      </Stack>

      <Show when={isEmpty && !loading}>
        <VStack w="100%" my="10vh">
          <Empty subtitle="Transactions will appear here" />
        </VStack>
      </Show>

      <Show when={filter}>
        <Paginator
          count={total}
          pageSize={filter?.item_per_page || 10}
          defaultPage={1}
          page={filter?.page}
          onPageChange={(e) => onPageChange?.(e.page)}
        />
      </Show>
    </Stack>
  );
}

export interface WalletMobileListItemProps extends Omit<StackProps, "id"> {
  wallet: WalletTableDataType;
  loading?: boolean;
}

export function WalletMobileListItem(props: WalletMobileListItemProps) {
  const { wallet, loading = true, ...xprops } = props;

  //   const navigate = useNavigate();

  // console.log("wallet item ID", id);

  const { datetime, description, status, type, unit } = wallet;

  return (
    <Stack
      py="16px"
      px="12px"
      // bg="input"
      rounded="8px"
      gap="8px"
      role="button"
      cursor="pointer"
      focusRingColor="primary"
      border="1px solid"
      borderColor="stroke.divider"
      //   onClick={() =>
      //     navigate(`/wallet/${id}`, {
      //       state: { id, consultant, bookedby, status, member },
      //     })
      //   }

      {...xprops}
    >
      <HStack justifyContent="space-between">
        <SkeletonText variant="shine" noOfLines={1} loading={loading}>
          <Text fontSize="12px" fontWeight="500" color="text">
            {type}
          </Text>
        </SkeletonText>
        <ProviderStatus fontSize="12px" fontWeight="500" variant="plain" status={status as string} loading={loading} />
      </HStack>

      <SkeletonText variant="shine" h="4px" noOfLines={1} loading={loading}>
        <Separator borderColor="stroke.divider" />
      </SkeletonText>

      <HStack justifyContent="space-between">
        <Skeleton variant="shine" loading={loading}>
          <HStack gap="4px">
            <Icon name="stethoscope" boxSize="12px" color="text.3" />
            <Text fontSize="12px" fontWeight="400" color="text">
              {description}
            </Text>
          </HStack>
        </Skeleton>

        <Skeleton variant="shine" loading={loading}>
          <Text fontSize="12px" fontWeight="500" color="text.2">
            {/* ₦50,000 */}
            <LocaleProvider locale="en-NG">
              <FormatNumber value={unit || 0} style="currency" currency="NGN" />
            </LocaleProvider>
          </Text>
        </Skeleton>
      </HStack>

      <SkeletonText variant="shine" noOfLines={1} loading={loading}>
        <Text fontSize="12px" fontWeight="400" color="text.3">
          {/* Today • 12:34pm */}
          {datetime}
        </Text>
      </SkeletonText>
    </Stack>
  );
}

const raw_items = [
  {
    id: 1,
    datetime: "Today • 12:34pm",
    description: "Fund Wallet",
    unit: 502853,
    type: "Credit",
    status: "Status",
  },
  {
    id: 2,
    datetime: "Today • 12:34pm",
    description: "Appointment charge",
    unit: 502853,
    type: "Deduction",
    status: "Status",
  },
  {
    id: 3,
    datetime: "Today • 12:34pm",
    description: "Fund Wallet",
    unit: 502853,
    type: "Credit",
    status: "Status",
  },
  {
    id: 4,
    datetime: "Today • 12:34pm",
    description: "Fund Wallet",
    unit: 502853,
    type: "Credit",
    status: "Status",
  },
  {
    id: 5,
    datetime: "Today • 12:34pm",
    description: "Fund Wallet",
    unit: 502853,
    type: "Credit",
    status: "Status",
  },
  {
    id: 6,
    datetime: "Today • 12:34pm",
    description: "Fund Wallet",
    unit: 502853,
    type: "Credit",
    status: "Status",
  },
  {
    id: 7,
    datetime: "Today • 12:34pm",
    description: "Fund Wallet",
    unit: 502853,
    type: "Credit",
    status: "Status",
  },
  {
    id: 8,
    datetime: "Today • 12:34pm",
    description: "Fund Wallet",
    unit: 502853,
    type: "Credit",
    status: "Status",
  },
  {
    id: 9,
    datetime: "Today • 12:34pm",
    description: "Fund Wallet",
    unit: 502853,
    type: "Credit",
    status: "Status",
  },
];
