import { chakra, Dialog, FormatNumber, IconButton, LocaleProvider, Portal, Stack, Text, useDisclosure, VStack } from "@chakra-ui/react";
import { Button, Icon, SubmitButton, useAppForm } from "@/components";

import { SyntheticEvent } from "react";

import { useQuery } from "@tanstack/react-query";
import { getUserProfileQueryOpts } from "@/queries";

export function WithdrawFundModal() {
  const disclosure = useDisclosure();
  // const { mutateAsync, isPending: loading } = useMutation(initFundWalletMutationOpts());
  // const { mutateAsync: completeFund, isPending: completingFund } = useMutation(completeFundWalletMutationOpts());

  const loading = false;
  const completingFund = false;

  const { data } = useQuery(getUserProfileQueryOpts());
  const user = data?.data;
  const rate = user?.funding_unitrate || 1;

  const form = useAppForm({
    defaultValues: {
      amount: "",
    },
    // validators: {
    //   onChange: initFundWalletSchema,
    //   onSubmit: initFundWalletSchema,
    // },

    async onSubmit({ value }) {
      if (!user?.email) return;

      console.log("Wallet", value);

      // console.log("Buy unit", value);
      // const promise = mutateAsync(value);
      // const result = await tryCatch(promise);

      // console.log("Buy unit result", result);

      // if (result.ok) {
      //   const res = result.data.data;
      //   const paystack = new Paystack();

      //   if (!res?.paystack_key) return;

      //   paystack.newTransaction({
      //     key: res?.paystack_key,
      //     email: user?.email,
      //     amount: res?.amount,
      //     reference: res?.transaction_id,

      //     async onSuccess() {
      //       const promise = completeFund({ transaction_id: res?.transaction_id });
      //       const result = await tryCatch(promise);

      //       if (result.ok) {
      //         disclosure.onClose();
      //       }
      //     },
      //   });
      // }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <Dialog.Root
      placement="center"
      modal={false}
      open={disclosure.open}
      closeOnEscape={false}
      closeOnInteractOutside={false}
      onOpenChange={(e) => disclosure.setOpen(e.open)}
    >
      <Dialog.Trigger asChild>
        <Button size="sm" leftIcon={<Icon name="hand_coins" color="white" />}>
          Withdraw Fund
        </Button>
      </Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content rounded="16px" p="24px" maxW="578px" gap="24px" as="form" onSubmit={handleSubmit}>
            <form.AppForm>
              <Dialog.Header p="0" justifyContent="space-between">
                <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                  Withdraw Fund
                </Dialog.Title>

                <Dialog.CloseTrigger asChild pos="relative" top="unset">
                  <IconButton
                    variant="plain"
                    aria-label="Close withdraw fund modal"
                    size="sm"
                    w="40px"
                    h="32px"
                    _hover={{
                      "& :where(svg)": {
                        color: "white !important",
                      },
                    }}
                    // onClick={onClose}
                    css={{ "--before-bg": "{colors.primary}" }}
                  >
                    <Icon name="close" color="stroke.checkbox" />
                  </IconButton>
                </Dialog.CloseTrigger>
              </Dialog.Header>
              <Dialog.Body p="0">
                <Stack gap="24px">
                  <form.AppField name="amount">
                    {(field) => (
                      <field.TextField
                        label="Amount to withdraw"
                        field={field}
                        type="number"
                        // endElement={<Icon name="dollar_sign" />}
                        value={field.state.value}
                        onChange={(e) => field.setValue(e.target.value)}
                      />
                    )}
                  </form.AppField>

                  {/* <TextField label="Amount to Buy" type="number" endElement={<Icon name="dollar_sign" />} /> */}

                  <form.Subscribe selector={(state) => state.values.amount}>
                    {(amount) => (
                      <LocaleProvider locale="en-NG">
                        <VStack p="12px" rounded="8px" bg="primary.50" textAlign="center">
                          <Text fontSize="12px">
                            <FormatNumber style="currency" value={rate} currency="NGN" /> = 1 unit
                          </Text>
                          <Text fontSize="12px">
                            <FormatNumber style="currency" value={+amount} currency="NGN" /> will get you{" "}
                            <chakra.span fontWeight="500">{+amount * rate} units</chakra.span>
                          </Text>
                        </VStack>
                      </LocaleProvider>
                    )}
                  </form.Subscribe>
                </Stack>
              </Dialog.Body>
              <Dialog.Footer mt="40px" px="0">
                <Dialog.ActionTrigger asChild>
                  <Button size="md" variant="subtle">
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button size="md">Buy Unit</Button> */}

                <SubmitButton size="md" loading={loading || completingFund}>
                  Buy Unit
                </SubmitButton>
              </Dialog.Footer>
            </form.AppForm>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
