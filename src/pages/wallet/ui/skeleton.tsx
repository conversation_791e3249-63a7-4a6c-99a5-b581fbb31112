/* eslint-disable @typescript-eslint/no-explicit-any */
import { Container, FormatNumber, Heading, HStack, IconButton, Skeleton, SkeletonCircle, Stack } from "@chakra-ui/react";
import { Icon, SearchField, WalletStat } from "@/components";

import { TableFilter } from "./table-filter";
import { WithdrawFundModal } from "./withdraw-fund-modal";
import { WalletList } from "./wallet-list";

export function WalletIndexSkeleton() {
  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Skeleton variant="shine" w="fit-content" loading>
          <Heading as="h5" fontSize="20px" fontWeight="600">
            Wallet
          </Heading>
        </Skeleton>

        <HStack flexDir={{ base: "column", sm: "column", "3sm": "row", md: "row" } as any}>
          <WalletStat label="wallet balance" valueText={<FormatNumber value={0} />} loading />
          <WalletStat bg="stat.2" label="Total  inflow" valueText={<FormatNumber value={0} />} loading />
          <WalletStat bg="stat.3" label="Total Outflow" valueText={<FormatNumber value={0} />} loading />
        </HStack>

        <Stack gap="16px">
          <Skeleton variant="shine" w="fit-content" loading>
            <Heading as="h6" fontSize="16px" fontWeight="700">
              Transaction history
            </Heading>
          </Skeleton>

          <HStack justifyContent="space-between">
            <SearchField loading />

            <HStack>
              <SkeletonCircle variant="shine" loading>
                <IconButton
                  variant="plain"
                  aria-label="Download transactions"
                  size="md"
                  w="40px"
                  h="32px"
                  _hover={{
                    "& :where(svg)": {
                      color: "white !important",
                    },
                  }}
                >
                  <Icon name="download" color="black" />
                </IconButton>
              </SkeletonCircle>

              <TableFilter loading />

              <Skeleton variant="shine" loading>
                <WithdrawFundModal />
              </Skeleton>
            </HStack>
          </HStack>

          <WalletList loading />
        </Stack>
      </Stack>
    </Container>
  );
}
