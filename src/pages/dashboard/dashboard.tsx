/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Alert,
  Button,
  DashboardNumberItem,
  FieldCard,
  GenderDonutChart,
  Icon,
  MultiLineChart,
  RatingIndexChart,
  Select,
  TopSpendingServiceChart,
} from "@/components";
import { useUser } from "@/hooks";
import { GraphRo, MostBookedDaysRo, StatRo, TopBookedServicesRo } from "@/interfaces";
import { dashboardStatsQueryOpts } from "@/queries";
import { Container, Grid, HStack, Show, Skeleton, Stack, Text } from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { eachMonthOfInterval } from "date-fns";
import { useMemo, useState } from "react";
import { useNavigate } from "react-router";

export function DashboardIndex() {
  const navigate = useNavigate();

  const { data: user_data } = useUser();
  const [booking_trend_year, setBookingTrendYear] = useState<string | undefined>("2025");
  const [most_booked_month, setMostBookedMonth] = useState<string | undefined>("1");

  const { data: stat_data, isPending: loading_stat } = useQuery(dashboardStatsQueryOpts<StatRo>("dashboard-booking-stat"));

  const { data: graph_data, isPending: loading_graph } = useQuery(
    dashboardStatsQueryOpts<GraphRo>("dashboard-booking-graph", booking_trend_year)
  );
  const { data: service_data, isPending: loading_service } = useQuery(
    dashboardStatsQueryOpts<TopBookedServicesRo>("dashboard-booking-service")
  );
  const { data: day_data, isPending: loading_most_booked } = useQuery(
    dashboardStatsQueryOpts<MostBookedDaysRo>("dashboard-booking-day", booking_trend_year, most_booked_month)
  );

  const user = user_data?.data;
  const graph_stat = graph_data?.data;
  const count_stat = stat_data?.data?.count_stat;
  const demo_stat = stat_data?.data?.demo_stat;
  const service_stat = service_data?.data;
  const most_booked_stat = day_data?.data;

  // const loading_stat = false;
  // const loading_graph = false;
  // const loading_service = false;

  // console.log("Stats", { graph_stat, count_stat, demo_stat, service_stat, day_data });

  const is_profile_complete = (user?.signup_stage?.provider ?? 0) > 6;

  const months = useMemo(() => {
    const m = eachMonthOfInterval({
      start: new Date(new Date().getFullYear(), 0),
      end: new Date(new Date().getFullYear(), 11),
    });
    return m.map((item) => ({
      label: item.toLocaleString("en-US", { month: "long" }),
      value: (item.getMonth() + 1).toString(),
      valueText: item.toLocaleString("en-US", { month: "long" }),
    }));
  }, []);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="24px">
        <Show when={!is_profile_complete}>
          <Alert
            status="error"
            border="1px solid"
            bg="stroke.error.50"
            borderColor="stroke.error"
            title="You're Almost There!"
            icon={<Icon name="triangle_alert" color="red" boxSize="24px" />}
            titleProps={{
              color: "stroke.error",
              fontSize: "14px",
              fontWeight: 600,
            }}
            justifyContent="space-between"
            endElement={
              <Button
                size="md"
                ml={{ sm: "30px" }}
                alignSelf={{ sm: "flex-start", "4sm": "center" } as any}
                onClick={() =>
                  navigate("/profile/complete-profile", {
                    viewTransition: true,
                  })
                }
              >
                Complete Profile
              </Button>
            }
          >
            <Text fontSize="14px" color="text.2">
              Complete your profile to appear in search results and start receiving bookings from clients looking for your expertise.
            </Text>
          </Alert>
        </Show>

        <Show when={[0, 2].includes(user?.status ?? 0) && !!is_profile_complete}>
          <Alert
            status="error"
            border="1px solid"
            bg="stroke.error.50"
            borderColor="stroke.error"
            title={user?.status === 0 ? "Awaiting Approval" : "Account Suspended"}
            icon={<Icon name="triangle_alert" color="red" boxSize="24px" />}
            titleProps={{
              color: "stroke.error",
              fontSize: "18px",
              fontWeight: 600,
            }}
            justifyContent="space-between"
            endElement={
              <Button
                size="md"
                ml={{ sm: "30px" }}
                alignSelf={{ sm: "flex-start", "4sm": "center" } as any}
                onClick={() =>
                  navigate("/profile", {
                    viewTransition: true,
                  })
                }
              >
                Visit Profile
              </Button>
            }
          >
            <Text fontSize="14px" color="text.2">
              {user?.status === 0
                ? "Thank you for completing your profile. Your account is currently under review, you’ll be notified once your access is granted. Please check your email for updates."
                : "Your account has been suspended by an administrator. If you believe this is a mistake or need more information, please contact support."}
            </Text>
          </Alert>
        </Show>

        <Stack gap={{ sm: "8px", "4sm": "24px" } as any}>
          <Grid
            templateColumns={
              {
                sm: "1fr",
                "1sm": "repeat(2, 1fr)",
                "4sm": "repeat(4, 1fr)",
                md: "repeat(4, 1fr)",
              } as any
            }
            gap={{ sm: "8px", "4sm": "20px" } as any}
          >
            <DashboardNumberItem
              flex=".45"
              icon="wallet"
              title="Wallet Balance"
              value={count_stat?.wallet_balance ?? 0}
              isMoneyValue
              loading={loading_stat}
              // hideBelow="3sm"
            />

            <DashboardNumberItem
              icon="users"
              title="Completed Appointment"
              value={count_stat?.total_appt_count ?? 0}
              loading={loading_stat}
            />
            <DashboardNumberItem
              icon="calendar_check"
              title="Upcoming Appointments"
              value={count_stat?.total_appt_upcoming ?? 0}
              loading={loading_stat}
            />

            <DashboardNumberItem
              icon="dollar_sign"
              title="Total Earnings"
              value={count_stat?.total_earning ?? 0}
              isMoneyValue
              loading={loading_stat}
            />
          </Grid>

          <FieldCard w="100%" p="16px" _content={{ gap: "52px" }} loading={loading_graph}>
            <HStack
              justifyContent="space-between"
              alignItems={{ sm: "flex-start", "1sm": "center", "2sm": "center" } as any}
              flexDir={{ sm: "column", "1sm": "row", "2sm": "row", md: "row" } as any}
            >
              <Skeleton variant={"shine"} loading={loading_graph}>
                <Text fontSize="14px" fontWeight="600" color="text.2" alignSelf={{ sm: "center", md: "flex-start" }}>
                  Booking Trend
                </Text>
              </Skeleton>

              <HStack flexWrap={{ base: "wrap" }}>
                <Skeleton variant="shine" rounded="full" loading={loading_graph}>
                  <Select
                    placeholder="Year"
                    minW="128px"
                    rounded="full"
                    value={[booking_trend_year || ""]}
                    items={[{ label: "2025", value: "2025", valueText: "Year 2025" }]}
                    triggerProps={{
                      rounded: "full",
                      minH: "38px",
                      bg: "primary.50",
                      color: "text.2",
                      fontSize: "14px",
                      fontWeight: 600,
                    }}
                    onValueChange={(e) => setBookingTrendYear(e.value[0])}
                  />
                </Skeleton>
              </HStack>
            </HStack>

            <MultiLineChart data={graph_stat} loading={loading_graph} />
          </FieldCard>

          <Grid templateColumns={{ sm: "1fr", "3sm": "1fr", md: ".5fr .75fr" } as any} gap={{ sm: "8px", "4sm": "32px" } as any}>
            <FieldCard
              p="16px"
              viewTransitionName="rating-index"
              loading={loading_service}
              _content={{
                gap: "20px",
                h: "100%",
                // justifyContent: "space-between",
              }}
            >
              <HStack
                justifyContent="space-between"
                alignItems={{ sm: "flex-start", "1sm": "center", "2sm": "center" } as any}
                flexDir={{ sm: "column", "1sm": "row", "2sm": "row", md: "row" } as any}
              >
                <Skeleton variant={"shine"} loading={loading_graph}>
                  <Text fontSize="14px" fontWeight="600" color="text.2" alignSelf={{ sm: "center", md: "flex-start" }}>
                    5 Most Booked Services
                  </Text>
                </Skeleton>
              </HStack>

              <RatingIndexChart gap="38px" stat={service_stat} loading={loading_service} />
            </FieldCard>

            <Grid
              templateColumns={
                {
                  sm: "1fr",
                  "3sm": "repeat(2, 1fr)",
                  md: ".9fr 1.6fr",
                } as any
              }
              gap={{ sm: "8px", "4sm": "32px" } as any}
            >
              <FieldCard p="16px" _content={{ gap: "52px", justifyContent: "space-between" }}>
                <HStack justifyContent="space-between">
                  <Skeleton variant={"shine"} loading={loading_graph}>
                    <Text fontSize="14px" fontWeight="600" color="text.2" alignSelf={{ sm: "center", md: "flex-start" }}>
                      Gender Distribution
                    </Text>
                  </Skeleton>
                </HStack>

                <GenderDonutChart data={demo_stat} loading={loading_graph} />
              </FieldCard>

              <FieldCard
                p="16px"
                _content={{
                  gap: "52px",
                  h: "100%",
                  justifyContent: "space-between",
                }}
              >
                <HStack
                  justifyContent="space-between"
                  alignItems={{ sm: "flex-start", "1sm": "center", "2sm": "center" } as any}
                  flexDir={{ sm: "column", "1sm": "row", "2sm": "row", md: "row" } as any}
                >
                  <Skeleton variant={"shine"} loading={loading_most_booked}>
                    <Text fontSize="14px" fontWeight="600" color="text.2" alignSelf={{ sm: "center", md: "flex-start" }}>
                      Most Booked Days
                    </Text>
                  </Skeleton>

                  <HStack flexWrap={{ base: "wrap" }}>
                    <Skeleton variant="shine" rounded="full" loading={loading_most_booked}>
                      <Select
                        placeholder="Month"
                        minW="128px"
                        rounded="full"
                        value={[most_booked_month || ""]}
                        items={months}
                        triggerProps={{
                          rounded: "full",
                          minH: "38px",
                          bg: "primary.50",
                          color: "text.2",
                          fontSize: "14px",
                          fontWeight: 600,
                        }}
                        onValueChange={(e) => setMostBookedMonth(e.value[0])}
                      />
                    </Skeleton>
                  </HStack>
                </HStack>

                <TopSpendingServiceChart data={most_booked_stat} loading={loading_most_booked} />
              </FieldCard>
            </Grid>
          </Grid>
        </Stack>
      </Stack>
    </Container>
  );
}
