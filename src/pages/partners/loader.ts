import { QueryClient } from "@tanstack/react-query";
import { partnerListCountQueryOpts, partnerListQueryOpts } from "@/queries";
import { LoaderFunctionArgs } from "react-router";
import { ensureQueryData } from "@/libs";

export function partnerIndexLoader(query_client: QueryClient) {
  return async ({ request }: LoaderFunctionArgs) => {
    const fetch = ensureQueryData(query_client);
    const search = new URL(request.url).search;
    const params = new URLSearchParams(search);
    if (!params.has("item_per_page")) params.set("item_per_page", "10");

    return await Promise.all([fetch(partnerListQueryOpts(params.toString())), fetch(partnerListCountQueryOpts(params.toString()))]);
  };
}

export type PartnerIndexLoaderDataType = Awaited<ReturnType<ReturnType<typeof partnerIndexLoader>>>;
