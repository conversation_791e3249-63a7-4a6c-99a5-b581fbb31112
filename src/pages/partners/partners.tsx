import { AppliedListFilters, Icon, SearchField } from "@/components";
import { Container, Heading, HStack, IconButton, Stack } from "@chakra-ui/react";
import { PartnerList } from "./ui/partner-list";
import { PartnerIndexLoaderDataType } from "./loader";
import { useLoaderData } from "react-router";
import { useListFilter } from "@/hooks";
import { useQuery } from "@tanstack/react-query";
import { partnerListCountQueryOpts, partnerListQueryOpts } from "@/queries";
import { toQueryString } from "@/utils";
import { TableFilter } from "./ui/table-filter";

export function PartnersIndex() {
  const [initial_list, initial_count] = useLoaderData() as PartnerIndexLoaderDataType;

  const filters = useListFilter({ page: 1, item_per_page: 10, q: "" });
  const { filter, setFilter } = filters;

  const { data: list, isFetching, isPending } = useQuery({ ...partnerListQueryOpts(toQueryString(filter)), initialData: initial_list });
  const { data: count } = useQuery({ ...partnerListCountQueryOpts(toQueryString(filter)), initialData: initial_count });

  const handlePageChange = (page: number) => {
    setFilter({ page });
  };

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Heading as="h5" fontSize="20px" fontWeight="600">
          Partners
        </Heading>

        <HStack justifyContent="space-between">
          <SearchField value={filter?.q} onChange={(value) => setFilter({ q: value })} loading={isPending} />

          <HStack>
            <IconButton
              variant="plain"
              aria-label="Download appointments"
              size="md"
              w="40px"
              h="32px"
              viewTransitionName="bell-image"
              _hover={{
                "& :where(svg)": {
                  color: "white !important",
                },
              }}
              display="none"
            >
              <Icon name="download" color="black" />
            </IconButton>

            <TableFilter loading={isPending} filters={filter} onFilterChange={setFilter} />
          </HStack>
        </HStack>

        <AppliedListFilters keyPath="partner" loading={isFetching && isPending} {...filters} />

        <PartnerList
          filter={filter}
          onPageChange={handlePageChange}
          loading={isFetching && isPending}
          list={{ ...list, ...count?.data }}
          emptyListStackProps={{ my: "22svh" }}
        />
      </Stack>
    </Container>
  );
}
