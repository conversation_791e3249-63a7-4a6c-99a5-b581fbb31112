import { AppliedListFilters, <PERSON><PERSON>crumb, Icon, SearchField } from "@/components";
import { toQueryString } from "@/utils";
import { Container, Heading, HStack, IconButton, SkeletonCircle, Stack } from "@chakra-ui/react";

import { useLocation, useParams } from "react-router";
import { useListFilter } from "@/hooks";
import { appointmentListCountQueryOpts, appointmentsQueryOpts } from "@/queries";
import { useQuery } from "@tanstack/react-query";
import { TableFilter } from "@/pages/appointments/ui/table-filter";
import { AppointmentList } from "@/pages/appointments/ui/appointment-list";

export function PartnerAppointments() {
  const params = useParams();
  const location = useLocation();

  const state = location?.state;
  const org_id: string = params?.id || state?.id;

  const filters = useListFilter({ page: 1, item_per_page: 10, org_id, q: "" });
  const { filter, setFilter } = filters;

  const { data: appt_data, isPending: loading_appt } = useQuery(appointmentsQueryOpts(toQueryString(filter)));
  //   const { data: appt_stats, isPending: loading_stats } = useQuery(appointmentStatsQueryOpts(toQueryString({ org_id })));
  const { data: appt_count, isPending: loading_appt_count } = useQuery(appointmentListCountQueryOpts(toQueryString({ org_id })));

  const loading = loading_appt || loading_appt_count;

  const handlePageChange = (page: number) => {
    setFilter({ page });
  };

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Partners", to: `/partners/`, is_first: true },
            { title: "Details", to: `/partners/${org_id}` },
            { title: "Appointments", to: "#" },
          ]}
        />

        <Heading as="h5" fontSize="20px" fontWeight="600">
          Appointments
        </Heading>

        {/* <FieldCard heading="Appointments" headingProps={{ fontWeight: 700 }}> */}
        {/* <Grid templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(4, 1fr)" }} gap={{ base: "10px", md: "20px" }}>
            {stats.map((item, i) => (
              <Details
                key={`detail-${i}`}
                py={{ base: "16px", md: "24px" }}
                px="16px"
                flexDir="row"
                loading={loading_stats}
                orientation="vertical"
                justifyContent="space-between"
                _itemStyles={{
                  title: { fontSize: "14px" },
                  description: { fontSize: "18px", fontWeight: 500 },
                }}
                items={[
                  {
                    title: item.title,
                    description: when<ReactNode | number>(
                      !item?.title.includes("earnings"),
                      item.value,
                      <LocaleProvider locale="en-NG">
                        <FormatNumber style="currency" currency="NGN" value={item.value} />
                      </LocaleProvider>
                    ),
                  },
                ]}
              />
            ))}
          </Grid> */}

        <HStack justifyContent="space-between">
          <SearchField value={filter?.q} onChange={(value) => setFilter({ q: value })} loading={loading} />

          <HStack>
            <SkeletonCircle variant="shine" loading={loading}>
              <IconButton
                variant="plain"
                aria-label="Download appointments"
                size="md"
                w="40px"
                h="32px"
                _hover={{
                  "& :where(svg)": {
                    color: "white !important",
                  },
                }}
                display="none"
              >
                <Icon name="download" color="black" />
              </IconButton>
            </SkeletonCircle>

            <TableFilter loading={loading} filters={filter} onFilterChange={setFilter} />
          </HStack>
        </HStack>

        <AppliedListFilters keyPath="appointment" omitList={["org_id"]} loading={loading} {...filters} />

        <AppointmentList filter={filter} onPageChange={handlePageChange} list={{ ...appt_data, ...appt_count?.data }} loading={loading} />
        {/* </FieldCard> */}
      </Stack>
    </Container>
  );
}
