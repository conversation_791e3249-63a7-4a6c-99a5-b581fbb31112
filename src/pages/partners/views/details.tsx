/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON>read<PERSON><PERSON>b, Button, Details, Empty, FieldCard, Icon, ProviderStatus, toPartnerTableData } from "@/components";
import { ServiceRateBadge } from "@/pages/setup/setup";
import { Avatar, Container, FormatNumber, Grid, Heading, HStack, Show, SkeletonCircle, Stack, Text, VStack } from "@chakra-ui/react";
import { ModifyPartnerModal } from "../ui/modify-modal";
import { useQuery } from "@tanstack/react-query";
import { appointmentStatsQueryOpts, getPartnerByIdQueryOpts } from "@/queries";
import { useLocation, useNavigate, useParams } from "react-router";
import { toQueryString } from "@/utils";
import { PartnerListDataRo } from "@/interfaces";
import { useMemo } from "react";
import capitalize from "lodash.capitalize";

export function PartnerDetails() {
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const state = location.state;
  const org_id = params?.id || state?.id;

  const { data, isPending: loading_part } = useQuery(getPartnerByIdQueryOpts(org_id));
  const { data: appt_stats, isPending: loading_stats } = useQuery(appointmentStatsQueryOpts(toQueryString({ org_id })));

  const part = toPartnerTableData(data?.data || [])?.[0];
  const name = part?.name || state?.name || "";
  const avatar = part?.avatar || state?.avatar;
  const status = (part?.status || state?.status) as string;
  const services = ((part?.services || state?.services || []) as PartnerListDataRo["service_data"]).map((item) => ({
    service_offer_id: item?.service_offer_id,
    service_offer_name: item?.name,
    amount: item?.min_amount,
    description: "",
  }));

  const loading = loading_part || loading_stats;

  const stats = useMemo(() => {
    const stat = appt_stats?.data;
    return [
      { title: "Total Appointments", description: stat?.total_count || 0 },
      { title: "Upcoming", description: stat?.total_upcoming || 0 },
      { title: "Completed", description: stat?.total_completed || 0 },
      { title: "Canceled", description: stat?.total_cancel || 0 },
    ];
  }, [appt_stats]);

  //   console.log("Stats Data", stats_data);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Partners", to: "/partners", is_first: true },
            { title: capitalize(name), to: "#" },
          ]}
        />

        <HStack
          w="100%"
          rounded="8px"
          bg="bkg2"
          p="16px"
          gap="16px"
          justifyContent="space-between"
          flexDir={{ "2smDown": "column", "3sm": "row" } as any}
          alignItems={{ "2smDown": "flex-start", "3sm": "center" } as any}
        >
          <HStack gap="16px">
            <SkeletonCircle variant="shine" loading={false}>
              <Avatar.Root boxSize="64px" border="2px solid" borderColor="stroke.divider">
                <Avatar.Fallback fontSize="12px" name={name} />
                {avatar && <Avatar.Image src={avatar} />}
              </Avatar.Root>
            </SkeletonCircle>

            <Stack gap="2px">
              <Heading as="h6" fontSize="18px" fontWeight={600} color="text" textTransform="capitalize">
                {name}
              </Heading>

              <ProviderStatus status={status} variant="badge" loading={false} />
            </Stack>
          </HStack>

          <HStack>
            <Show when={["pending"].includes(status?.toLowerCase())}>
              <ModifyPartnerModal org_id={org_id} name={name} actionType="decline">
                <Button
                  variant="plain"
                  leftIcon={<Icon name="plus" rotate="45deg" color="inherit" />}
                  css={{ "--before-bg": "colors.red.500" }}
                >
                  Decline Invite
                </Button>
              </ModifyPartnerModal>
            </Show>

            <Show when={["pending"].includes(status?.toLowerCase())}>
              <ModifyPartnerModal org_id={org_id} name={name} actionType="accept">
                <Button variant="solid" leftIcon={<Icon name="badge_check" color="inherit" />}>
                  Accept Invite
                </Button>
              </ModifyPartnerModal>
            </Show>

            <Show when={["active"].includes(status?.toLowerCase())}>
              <ModifyPartnerModal org_id={org_id} name={name} actionType="suspend">
                <Button variant="solid" leftIcon={<Icon name="octagon_pause" color="inherit" />}>
                  Suspend Partnership
                </Button>
              </ModifyPartnerModal>
            </Show>

            <Show when={["suspended"].includes(status?.toLowerCase())}>
              <ModifyPartnerModal org_id={org_id} name={name} actionType="activate">
                <Button variant="solid" leftIcon={<Icon name="badge_check" color="inherit" />}>
                  Activate Partnership
                </Button>
              </ModifyPartnerModal>
            </Show>
          </HStack>
        </HStack>

        <Details
          p="16px"
          orientation="vertical"
          _itemStyles={{
            wrapper: { gap: "8px" },
            title: { fontSize: "16px", fontWeight: 600, color: "text" },
            description: { fontSize: "14px", color: "text.2" },
          }}
          items={[
            {
              title: "About",
              description: part?.description || state?.description || "N/A",
            },
          ]}
        />

        <Show when={["active", "suspended"].includes(status?.toLowerCase())}>
          <Grid templateColumns={{ "2smDown": "1fr", md: "2fr 1fr 1fr" } as any} gap="20px">
            <Details
              p="16px"
              loading={loading}
              orientation="vertical"
              justifyContent="space-between"
              flexDir={{ "2smDown": "column", "3sm": "row" } as any}
              onClick={() => navigate(`/partners/${org_id}/appointments`, { viewTransition: true })}
              _itemStyles={{
                title: { fontSize: "14px" },
                description: { fontSize: "18px", fontWeight: 500 },
              }}
              items={stats}
            />
            <Details
              p="16px"
              flexDir="row"
              loading={loading}
              orientation="vertical"
              justifyContent="space-between"
              onClick={() => navigate(`/partners/${org_id}/earnings`, { viewTransition: true })}
              _itemStyles={{
                title: { fontSize: "14px" },
                description: { fontSize: "18px", fontWeight: 500 },
              }}
              items={[
                {
                  title: "Total Earnings",
                  description: (
                    <Text fontSize="inherit" fontWeight="500">
                      <FormatNumber style="currency" currency="NGN" value={appt_stats?.data?.total_spent || 0} />
                    </Text>
                  ),
                },
              ]}
            />
            <Details
              p="16px"
              flexDir="row"
              loading={loading}
              orientation="vertical"
              justifyContent="space-between"
              _itemStyles={{
                title: { fontSize: "14px" },
                description: { fontSize: "18px", fontWeight: 500 },
              }}
              items={[
                {
                  title: "Services",
                  description: services.length,
                },
              ]}
            />
          </Grid>
        </Show>

        <FieldCard p="16px">
          <HStack w="100%" justifyContent="space-between">
            <Stack gap="2px">
              <Text fontSize="16px" fontWeight="600" color="text">
                Service List
              </Text>
              <Text fontSize="12px" color="text.2">
                Here you see the services and amount this partner is offering you
              </Text>
            </Stack>

            {/* <Button
              variant="subtle"
              size="md"
              leftIcon={<Icon name="pen" color="text" />}
              _hover={{ "& *": { color: "white" } }}
              //   onClick={() => navigate("/setup/service-rates", { viewTransition: true })}
            >
              Edit
            </Button> */}
          </HStack>

          <Show when={services.length < 1 && !loading}>
            <VStack my="10px">
              <Empty subtitle="This partner has not set their service services yet" />
            </VStack>
          </Show>

          <Show when={services.length > 0 && !loading}>
            <HStack gap="16px" flexWrap="wrap">
              {services.map((rate) => (
                <ServiceRateBadge key={rate.service_offer_id} rate={rate} />
              ))}
            </HStack>
          </Show>

          <Show when={services.length < 1 && loading}>
            <HStack gap="16px" flexWrap="wrap">
              <ServiceRateBadge loading={loading} />
              <ServiceRateBadge loading={loading} />
              <ServiceRateBadge loading={loading} />
              <ServiceRateBadge loading={loading} />
              <ServiceRateBadge loading={loading} />
              <ServiceRateBadge loading={loading} />
            </HStack>
          </Show>
        </FieldCard>
      </Stack>
    </Container>
  );
}
