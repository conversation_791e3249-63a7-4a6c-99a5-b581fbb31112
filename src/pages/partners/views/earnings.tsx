import { AppliedListFilters, <PERSON>readcrumb, Icon, <PERSON>Field } from "@/components";
import { Container, <PERSON><PERSON>, HStack, IconButton, Stack } from "@chakra-ui/react";
import { useLocation, useParams } from "react-router";
import { useQuery } from "@tanstack/react-query";
import { partnerEarningsQueryOpts } from "@/queries";
import { useListFilter } from "@/hooks";
import { toQueryString } from "@/utils";
import { TableFilter } from "@/pages/wallet/ui/table-filter";
import { WalletList } from "@/pages/wallet/ui/wallet-list";

export function PartnerEarnings() {
  const params = useParams();
  //   const navigate = useNavigate();
  const location = useLocation();

  const state = location?.state;
  const org_id: string = params?.id || state?.id;

  const filters = useListFilter({ page: 1, item_per_page: 10, q: "" });
  const { filter, setFilter } = filters;

  const { data: list, isPending } = useQuery(partnerEarningsQueryOpts(org_id, toQueryString({ ...filter, org_id })));

  // const list = toWalletTableData(data?.data || []);
  const loading = isPending;

  const handlePageChange = (page: number) => {
    setFilter({ page });
  };

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap={{ sm: "12px", md: "16px" }}>
        <Breadcrumb
          items={[
            { title: "Partners", to: "/partners", is_first: true },
            { title: "Details", to: `/partners/${org_id}` },
            { title: "Earnings", to: "#" },
          ]}
        />

        <Heading as="h5" fontSize="20px" fontWeight="600">
          Earnings
        </Heading>

        <HStack justifyContent="space-between">
          <SearchField value={filter?.q} onChange={(value) => setFilter({ q: value })} loading={loading} />

          <HStack>
            <IconButton
              variant="plain"
              aria-label="Download transactions"
              size="md"
              w="40px"
              h="32px"
              _hover={{
                "& :where(svg)": {
                  color: "white !important",
                },
              }}
              display="none"
            >
              <Icon name="download" color="black" />
            </IconButton>

            <TableFilter filters={filter} onFilterChange={setFilter} />
          </HStack>
        </HStack>

        <AppliedListFilters keyPath="wallet" loading={loading} {...filters} />

        <WalletList list={list} filter={filter} onPageChange={handlePageChange} loading={loading} />
      </Stack>
    </Container>
  );
}
