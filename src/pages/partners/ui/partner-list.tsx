import { <PERSON>istFilter, PartnerListRo } from "@/interfaces";
import { getStatusColor } from "@/utils";
import {
  Avatar,
  HStack,
  Separator,
  Show,
  Skeleton,
  SkeletonCircle,
  SkeletonText,
  Stack,
  StackProps,
  Table,
  Text,
  VStack,
} from "@chakra-ui/react";
import { useNavigate } from "react-router";
import { useMemo } from "react";
import { PartnerTableDataType, Empty, Paginator, ProviderStatus, toPartnerTableData } from "@/components";

interface PartnerListProps {
  list?: PartnerListRo;
  filter?: IListFilter;

  onPageChange?: (page: number) => void;
  loading?: boolean;

  emptyListStackProps?: StackProps;
}

export function PartnerList(props: PartnerListProps) {
  const { list, filter, loading, onPageChange, emptyListStackProps } = props;

  const navigate = useNavigate();

  const total = useMemo(() => list?.total || 1, [list]);
  const items = useMemo(() => toPartnerTableData(list?.data ?? raw_items), [list]);
  const isEmpty = useMemo(() => !list || !list.data || list.data.length === 0, [list]);

  return (
    <Stack gap="20px" minH="82svh" justifyContent="space-between">
      <Table.ScrollArea hideBelow="3sm">
        <Table.Root interactive>
          <Table.Header>
            <Table.Row
              bg="input"
              rounded="4px"
              color="text.2"
              css={{
                "& > th": {
                  borderBottom: "none",
                  _first: {
                    borderTopLeftRadius: "4px",
                    borderBottomLeftRadius: "4px",
                  },
                  _last: {
                    borderTopRightRadius: "4px",
                    borderBottomRightRadius: "4px",
                  },
                },
              }}
            >
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Date & Time
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Name of Organisation
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Status
                </SkeletonText>
              </Table.ColumnHeader>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {items.map((item) => (
              <Table.Row
                key={item.id}
                // border="none"
                py="100px"
                viewTransitionName={`partner-${item?.id}`}
                border="1px solid transparent"
                borderBottomColor="stroke.divider"
                _last={{ border: "none" }}
                css={{
                  "& > td": {
                    color: "text.2",
                    borderBottom: "none",
                    py: "14px",
                    _first: {
                      borderTopLeftRadius: "4px",
                      borderBottomLeftRadius: "4px",
                    },
                    _last: {
                      borderTopRightRadius: "4px",
                      borderBottomRightRadius: "4px",
                    },
                  },

                  _hover: {
                    bg: "primary.50",
                    cursor: "pointer",
                  },
                }}
                onClick={() => navigate(`/partners/${item.id}`, { state: item, viewTransition: true })}
              >
                <Table.Cell>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.datetime}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell textTransform="capitalize">
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.name}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell color={`${getStatusColor(item?.status as string)} !important`}>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.status}
                  </SkeletonText>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </Table.ScrollArea>

      <Stack gap="8px" hideFrom="3sm">
        {items.map((item) => (
          <PartnerMobileListItem key={item.id} partner={item} loading={loading} />
        ))}
      </Stack>

      <Show when={isEmpty && !loading}>
        <VStack w="100%" my="10vh" {...emptyListStackProps}>
          <Empty subtitle="Partners will appear here" />
        </VStack>
      </Show>

      <Show when={filter && !loading}>
        <Paginator
          count={total}
          defaultPage={1}
          page={filter?.page}
          pageSize={filter?.item_per_page || 1}
          onPageChange={(e) => onPageChange?.(e.page)}
        />
      </Show>
    </Stack>
  );
}

export interface PartnerMobileListItemProps extends Omit<StackProps, "id"> {
  partner: PartnerTableDataType;
  loading?: boolean;
}

export function PartnerMobileListItem(props: PartnerMobileListItemProps) {
  const { partner, loading = true, ...xprops } = props;
  const navigate = useNavigate();

  const { id, name, status, avatar } = partner;

  // console.log("appointment item ID", id);

  return (
    <Stack
      py="16px"
      px="12px"
      // bg="input"
      rounded="8px"
      gap="8px"
      role="button"
      cursor="pointer"
      focusRingColor="primary"
      border="1px solid"
      borderColor="stroke.divider"
      onClick={() =>
        navigate(`/partners/${id}`, {
          viewTransition: true,
          state: partner,
        })
      }
      {...xprops}
    >
      <HStack justifyContent="space-between">
        <SkeletonText variant="shine" noOfLines={1} loading={loading}>
          <Text fontSize="12px" fontWeight="600" color="text.2" textTransform="uppercase">
            {partner.datetime}
          </Text>
        </SkeletonText>
        <ProviderStatus fontSize="12px" fontWeight="500" variant="badge" status={status as string} loading={loading} />
      </HStack>

      <SkeletonText variant="shine" h="4px" noOfLines={1} loading={loading}>
        <Separator borderColor="stroke.divider" />
      </SkeletonText>

      <HStack justifyContent="space-between">
        <Skeleton variant="shine" loading={loading}>
          <HStack gap="4px">
            {/* <Icon name="stethoscope" boxSize="12px" color="text.3" /> */}
            <SkeletonCircle variant="shine" loading={false}>
              <Avatar.Root boxSize="34px" border="2px solid" borderColor="stroke.divider">
                <Avatar.Fallback fontSize="12px" name={name} />
                {avatar && <Avatar.Image src={avatar} />}
              </Avatar.Root>
            </SkeletonCircle>

            <Text fontSize="14px" fontWeight="400" color="text" textTransform="capitalize">
              {name}
            </Text>
          </HStack>
        </Skeleton>

        <Skeleton variant="shine" loading={loading}>
          <Text fontSize="14px" fontWeight="500" color="text.2">
            ₦{partner?.min_amount || 0} - ₦{partner?.max_amount || 0}
          </Text>
        </Skeleton>
      </HStack>

      {/* <SkeletonText variant="shine" noOfLines={1} loading={loading}>
        <Text fontSize="12px" fontWeight="400" color="text.3">
          Booked by: James
        </Text>
      </SkeletonText> */}
    </Stack>
  );
}

const raw_items = [
  {
    id: 1,
    datetime: "Today • 12:34pm",
    name: "Adegboyoga Precious",
    status: "Upcoming",
  },
  {
    id: 2,
    datetime: "Today • 12:34pm",
    name: "Jide Kosoko",
    status: "Pending",
  },
  {
    id: 3,
    datetime: "Today • 12:34pm",
    name: "Nneka Chukwu",
    status: "Completed",
  },
  {
    id: 4,
    datetime: "Today • 12:34pm",
    name: "Adebayo Salami",
    status: "Canceled",
  },
  {
    id: 5,
    datetime: "Today • 12:34pm",
    name: "Desmond Tutu",
    status: "Completed",
  },
  {
    id: 6,
    datetime: "Today • 12:34pm",
    name: "Adebanji Bolaji",
    status: "Completed",
  },
  {
    id: 7,
    datetime: "Today • 12:34pm",
    name: "Baba Kaothat",
    status: "Completed",
  },
  {
    id: 8,
    datetime: "Today • 12:34pm",
    name: "Jibike Alarape",
    status: "Completed",
  },
  {
    id: 9,
    datetime: "Today • 12:34pm",
    name: "Eze Chinedu",
    status: "Completed",
  },
];
