import { FieldCard, FilterMenu, Select } from "@/components";
import { usePartialState } from "@/hooks";
import { IListFilter, PartnerListDataRo } from "@/interfaces";
import { useRef } from "react";

type Filter = IListFilter & Partial<Pick<PartnerListDataRo, "status">>;

interface TableFilterProps {
  filters?: Filter;
  onFilterChange?: (filters: Filter) => void;

  loading?: boolean;
}

export function TableFilter(props: TableFilterProps) {
  const { filters: raw_filters, loading, onFilterChange } = props;

  const contentRef = useRef<HTMLDivElement | null>(null);

  const [filters, setFilters] = usePartialState<Filter>(raw_filters || {}, [raw_filters]);
  const { status } = filters || {};

  const handleApplyFilter = () => {
    onFilterChange?.({ ...filters, page: 1 });
  };

  return (
    <FilterMenu loading={loading} onApply={handleApplyFilter} ref={contentRef}>
      <FieldCard heading="Filter" border="none" p="16px">
        <Select
          // size="xs"
          label="Status"
          placeholder="Select"
          value={[status ? status.toString() : ""]}
          portalContainerRef={contentRef}
          onValueChange={(e) => setFilters({ status: e.value[0] })}
          items={[
            { label: "Pending", value: "0" },
            { label: "Active", value: "1" },
            { label: "Suspended", value: "2" },
            { label: "Canceled", value: "3" },
          ]}
        />
      </FieldCard>
    </FilterMenu>
  );
}
