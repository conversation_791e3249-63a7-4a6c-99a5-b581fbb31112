import { Dialog, Field, IconButton, Portal, Stack, useDisclosure } from "@chakra-ui/react";
import { <PERSON><PERSON>, FieldErrorInfo, Icon, ShowFieldWhen, SubmitButton, toaster, useAppForm } from "@/components";
import { SyntheticEvent, useMemo, useRef } from "react";
import { useMutation } from "@/hooks";
import { tryCatch } from "@/utils";
import { declinePartnerInviteSchema, suspendOrActivateSchema } from "@/schemas";
import { declineSuspendOrActivatePartnerMutationOpts } from "@/queries";

interface ModifyPartnerModalProps extends Dialog.RootProps {
  name: string;
  org_id: string;
  onSuspend?: (data: string) => void;
  actionType?: "suspend" | "decline" | "activate" | "accept";
  showDocumentTypeInButtonName?: boolean;
}

type FormType = typeof declinePartnerInviteSchema.infer;

export function ModifyPartnerModal(props: ModifyPartnerModalProps) {
  const { org_id, name, actionType = "decline", children, ...xprops } = props;

  const disclosure = useDisclosure();

  const contentRef = useRef<HTMLDivElement | null>(null);

  const status = actionType === "suspend" ? "2" : actionType === "decline" ? "3" : "1";

  const schema = useMemo(() => {
    if (actionType === "decline") return declinePartnerInviteSchema;
    return suspendOrActivateSchema;
  }, [actionType]);

  const mutationOpts = declineSuspendOrActivatePartnerMutationOpts(org_id);
  const { mutateAsync, isPending: loading } = useMutation(mutationOpts);

  const form = useAppForm({
    defaultValues: {
      status,
      reason: "",
    } as FormType,
    validators: {
      onChange: schema,
      onSubmit: schema,
    },
    async onSubmit({ value }) {
      console.log("Suspend value", value);
      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: `${name} has been ${actionType}d successfully`,
        });
        form.reset();
        disclosure.onClose();
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  const content = useMemo(() => {
    type ActionContentType = { title: string; description: string; actionLabel: string };

    const map: Record<NonNullable<typeof actionType>, ActionContentType> = {
      suspend: {
        title: `Suspend Partnership?`,
        description: `This action will suspend ${name}'s partnership`,
        actionLabel: "Suspend",
      },
      decline: {
        title: `Decline Invite?`,
        description: `This action will decline ${name}'s invite`,
        actionLabel: "Decline Invite",
      },
      activate: {
        title: `Activate Partnership?`,
        description: `Accepting the invite means you agree with the payment this partner is willing to pay you for your services with them`,
        actionLabel: "Activate",
      },
      accept: {
        title: `Accept Invite`,
        description: `Accepting the invite means you agree with the payment this partner is willing to pay you for your services with them`,
        actionLabel: "Accept Invite",
      },
    };
    return map[actionType];
  }, [actionType, name]);

  return (
    <Dialog.Root placement="center" open={disclosure.open} onOpenChange={(e) => disclosure.setOpen(e.open)} {...xprops}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <form.AppForm>
            <Dialog.Content ref={contentRef} rounded="16px" p="24px" maxW="490px" gap="24px" bg="white" as="form" onSubmit={handleSubmit}>
              <Stack>
                <Dialog.Header p="0" justifyContent="space-between">
                  <Dialog.Title fontSize="24px" fontWeight={700} lineHeight="130%">
                    {content.title}
                  </Dialog.Title>

                  <Dialog.CloseTrigger asChild pos="relative" top="unset">
                    <IconButton
                      variant="plain"
                      aria-label="Close buy units modal"
                      size="sm"
                      w="40px"
                      h="32px"
                      _hover={{
                        "& :where(svg)": {
                          color: "white !important",
                        },
                      }}
                      css={{ "--before-bg": "{colors.primary}" }}
                    >
                      <Icon name="close" color="stroke.checkbox" />
                    </IconButton>
                  </Dialog.CloseTrigger>
                </Dialog.Header>

                <Dialog.Description>{content.description}</Dialog.Description>
              </Stack>

              <Dialog.Body p="0">
                <ShowFieldWhen when={(values) => values?.status === "2" || values?.status === "3"}>
                  <Stack gap="24px">
                    <form.AppField name="reason">
                      {(field) => (
                        <Field.Root invalid={field.state.meta.errors.length > 0}>
                          <field.Textarea
                            placeholder="Reason"
                            minH="194px"
                            value={field.state.value}
                            onChange={(e) => field.setValue(e.target.value)}
                          />
                          <FieldErrorInfo field={field} />
                        </Field.Root>
                      )}
                    </form.AppField>
                  </Stack>
                </ShowFieldWhen>
              </Dialog.Body>

              <Dialog.Footer py="0">
                <Dialog.ActionTrigger asChild>
                  <Button w="50%" size="md" variant="subtle" disabled={loading}>
                    Cancel
                  </Button>
                </Dialog.ActionTrigger>

                {/* <Button
                w="50%"
                size="md"
                onClick={() => onSuspend?.("dummy data")}
              >
                Suspend
              </Button> */}

                <SubmitButton w="50%" size="md" loading={loading}>
                  {content.actionLabel}
                </SubmitButton>
              </Dialog.Footer>
            </Dialog.Content>
          </form.AppForm>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
