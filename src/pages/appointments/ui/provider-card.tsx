import { Badge, Box, FormatNumber, HStack, Image, LocaleProvider, Skeleton, SkeletonText, Stack, StackProps, Text } from "@chakra-ui/react";

// import placeholder_img from "@/assets/images/provider-avatar.png";
import { useMemo } from "react";
import { getStatusColor } from "@/utils";
import { Link } from "react-router";
// import take from "lodash.take";
import { Icon } from "@/components";

interface ProviderCardProps extends StackProps {
  id?: string;
  name: string;
  profession: string;
  status: string;
  avatar?: string;
  to?: string;
  rating?: number;
  tier?: string;
  tiers?: { name: string }[];
  loading?: boolean;
  accountType?: string;
  charge?: number;
  selected?: boolean;
}
export function ProviderCard(props: ProviderCardProps) {
  const {
    id,
    name,
    profession,
    status,
    // tier,
    // tiers = [],
    // rating,
    avatar,
    to,
    accountType,
    charge,
    loading = false,
    selected = false,
    ...xprops
  } = props;

  const as = selected ? Link : undefined;
  const _to = selected ? undefined : to;

  //   const tier_names = useMemo(() => {
  //     if (tiers.length < 1) return "N/A";

  //     const names =
  //       take(tiers, 2)
  //         .map((t) => t?.name)
  //         .join(", ") ||
  //       tier ||
  //       "N/A";
  //     if (tiers.length > 2) names.concat(`, +${tiers.length - 2}`);
  //     return names;
  //   }, [tiers, tier]);

  return (
    <Stack
      as={as}
      rounded="8px"
      bg="white"
      p="4px"
      border="1px solid"
      borderColor="stroke.divider"
      overflow="hidden"
      viewTransitionName={`disabled`}
      data-selected={selected}
      cursor="pointer"
      {...{ to: _to }}
      css={{
        "&:is([data-selected=true])": {
          pos: "relative",
          borderColor: "primary",
          _after: {
            content: "''",
            pos: "absolute",
            boxSize: "10px",
            bg: "primary",
            top: "20px",
            right: "20px",
            rounded: "full",
          },
        },
      }}
      {...xprops}
    >
      <Skeleton variant="shine" loading={loading}>
        <Box w="100%" pos="relative" rounded="4px" overflow="hidden" h={{ base: "160px", md: "160px" }}>
          <Image src={avatar} objectFit="cover" w="100%" h="100%" viewTransitionName={`provider-avatar-${id || "1"}`} />

          {accountType && (
            <Badge
              pos="absolute"
              top="8px"
              left="8px"
              color="text.2"
              bg="primary.50"
              px="6px"
              py="6px"
              rounded="6px"
              display="flex"
              gap="6px"
              fontSize="17px"
              textTransform="capitalize"
            >
              <Icon name="stethoscope" boxSize="18px" color="text" />
              {accountType || "Individual"}
            </Badge>
          )}
        </Box>
      </Skeleton>

      <Stack py="16px" px="14px" gap="12px">
        <Stack gap="4px">
          <SkeletonText w="fit-content" noOfLines={1} variant="shine" loading={loading}>
            <Text fontSize="14px" fontWeight="500" color="text" textTransform="capitalize">
              {name}
            </Text>
          </SkeletonText>

          <SkeletonText variant="shine" noOfLines={1} loading={loading}>
            <Text fontSize="12px" fontWeight="400" color="text.2" textTransform="capitalize">
              {profession}
            </Text>
          </SkeletonText>
        </Stack>

        <HStack w="100%" justifyContent="space-between">
          {/* <Rating value={rating || 4.5} loading={loading} /> */}

          {/* {!!tier && <TierIndicator value={tier_names} loading={loading} />} */}

          {!charge && <ProviderStatus variant="plain" status={status} loading={loading} />}

          {!!charge && (
            <Skeleton variant="shine" loading={loading}>
              <LocaleProvider locale="en-NG">
                <Text fontSize="14px" fontWeight="500" color="text.2">
                  Charge From <FormatNumber style="currency" value={charge || 0} currency="NGN" />
                </Text>
              </LocaleProvider>
            </Skeleton>
          )}
        </HStack>
      </Stack>
    </Stack>
  );
}

interface TierIndicatorProps extends StackProps {
  value: string | number;
  loading?: boolean;
}
export function TierIndicator(props: TierIndicatorProps) {
  const { value, loading, ...xprops } = props;

  const tier = useMemo(() => {
    if (typeof value === "string") return value;
    return `Tier ${value}`;
  }, [value]);

  return (
    <Skeleton variant="shine" loading={loading}>
      <HStack {...xprops}>
        <Icon name="shield_half" />
        <Text fontSize="12px" color="text">
          {tier}
        </Text>
      </HStack>
    </Skeleton>
  );
}

interface ProviderStatusProps extends StackProps {
  variant?: "plain" | "badge";
  status: string;
  loading?: boolean;
}
export function ProviderStatus(props: ProviderStatusProps) {
  const { variant, status, loading, ...xprops } = props;

  const color = useMemo(() => getStatusColor(status), [status]);

  if (variant === "plain") {
    return (
      <HStack {...xprops}>
        <Skeleton variant="shine" loading={loading}>
          <Text fontSize="14px" fontWeight="500" color={color} textTransform="capitalize">
            {status}
          </Text>
        </Skeleton>
      </HStack>
    );
  }

  return (
    <Skeleton variant="shine" loading={loading}>
      <Badge py="4px" px="8px" maxW="fit-content" color="white" textTransform="capitalize" bg={color} {...xprops}>
        {status}
      </Badge>
    </Skeleton>
  );
}
