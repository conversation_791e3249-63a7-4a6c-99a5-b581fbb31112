import { FieldCard, FilterMenu, Select, TextField } from "@/components";
import { usePartialState } from "@/hooks";
import { IListFilter, AppointmentListDataRo } from "@/interfaces";
import { getDateRange } from "@/utils";
import { Show, Stack } from "@chakra-ui/react";
import { useRef } from "react";

type Filter = IListFilter & Partial<Pick<AppointmentListDataRo, "status">>;

interface TableFilterProps {
  filters?: Filter;
  onFilterChange?: (filters: Filter) => void;

  loading?: boolean;
}

export function TableFilter(props: TableFilterProps) {
  const { filters: raw_filters, loading, onFilterChange } = props;

  const contentRef = useRef<HTMLDivElement | null>(null);

  const [filters, setFilters, , set] = usePartialState<Filter>(raw_filters || {}, [raw_filters]);
  const { status } = filters || {};

  const handleApplyFilter = () => {
    onFilterChange?.({ ...filters, page: 1 });
  };

  const handleRange = (range: Filter["range"]) => {
    if (range?.value && range.value !== "custom") {
      const date_range = getDateRange(range.value);
      range = { ...range, ...date_range };
    }
    set((state) => ({ ...state, range: { ...state?.range, ...range } }));
  };

  return (
    <FilterMenu loading={loading} onApply={handleApplyFilter} ref={contentRef}>
      <FieldCard
        heading="Filter"
        border="none"
        p="16px"
        // css={{
        //   maxW: "240px",
        // }}
      >
        {/* <FilterRadioGroup
            heading="Date"
            value={filters?.range?.value}
            onValueChange={(e) => handleRange({ value: e.value as string })}
            items={[
              { label: "Today", value: "today" },
              { label: "This Week", value: "this_week" },
              { label: "This Month", value: "this_month" },
              {
                label: "Custom",
                value: "custom",
                show_content: true,
              },
            ]}
          >
            <Stack gap="16px">
              <TextField
                _root={{ minW: "100%" }}
                label="From"
                type={"date"}
                endElement={null}
                value={filters?.range?.start_date}
                onChange={(e) => handleRange({ start_date: e.target.value })}
              />
              <TextField
                _root={{ minW: "100%" }}
                label="To"
                type={"date"}
                endElement={null}
                value={filters?.range?.end_date}
                onChange={(e) => handleRange({ end_date: e.target.value })}
              />
            </Stack>
          </FilterRadioGroup> */}

        <Stack gap="16px">
          <Select
            // size="xs"
            label="Date"
            placeholder="Select"
            value={[filters?.range?.value || ""]}
            portalContainerRef={contentRef}
            onValueChange={(e) => handleRange({ value: e.value[0] })}
            items={[
              { label: "Today", value: "today" },
              { label: "This Week", value: "this_week" },
              { label: "This Month", value: "this_month" },
              { label: "Range", value: "custom" },
            ]}
            // maxW="240px"
            // triggerProps={{
            //   h: "32px",
            // }}
          />

          <Show when={filters?.range?.value === "custom"}>
            <TextField
              _root={{ minW: "100%" }}
              label="From"
              type={"date"}
              endElement={null}
              value={filters?.range?.start_date}
              onChange={(e) => handleRange({ start_date: e.target.value })}
            />
            <TextField
              _root={{ minW: "100%" }}
              label="To"
              type={"date"}
              endElement={null}
              value={filters?.range?.end_date}
              onChange={(e) => handleRange({ end_date: e.target.value })}
            />
          </Show>
        </Stack>

        <Select
          // size="xs"
          label="Status"
          placeholder="Select"
          value={[status ? status.toString() : ""]}
          portalContainerRef={contentRef}
          onValueChange={(e) => setFilters({ status: e.value[0] })}
          items={[
            { label: "Upcoming", value: "1" },
            { label: "Pending", value: "0" },
            { label: "Completed", value: "3" },
            { label: "Canceled", value: "4" },
            { label: "Live", value: "2" },
          ]}
        />

        {/* <FilterRadioGroup
            heading="Status"
            value={status ? status.toString() : undefined}
            onValueChange={(e) => setFilters({ status: e.value as string })}
            items={[
              { label: "Upcoming", value: "1" },
              { label: "Pending", value: "0" },
              { label: "Completed", value: "3" },
              { label: "Canceled", value: "4" },
              { label: "Live", value: "2" },
            ]} 
          />*/}
      </FieldCard>
    </FilterMenu>
  );
}
