/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components";
import { Container, Heading, HStack, IconButton, Input, InputGroup, Skeleton, SkeletonCircle, Stack } from "@chakra-ui/react";

import { TableFilter } from "./table-filter";
import { AppointmentList } from "./appointment-list";

export function AppointmentIndexSkeleton() {
  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Skeleton asChild w="fit-content" variant="shine" loading>
          <Heading as="h5" fontSize="20px" fontWeight="600">
            Appointments
          </Heading>
        </Skeleton>

        <HStack justifyContent="space-between">
          <Skeleton asChild w="fit-content" variant="shine" loading>
            <InputGroup maxW="300px" startElement={<Icon name="search" />}>
              <Input variant="subtle" placeholder="Search appointment" />
            </InputGroup>
          </Skeleton>

          <HStack>
            <SkeletonCircle asChild variant="shine" loading>
              <IconButton
                variant="plain"
                aria-label="Download appointments"
                size="md"
                w="40px"
                h="32px"
                _hover={{
                  "& :where(svg)": {
                    color: "white !important",
                  },
                }}
              >
                <Icon name="download" color="black" />
              </IconButton>
            </SkeletonCircle>

            <TableFilter loading />
          </HStack>
        </HStack>

        <HStack fontSize="14px" color="text.3" alignItems={{ sm: "flex-start", "2sm": "center" } as any}>
          <Skeleton variant="shine" loading>
            <Button
              variant="plain"
              color="text"
              size="sm"
              textDecoration="underline"
              textDecorationColor="text.3"
              _hover={{ textDecor: "none", "& > *": { color: "white" } }}
            >
              Clear all filters
            </Button>
          </Skeleton>
        </HStack>

        <AppointmentList loading />
      </Stack>
    </Container>
  );
}
