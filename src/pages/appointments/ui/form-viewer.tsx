/* eslint-disable @typescript-eslint/no-explicit-any */
import { AppointmentDiagnosisDataRo, AppointmentMedicalPrescriptionDataRo, GeneralFormDataRo, MedicalRecordDataRo } from "@/interfaces";
import { SubstanceAbuseFormDto } from "@/schemas";
import { Accordion, Grid, HStack, Show, Skeleton, SkeletonCircle, Span, Stack, Text } from "@chakra-ui/react";
import { useCallback } from "react";

type RecordType = "assessment" | "tools" | "treatment-plan" | "diagnosis" | "medication" | "progress-note";

interface Props<T = GeneralFormDataRo> {
  record_type?: RecordType;
  data: MedicalRecordDataRo<T>["data"];
}

export function FormViewer<T>(props: Props<T>) {
  const { record_type, data: raw } = props;
  // console.log("Questions and Answers", props);

  const data = raw as GeneralFormDataRo[];

  const getTabname = useCallback(
    (item: GeneralFormDataRo) => {
      // if(record_type === "assessment") return "Assessment";
      // if (record_type === "tools") return "Tools";
      // if (record_type === "treatment-plan") return "Treatment Plan";
      // if (record_type === "diagnosis") return "Diagnosis";
      // if (record_type === "medication") return "Medication";
      if (record_type === "progress-note") return "Progress Note";
      return item?.data_name;
    },
    [record_type]
  );

  if (record_type === "diagnosis") {
    return <DiagnosisViewer {...props} />;
  }

  if (record_type === "medication") {
    return <MedicationViewer {...props} />;
  }

  return (
    <Stack gap="16px">
      {data.map((items, index) => (
        <Stack key={`item-${index}`}>
          {/* <FormHeading type="heading" id={`item-${index}`} label={items.data_name} /> */}
          <Accordion.Root collapsible display="flex" flexDir="column" gap="16px" /*value={value} onValueChange={(e) => setValue(e.value)}*/>
            <Accordion.Item
              p="16px"
              key={index}
              value={`item--${index}`}
              border="1px solid"
              borderColor="stroke.divider"
              rounded="8px"
              css={{
                "&[data-state='open']": {
                  borderColor: "primary",
                  backgroundColor: "primary.50/20",
                },
              }}
              viewTransitionName={`accordion-item--${index}`}
            >
              <Accordion.ItemTrigger p="0" justifyContent="space-between">
                <Skeleton w="fit-content" variant="shine" loading={false}>
                  <Span flex="1" textTransform="capitalize" color="text.2">
                    {getTabname(items)}
                  </Span>
                </Skeleton>

                <SkeletonCircle variant="shine" loading={false}>
                  <Accordion.ItemIndicator />
                </SkeletonCircle>
              </Accordion.ItemTrigger>
              <Accordion.ItemContent>
                <Accordion.ItemBody pb="0">
                  <Stack gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
                    {items?.data_name?.toLowerCase() === "substance abuse" && record_type === "assessment" ? (
                      <AssessmentSubstanceAbuse data={items.data as unknown as SubstanceAbuseFormDto["data"]} />
                    ) : (
                      <Grid
                        templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)" } as any}
                        gap="32px"
                        alignItems="end"
                      >
                        {items.data
                          .filter((t) => !!t?.label)
                          .map((item, i) => (
                            <Stack key={`field-${i}`} gap="8px">
                              <Text color="text.3" fontSize="16px">
                                {item?.label || "---"}
                              </Text>
                              <Text fontSize="16px" textTransform="capitalize">
                                {item?.value_text || item?.value || "---"}
                              </Text>
                            </Stack>
                          ))}
                      </Grid>
                    )}
                  </Stack>
                </Accordion.ItemBody>
              </Accordion.ItemContent>
            </Accordion.Item>
          </Accordion.Root>
        </Stack>
      ))}
    </Stack>
  );
}

function DiagnosisViewer<T>(props: Props<T>) {
  const { data } = props;

  //   console.log("Diagnosis Viewer", data);
  const diag = data as AppointmentDiagnosisDataRo[];

  return (
    <Stack gap="16px">
      {diag.map((item, index) => (
        <Stack key={`item-${index}`}>
          {/* <FormHeading type="heading" id={`item-${index}`} label={items.data_name} /> */}
          <Accordion.Root collapsible display="flex" flexDir="column" gap="16px" /*value={value} onValueChange={(e) => setValue(e.value)}*/>
            <Accordion.Item
              p="16px"
              key={index}
              value={`item--${index}`}
              border="1px solid"
              borderColor="stroke.divider"
              rounded="8px"
              css={{
                "&[data-state='open']": {
                  borderColor: "primary",
                  backgroundColor: "primary.50/20",
                },
              }}
              viewTransitionName={`accordion-item--${index}`}
            >
              <Accordion.ItemTrigger p="0" justifyContent="space-between">
                <HStack>
                  <Span>{item.code10}</Span>
                  <Span>/</Span>
                  <Span color="text.2">{item.code9}</Span>
                  <Span>-</Span>
                  <Span color="text.3" maxW={{ sm: "40svw", "2sm": "100%" } as any} truncate>
                    {item.description}
                  </Span>
                </HStack>

                <Accordion.ItemIndicator />
              </Accordion.ItemTrigger>
              <Accordion.ItemContent>
                <Accordion.ItemBody pb="0">
                  <Stack gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
                    <Grid
                      templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)" } as any}
                      gap="32px"
                      alignItems="end"
                    >
                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          ICD-10
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.code10 || "---"}
                        </Text>
                      </Stack>

                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          ICD-9
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.code9 || "---"}
                        </Text>
                      </Stack>

                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          Description
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.description || "---"}
                        </Text>
                      </Stack>
                    </Grid>
                  </Stack>
                </Accordion.ItemBody>
              </Accordion.ItemContent>
            </Accordion.Item>
          </Accordion.Root>
        </Stack>
      ))}
    </Stack>
  );
}

function MedicationViewer<T>(props: Props<T>) {
  const { data } = props;

  //   console.log("Diagnosis Viewer", data);
  const medi = data as AppointmentMedicalPrescriptionDataRo[];

  return (
    <Stack gap="16px">
      {medi.map((item, index) => (
        <Stack key={`item-${index}`}>
          {/* <FormHeading type="heading" id={`item-${index}`} label={items.data_name} /> */}
          <Accordion.Root collapsible display="flex" flexDir="column" gap="16px" /*value={value} onValueChange={(e) => setValue(e.value)}*/>
            <Accordion.Item
              p="16px"
              key={index}
              value={`item--${index}`}
              border="1px solid"
              borderColor="stroke.divider"
              rounded="8px"
              css={{
                "&[data-state='open']": {
                  borderColor: "primary",
                  backgroundColor: "primary.50/20",
                },
              }}
              viewTransitionName={`accordion-item--${index}`}
            >
              <Accordion.ItemTrigger p="0" justifyContent="space-between">
                <HStack>
                  <Span textTransform="capitalize">{item.med_name}</Span>
                </HStack>

                <Accordion.ItemIndicator />
              </Accordion.ItemTrigger>
              <Accordion.ItemContent>
                <Accordion.ItemBody pb="0">
                  <Stack gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
                    <Grid
                      templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)" } as any}
                      gap="32px"
                      alignItems="end"
                    >
                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          Medication Name
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.med_name || "---"}
                        </Text>
                      </Stack>

                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          Dosage
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.med_dosage || "---"}
                        </Text>
                      </Stack>

                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          Frequency
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.med_freq || "---"}
                        </Text>
                      </Stack>

                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          Type
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.med_type || "---"}
                        </Text>
                      </Stack>
                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          Start Date
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.start_date || "---"}
                        </Text>
                      </Stack>

                      <Stack gap="8px">
                        <Text color="text.3" fontSize="16px">
                          End Date
                        </Text>
                        <Text fontSize="16px" textTransform="capitalize">
                          {item?.end_date || "---"}
                        </Text>
                      </Stack>
                    </Grid>
                  </Stack>
                </Accordion.ItemBody>
              </Accordion.ItemContent>
            </Accordion.Item>
          </Accordion.Root>
        </Stack>
      ))}
    </Stack>
  );
}

function AssessmentSubstanceAbuse(props: { data: SubstanceAbuseFormDto["data"] }) {
  const { data } = props;

  // console.log("Assessment Substance Abuse", data);

  return data.map((item, index) => (
    <Grid
      key={`subabs-${index}`}
      templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)" } as any}
      gap="32px"
      alignItems="end"
    >
      <Stack gap="8px">
        <Text color="text.3" fontSize="16px">
          Substance Type
        </Text>
        <Text fontSize="16px" textTransform="capitalize">
          {item?.substance_abuse || "---"}
        </Text>
      </Stack>
      <Stack gap="8px">
        <Text color="text.3" fontSize="16px">
          Last Use Date
        </Text>
        <Text fontSize="16px">{item?.last_use_date || "---"}</Text>
      </Stack>
      <Stack gap="8px">
        <Text color="text.3" fontSize="16px">
          Frequency of Use
        </Text>
        <Text fontSize="16px">{item?.last_frequency_use || "---"}</Text>
      </Stack>
      <Stack gap="8px">
        <Text color="text.3" fontSize="16px">
          Money Spent
        </Text>
        <Text fontSize="16px">{item?.money_spent || "---"}</Text>
      </Stack>
      <Stack gap="8px">
        <Text color="text.3" fontSize="16px">
          Treatment History
        </Text>
        <Text fontSize="16px">{item?.abuse_treatment || "---"}</Text>
      </Stack>

      <Show when={(item?.substance_abuse || "").toLowerCase() === "alcohol"}>
        {[
          { label: "1. Drink any alcohol (more than a few sips)?", value: item?.any_alcohol },
          { label: "2. Smoke any marijuana or hashish?", value: item?.smoke_hashish },
          { label: "3. Use anything else to get High?", value: item?.anything_high },
          {
            label: "Have you ever ridden in a CAR driven by someone (including) yourself who was high or had been using alcohol or drugs?",
            value: item?.ridden_car,
          },
          { label: "Do you ever use alcohol or drugs to RELAX, feel better about yourself, or fit in?", value: item?.alcohol_relax },
          { label: "Do you ever use alcohol or drugs while you are by yourself or ALONE?", value: item?.alcohol_alone },
          { label: "Do you ever FORGET things you did while using drugs or alcohol?", value: item?.forget_drugs },
          {
            label: "Do you ever have any FRIENDS or FAMILY who you can talk to about your drug or alcohol problems?",
            value: item?.friends_family,
          },
          { label: "Do you ever get into TROUBLE while you are using drugs or alcohol?", value: item?.gotten_trouble },
        ].map((q, i) => (
          <Stack key={`alcohol-q-${i}`} gap="8px">
            <Text color="text.3" fontSize="16px">
              {q.label}
            </Text>
            <Text fontSize="16px">{q.value || "---"}</Text>
          </Stack>
        ))}
      </Show>

      <Show when={(item?.substance_abuse || "").toLowerCase() !== "alcohol"}>
        {[
          { label: "1. Taken in large amount of substance or over a long period than was intended", value: item?.large_amount },
          {
            label: "2. There is a persistent desire or unsuccessful efforts to cut down or control substance use.",
            value: item?.persistent_desire,
          },
          {
            label: "3.A great deal of time is spent in activities necessary to obtain, use, or recover from its effects of substance.",
            value: item?.great_deal,
          },
          {
            label: "4. Craving or a strong desire or urge to use a substance",
            value: item?.craving_desire,
          },
          {
            label: "5. Recurrent use resulting in failure to fulfill major role obligations at work, school, or home.",
            value: item?.recurrent_resulting,
          },
          {
            label:
              "6. Continues substance use despite persistent/recurrent social or interpersonal problems caused/exacerbated by the effects of substance use.",
            value: item?.continues_substance,
          },
          {
            label: "7. Important social, occupational or recreational activities are given up or reduced because of substance use",
            value: item?.occupational_activities,
          },
          {
            label: "8. Recurrent substance use in situations in which it is physically hazardous.",
            value: item?.physically_hazardous,
          },
          {
            label:
              "9. Use is continued despite knowledge of having a persistent or recurrent physical or psychological problem that is likely to have been caused or exacerbated by substance use.",
            value: item?.psychological_problem,
          },
          {
            label: "10. Tolerance as defined by either of the following:",
            value: item?.tolerance_defined,
          },
          {
            label: "a. A need for markedly increased amount to achieve intoxication or desired effect.",
            value: item?.markedly_increased,
          },
          {
            label: "b. A markedly diminished effect with continued use of the same amount.",
            value: item?.markedly_diminished,
          },
          {
            label: "c. The characteristic withdrawal syndrome for substance",
            value: item?.characteristic_withdrawal,
          },
          {
            label: "The characteristic withdrawal syndrome for substance Answer",
            value: item?.characteristic_withdrawal_answer,
          },
          {
            label: "d. Have all exiting withdrawal symptoms been checked.",
            value: item?.exiting_withdrawal,
          },
          {
            label: "e. Substance (or closely related substance) taken to relive/avoid withdrawal symptoms.",
            value: item?.closely_related_substance,
          },
        ].map((q, i) => (
          <Stack key={`alcohol-q-${i}`} gap="8px">
            <Text color="text.3" fontSize="16px">
              {q.label}
            </Text>
            <Text fontSize="16px">{q.value || "---"}</Text>
          </Stack>
        ))}
      </Show>
    </Grid>
  ));
}
