/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON>, I<PERSON>, ShowFieldWhen, SubmitButton, toaster, useAppForm, withForm } from "@/components";
import { SubstanceAbuseFormDto, substanceAbuseFormSchema } from "@/schemas";
import { PLAIN_YES_NO_OPTIONS } from "@/static";
import { ButtonGroup, Grid, HStack, Span, Stack } from "@chakra-ui/react";
import { formOptions } from "@tanstack/react-form";
import { FormHeading } from "./form-builder";
import { useMutation } from "@/hooks";
import { createAssessmentMutationOpts, deleteAssessmentMutationOpts } from "@/queries";
import { tryCatch } from "@/utils";
import { IGenericAppointmentFormCategory } from "@/interfaces";
import { useMemo } from "react";

interface Props {
  appt_id: string;
  form_data?: IGenericAppointmentFormCategory & { update_id?: string };
  loading?: boolean;
  accordionCloseFn?: VoidFunction;
  removeFromSelectedItems?: (id: string) => void;
}

type SingleSubstanceAbuseFormDto = SubstanceAbuseFormDto["data"][0];

function getRandomStr() {
  return Math.random().toString(36).substring(7);
}

const empty_substance_abuse = (id: string) =>
  ({
    temp_id: id,
    substance_abuse: "",
    last_use_date: "",
    last_frequency_use: "",
    money_spent: "",
    abuse_treatment: "",
  } as SingleSubstanceAbuseFormDto);

const form_opts = formOptions({
  defaultValues: {
    appointment_id: "",
    data: [empty_substance_abuse(`subabs-${getRandomStr()}`)],
  } as SubstanceAbuseFormDto,
});

export function SubstanceAbuseForm(props: Props) {
  const { appt_id, form_data: raw, accordionCloseFn, removeFromSelectedItems } = props;

  const form_data = useMemo(() => ({ ...raw, forms: (raw?.forms as unknown as SingleSubstanceAbuseFormDto[]) || [] }), [raw]);

  const { mutateAsync, isPending: saving } = useMutation(createAssessmentMutationOpts(appt_id));
  const { mutateAsync: deleteAssessment, isPending: deleting } = useMutation(
    deleteAssessmentMutationOpts(appt_id, form_data?.update_id || "")
  );

  const default_form_opts = useMemo(() => {
    const forms = form_data?.forms || [];
    return {
      ...form_opts,
      defaultValues: {
        ...form_opts.defaultValues,
        data:
          forms.length > 0
            ? forms?.map((f) => ({ ...f, temp_id: f?.temp_id || `subabs-${getRandomStr()}` }))
            : [empty_substance_abuse(`subabs-${getRandomStr()}`)],
      },
    };
  }, [form_data]);

  const form = useAppForm({
    ...default_form_opts,
    validators: {
      onChange: substanceAbuseFormSchema,
      onSubmit: substanceAbuseFormSchema,
    },
    async onSubmit({ value }) {
      console.log("Form value", value);

      const promise = mutateAsync({
        appointment_id: appt_id || "",
        data_name: form_data?.category,
        data_tab: form_data?.id,
        data: value.data,
        data_id: form_data?.update_id,
      });

      const result = await tryCatch(promise);
      if (result.ok) {
        accordionCloseFn?.();
        toaster.success({
          title: "Success",
          description: "Form has been submitted successfully",
        });
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  const handleDelete = async () => {
    if (form_data?.update_id) {
      const promise = deleteAssessment({});
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: `${form_data?.category || "Assessment"} form has been deleted successfully`,
        });
      }
    }

    removeFromSelectedItems?.(form_data?.id || "");
  };

  return (
    <form.AppForm>
      <Stack gap="16px" as="form" onSubmit={handleSubmit}>
        <Stack gap="16px">
          <form.AppField name="data" mode="array">
            {(field) =>
              field.state.value.map((_, index) => (
                <Stack gap="16px">
                  {index > 0 && (
                    <FormHeading
                      type="heading"
                      id="substance_abuse"
                      label={`SUBSTANCE ABUSE ${index + 1}`}
                      bg="primary.50"
                      color="primary"
                    />
                  )}
                  <Stack key={index} gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
                    <form.AppField name={`data[${index}].substance_abuse`}>
                      {(field) => (
                        <field.Select
                          label="1. Substance Abuse?"
                          field={field}
                          value={[field.state.value]}
                          onBlur={field.handleBlur}
                          onValueChange={(e) => field.setValue(e.value[0])}
                          items={[
                            { label: "Alcohol", value: "Alcohol" },
                            { label: "Amphetamine", value: "Amphetamine" },
                            { label: "Cannabis", value: "Cannabis" },
                            { label: "Cocaine", value: "Cocaine" },
                            { label: "Inhalant", value: "Inhalant" },
                            { label: "Opioid", value: "Opioid" },
                            { label: "Other Hallucinogen", value: "Other Hallucinogen" },
                            { label: "Other Stimulant", value: "Other Stimulant" },
                            { label: "Phencyclidine", value: "Phencyclidine" },
                            { label: "Sed/Hyp/Anx", value: "Sed/Hyp/Anx" },
                            { label: "Tobacco", value: "Tobacco" },
                            { label: "Other or Unknown", value: "Other or Unknown" },
                          ]}
                        />
                      )}
                    </form.AppField>

                    <Grid templateColumns="repeat(3, 1fr)" gap="16px">
                      <form.AppField name={`data[${index}].last_use_date`}>
                        {(field) => (
                          <field.TextField
                            type="date"
                            label="When was your last use"
                            field={field}
                            value={field.state.value}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.setValue(e.target.value)}
                          />
                        )}
                      </form.AppField>

                      <form.AppField name={`data[${index}].last_frequency_use`}>
                        {(field) => (
                          <field.Select
                            label="Last Frequency of Use"
                            field={field}
                            value={[field.state.value]}
                            onBlur={field.handleBlur}
                            onValueChange={(e) => field.setValue(e.value[0])}
                            items={[
                              { label: "Daily", value: "Daily" },
                              { label: "1_2 Times Per Week", value: "1_2 Times Per Week" },
                              { label: "3_6 Times Per Week", value: "3_6 Times Per Week" },
                              { label: "1_3 Times In The Last 30 Days", value: "1_3 Times In The Last 30 Days" },
                            ]}
                          />
                        )}
                      </form.AppField>

                      <form.AppField name={`data[${index}].money_spent`}>
                        {(field) => (
                          <field.TextField
                            label="Money Spent"
                            field={field}
                            value={field.state.value}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.setValue(e.target.value)}
                          />
                        )}
                      </form.AppField>
                    </Grid>

                    <form.AppField name={`data[${index}].abuse_treatment`}>
                      {(field) => (
                        <field.Textarea
                          label="Substance Abuse Treatment and Hospitalizations in past two years"
                          field={field}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.setValue(e.target.value)}
                          minH="100px"
                        />
                      )}
                    </form.AppField>

                    <ShowFieldWhen
                      when={(state) => (state.data[index] as SingleSubstanceAbuseFormDto).substance_abuse.toLowerCase() === "alcohol"}
                    >
                      <AlcoholFormVariant form={form as any} index={index} />
                    </ShowFieldWhen>

                    <ShowFieldWhen
                      when={(state) => (state.data[index] as SingleSubstanceAbuseFormDto).substance_abuse.toLowerCase() !== "alcohol"}
                    >
                      <OtherFormVariant form={form as any} index={index} />
                    </ShowFieldWhen>

                    {index > 0 && (
                      <HStack mt="28px">
                        <Button
                          w="fit-content"
                          variant="subtle"
                          leftIcon={<Icon name="trash" color="inherit" />}
                          onClick={() => form.removeFieldValue("data", index)}
                          css={{ "--before-bg": "colors.red.500" }}
                          color="red.500"
                        >
                          Remove Substance Abuse {index + 1}
                        </Button>
                      </HStack>
                    )}
                  </Stack>
                </Stack>
              ))
            }
          </form.AppField>

          <ButtonGroup gap="16px" justifyContent="flex-end">
            <Button
              variant="subtle"
              leftIcon={<Icon name="plus" color="inherit" />}
              onClick={() => form.pushFieldValue("data", empty_substance_abuse(`subabs-${getRandomStr()}`) as any)}
              color="primary"
              disabled={saving}
            >
              Add Substance Abuse
            </Button>

            <Span flex="1" />

            <Button
              w="fit-content"
              variant="solid"
              leftIcon={<Icon name="trash" color="inherit" />}
              onClick={handleDelete}
              css={{ "--before-bg": "colors.red.500" }}
              disabled={deleting || saving}
              loading={deleting}
            >
              Delete
            </Button>

            <SubmitButton disabled={deleting || saving} loading={saving}>
              Save & Update
            </SubmitButton>
          </ButtonGroup>
        </Stack>
      </Stack>
    </form.AppForm>
  );
}

const AlcoholFormVariant = withForm({
  ...form_opts,
  props: { index: -1 },
  render(props) {
    const { form, index } = props;
    return (
      <Stack gap="16px">
        <Grid templateColumns="repeat(3, 1fr)" gap="16px" alignItems="end">
          <form.AppField name={`data[${index}].any_alcohol`}>
            {(field) => (
              <field.Select
                label="1. Drink any alcohol (more than a few sips)?"
                field={field}
                value={[field.state.value || ""]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={PLAIN_YES_NO_OPTIONS}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].smoke_hashish`}>
            {(field) => (
              <field.Select
                label="2. Smoke any marijuana or hashish?"
                field={field}
                value={[field.state.value || ""]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={PLAIN_YES_NO_OPTIONS}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].anything_high`}>
            {(field) => (
              <field.Select
                label="3. Use anything else to get High?"
                field={field}
                value={[field.state.value || ""]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={PLAIN_YES_NO_OPTIONS}
              />
            )}
          </form.AppField>
        </Grid>

        <FormHeading
          type="heading"
          id="instructions"
          label="Part B"
          description="Anything else includes illegal drugs, over the counter and prescription drugs, and things that you sniff or 'Huff'."
          subDescription="If you answered NO to ALL(A1,A2,A3) answer only B1 below, then STOP If you answered YES to ANY A1 to A3) answer B1 to B6 below"
        />

        <Stack gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
          <Grid templateColumns="repeat(3, 1fr)" gap="16px" alignItems="end">
            <form.AppField name={`data[${index}].ridden_car`}>
              {(field) => (
                <field.Select
                  label="1. Have you ever ridden in a CAR driven by someone (including) yourself who was high or had been using alcohol or drugs?"
                  field={field}
                  value={[field.state.value || ""]}
                  onBlur={field.handleBlur}
                  onValueChange={(e) => field.setValue(e.value[0])}
                  items={PLAIN_YES_NO_OPTIONS}
                />
              )}
            </form.AppField>
            <form.AppField name={`data[${index}].alcohol_relax`}>
              {(field) => (
                <field.Select
                  label="2. Do you ever use alcohol or drugs to RELAX, feel better about yourself, or fit in?"
                  field={field}
                  value={[field.state.value || ""]}
                  onBlur={field.handleBlur}
                  onValueChange={(e) => field.setValue(e.value[0])}
                  items={PLAIN_YES_NO_OPTIONS}
                />
              )}
            </form.AppField>
            <form.AppField name={`data[${index}].alcohol_alone`}>
              {(field) => (
                <field.Select
                  label="3. Do you ever use alcohol or drugs while you are by yourself or ALONE?"
                  field={field}
                  value={[field.state.value || ""]}
                  onBlur={field.handleBlur}
                  onValueChange={(e) => field.setValue(e.value[0])}
                  items={PLAIN_YES_NO_OPTIONS}
                />
              )}
            </form.AppField>
            <form.AppField name={`data[${index}].forget_drugs`}>
              {(field) => (
                <field.Select
                  label="4. Do you ever FORGET things you did while using drugs or alcohol?"
                  field={field}
                  value={[field.state.value || ""]}
                  onBlur={field.handleBlur}
                  onValueChange={(e) => field.setValue(e.value[0])}
                  items={PLAIN_YES_NO_OPTIONS}
                />
              )}
            </form.AppField>
            <form.AppField name={`data[${index}].friends_family`}>
              {(field) => (
                <field.Select
                  label="5. Do your FRIENDS or FAMILY ever tell you that you should cut down on your drinking or drug use?"
                  field={field}
                  value={[field.state.value || ""]}
                  onBlur={field.handleBlur}
                  onValueChange={(e) => field.setValue(e.value[0])}
                  items={PLAIN_YES_NO_OPTIONS}
                />
              )}
            </form.AppField>
            <form.AppField name={`data[${index}].gotten_trouble`}>
              {(field) => (
                <field.Select
                  label="6. Have you ever gotten into TROUBLE while you were using alcohol or drugs?"
                  field={field}
                  value={[field.state.value || ""]}
                  onBlur={field.handleBlur}
                  onValueChange={(e) => field.setValue(e.value[0])}
                  items={PLAIN_YES_NO_OPTIONS}
                />
              )}
            </form.AppField>
          </Grid>
        </Stack>
      </Stack>
    );
  },
});

const OtherFormVariant = withForm({
  ...form_opts,
  props: { index: -1 },
  render(props) {
    const { form, index } = props;
    return (
      <Stack gap="16px">
        <Grid templateColumns="repeat(2, 1fr)" gap="16px" alignItems="end">
          <form.AppField name={`data[${index}].large_amount`}>
            {(field) => (
              <field.TextField
                label="1. Taken in large amount of substance or over a long period than was intended"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].persistent_desire`}>
            {(field) => (
              <field.TextField
                label="2. There is a persistent desire or unsuccessful efforts to cut down or control substance use."
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].great_deal`}>
            {(field) => (
              <field.TextField
                label="3.A great deal of time is spent in activities necessary to obtain, use, or recover from its effects of substance."
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].craving_desire`}>
            {(field) => (
              <field.TextField
                label="4. Craving or a strong desire or urge to use a substance"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].recurrent_resulting`}>
            {(field) => (
              <field.TextField
                label="5. Recurrent use resulting in failure to fulfill major role obligations at work, school, or home."
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].continues_substance`}>
            {(field) => (
              <field.TextField
                label="6. Continues substance use despite persistent/recurrent social or interpersonal problems caused/exacerbated by the effects of substance use."
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].occupational_activities`}>
            {(field) => (
              <field.TextField
                label="7. Important social, occupational or recreational activities are given up or reduced because of substance use"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].physically_hazardous`}>
            {(field) => (
              <field.TextField
                label="8. Recurrent substance use in situations in which it is physically hazardous."
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].psychological_problem`}>
            {(field) => (
              <field.TextField
                label="9. Use is continued despite knowledge of having a persistent or recurrent physical or psychological problem that is likely to have been caused or exacerbated by substance use."
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].tolerance_defined`}>
            {(field) => (
              <field.TextField
                label="10. Tolerance as defined by either of the following:"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].markedly_increased`}>
            {(field) => (
              <field.Select
                label="a. A need for markedly increased amount to achieve intoxication or desired effect."
                field={field}
                value={[field.state.value || ""]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={PLAIN_YES_NO_OPTIONS}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].markedly_diminished`}>
            {(field) => (
              <field.TextField
                label="b. A markedly diminished effect with continued use of the same amount."
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].characteristic_withdrawal`}>
            {(field) => (
              <field.Select
                label="c. The characteristic withdrawal syndrome for substance"
                field={field}
                value={[field.state.value || ""]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={[
                  { label: "Withdrawal", value: "withdrawal" },
                  { label: "Content", value: "content" },
                ]}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].characteristic_withdrawal_answer`}>
            {(field) => (
              <field.TextField
                label="The characteristic withdrawal syndrome for substance Answer"
                field={field}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.setValue(e.target.value)}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].exiting_withdrawal`}>
            {(field) => (
              <field.Select
                label="d. Have all exiting withdrawal symptoms been checked."
                field={field}
                value={[field.state.value || ""]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={PLAIN_YES_NO_OPTIONS}
              />
            )}
          </form.AppField>
          <form.AppField name={`data[${index}].closely_related_substance`}>
            {(field) => (
              <field.Select
                label="e. Substance (or closely related substance) taken to relive/avoid withdrawal symptoms."
                field={field}
                value={[field.state.value || ""]}
                onBlur={field.handleBlur}
                onValueChange={(e) => field.setValue(e.value[0])}
                items={PLAIN_YES_NO_OPTIONS}
              />
            )}
          </form.AppField>
        </Grid>
      </Stack>
    );
  },
});
