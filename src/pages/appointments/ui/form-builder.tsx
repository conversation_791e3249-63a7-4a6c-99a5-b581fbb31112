/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, SubmitButton, Textarea, useAppForm, withForm } from "@/components";
import {
  IGenericAppointmentDateForm,
  IGenericAppointmentForm,
  IGenericAppointmentFormCalculationResult,
  IGenericAppointmentFormHeading,
  IGenericAppointmentFormOptionTotal,
  IGenericAppointmentFormScoringRange,
  IGenericAppointmentNumberForm,
  IGenericAppointmentSelectForm,
  IGenericAppointmentTextareaForm,
  IGenericAppointmentTextForm,
  IGenericUnresolvedAppointmentSelectForm,
} from "@/interfaces";
import { GenericAssessmentFormDto, genericAssessmentFormSchema } from "@/schemas";
import { form_builder_grid_groups } from "@/static";
import { getArrayChanges } from "@/utils";
import { ButtonGroup, Grid, HStack, List, Separator, Show, Stack, StackProps, Table, Text } from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { type } from "arktype";
import omit from "lodash.omit";
import pick from "lodash.pick";
import { useMemo } from "react";

interface FormBuilderProps {
  /** Name of the form */
  form_name: string;

  /** List of form data */
  form_data: IGenericAppointmentForm[];

  /** Unique id of the form */
  form_id: string;

  appointment_id: string;

  /** Required for updating the form, when available, the form is being updated */
  update_id?: string;

  deleting?: boolean;
  saving?: boolean;

  onSubmit?: (values: GenericAssessmentFormDto["data"]) => Promise<void>;
  onDelete?: () => Promise<void>;

  onChange?: (changes: ReturnType<typeof getArrayChanges<GenericAssessmentFormDto["data"][number]>>) => void;
}

type GenericAppointmentFormTypeWithIndex = IGenericAppointmentForm & { index: number };

export function FormBuilder(props: FormBuilderProps) {
  const { appointment_id, form_data, onSubmit, onDelete, deleting, saving, onChange } = props;

  const default_values = useMemo(() => {
    // remove headings from the form data
    const filtered_data = form_data.filter((item) => item.type !== "heading");
    const validation = genericAssessmentFormSchema({ data: filtered_data });
    if (validation instanceof type.errors) {
      // console.log("Validation errors", validation.summary);
      return [];
    }
    return validation.data;
  }, [form_data]);

  // console.log("default form values", default_values);

  const form = useAppForm({
    defaultValues: {
      data: default_values,
    } as GenericAssessmentFormDto,
    validators: {
      onChange: genericAssessmentFormSchema,
      onSubmit: genericAssessmentFormSchema,
    },
    listeners: {
      onChange: ({ formApi: { state } }) => {
        const values = state.values;
        const changes = getArrayChanges(default_values, values.data, "field_name");
        // console.log("Form Changed", changes);
        onChange?.(changes);
      },
    },
    async onSubmit({ value }) {
      // console.log("Form value", value);

      // clean up the data before sending it to the server
      const cleaned_data = value.data.map((item) => pick(item, ["type", "label", "field_name", "value", "value_text"]));
      await onSubmit?.(cleaned_data as GenericAssessmentFormDto["data"]);
    },
  });

  const mergeAndCategorizeForms = (fieldValues: GenericAssessmentFormDto["data"]) => {
    // console.log("FieldValues", { fieldValues, form_data });

    const merged_forms = form_data.reduce((result, curr /*, currIdx */) => {
      if (!Array.isArray(fieldValues)) return result;

      const curr_field = (fieldValues || []).find((item) => {
        if (item.type === "heading" && !item?.field_name) return false;
        const curr_r = curr as Exclude<IGenericAppointmentForm, IGenericAppointmentFormHeading>;
        return item.field_name === curr_r.field_name && item.label === curr.label;
      });

      // console.log(`${curr.label}`, { curr_field, curr });

      if (curr_field) {
        result.push({ ...curr, value: curr_field?.value, index: fieldValues.indexOf(curr_field) } as any);
      } else {
        result.push({ ...curr, index: -1 });
      }

      return result;
    }, [] as GenericAppointmentFormTypeWithIndex[]);

    // console.log("Uncategorized merged forms", merged_forms);

    return Object.values(categorizeForms(merged_forms));
  };

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  // const values = useStore(form.store, (store) => store.values);
  // const errors = useStore(form.store, (store) => store.errors);
  // console.log("Form values", { values, errors });

  // const FormGroup = useFormGroup({ appt_id: appointment_id });

  return (
    <form.AppForm>
      <Stack gap="16px" as="form" onSubmit={handleSubmit}>
        <form.Field name="data" mode="array">
          {(field) => {
            // console.log("Field values before merge", field.state.value, Array.isArray(field.state.value));
            const merged_forms = mergeAndCategorizeForms(field.state.value);
            // console.log("Merged forms", { merged_forms, field_value: field.state.value });

            return merged_forms.map((item, index) => (
              <Stack key={index} gap="16px">
                {!item?.hidden && <FormHeading {...item} />}

                <Show when={Object.keys(item.groups).length > 0}>
                  <Stack gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
                    {Object.entries(item.groups).map(([group, forms], index) => (
                      <FormGroup
                        key={`${index}-${group}`}
                        group={group}
                        form={form as any}
                        appt_id={appointment_id}
                        forms={forms as GenericAppointmentFormTypeWithIndex[]}
                      />
                    ))}
                  </Stack>
                </Show>
              </Stack>
            ));

            // console.log("Field value", { merged_forms, values: field.state.value });

            // return <>something</>;
          }}
        </form.Field>

        <HStack py="16px" justifyContent="flex-end" alignItems="center">
          <ButtonGroup gap="24px">
            {onDelete && (
              <Button
                variant="subtle"
                loading={deleting}
                disabled={deleting || saving}
                onClick={async () => {
                  await onDelete?.();
                  form.reset();
                }}
              >
                Delete
              </Button>
            )}

            <SubmitButton disabled={deleting || saving} loading={saving}>
              Save & Update
            </SubmitButton>
          </ButtonGroup>
        </HStack>
      </Stack>
    </form.AppForm>
  );
}

export function FormHeading(props: IGenericAppointmentFormHeading & StackProps) {
  const { label, description, subDescription, bullets, table, fontWeight = "500", fontSize = "14px", ...xprops } = props;
  return (
    <Stack p="16px" bg="input" gap="4px" rounded="8px" {...xprops}>
      <Text fontSize={fontSize} fontWeight={fontWeight}>
        {label}
      </Text>
      {!!description && <Text color="text.2">{description}</Text>}
      {!!subDescription && <Text color="text.2">{subDescription}</Text>}

      {!!bullets && (
        <List.Root textStyle="sm" align="center" p="12px">
          {bullets.map((item, index) => {
            if (typeof item === "string") {
              return <List.Item key={index}>{item}</List.Item>;
            }

            if (item.title) {
              return (
                <List.Root textStyle="sm" align="center" p="12px" gap="20px">
                  <List.Item key={index}>
                    <Text color="text" fontWeight="500">
                      {item.title}
                    </Text>
                    <List.Root px="10px" py="4px">
                      {item.bullets.map((bullet, index) => (
                        <List.Item key={index} color="text.2">
                          {bullet}
                        </List.Item>
                      ))}
                    </List.Root>
                  </List.Item>
                </List.Root>
              );
            }
          })}
        </List.Root>
      )}

      {!!table && (
        <Table.Root mt="24px" size="sm" variant="outline">
          <Table.Header>
            <Table.Row>
              {table.th.map((item, index) => (
                <Table.ColumnHeader key={index}>{item}</Table.ColumnHeader>
              ))}
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {table.td.map((row, index) => (
              <Table.Row key={index}>
                {row.map((cell, index) => (
                  <Table.Cell key={index}>{cell}</Table.Cell>
                ))}
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      )}
    </Stack>
  );
}

const FormGroup = withForm({
  props: { group: "", appt_id: "", forms: [] as GenericAppointmentFormTypeWithIndex[] },
  render: (props) => {
    const { group, form, forms, appt_id } = props;

    /// the group format is `form_builder_grid_groups[key]` or `form_builder_grid_groups[key]-any_random_string`
    /// example two-a, one-b...
    const [template_column] = group.split("-");
    const form_grid = (form_builder_grid_groups as Record<string, string | { [key: string]: string }>)[template_column] || "1fr";

    const getFieldComponent = (form: any, fieldInfo: GenericAppointmentFormTypeWithIndex, index: number) => {
      // IGenericAppointmentForm["type"]
      //
      const map: Record<string, React.JSX.Element> = {
        text: (
          <FormBuilderTextField
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            fieldInfo={fieldInfo as IGenericAppointmentTextForm}
          />
        ),
        number: (
          <FormBuilderNumberField
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            fieldInfo={fieldInfo as IGenericAppointmentNumberForm}
          />
        ),
        "options-total": (
          <FormBuilderOptionsTotal
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            fieldInfo={fieldInfo as IGenericAppointmentFormOptionTotal}
          />
        ),
        "scoring-range": (
          <FormBuilderScoringRange
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            fieldInfo={fieldInfo as IGenericAppointmentFormScoringRange}
          />
        ),
        textarea: (
          <FormBuilderTextarea
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            fieldInfo={fieldInfo as IGenericAppointmentTextareaForm}
          />
        ),
        date: (
          <FormBuilderDateField
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            fieldInfo={fieldInfo as IGenericAppointmentDateForm}
          />
        ),
        "unresolved-select": (
          <UnresolvedSelectFieldWrapper
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            appt_id={appt_id}
            fieldInfo={fieldInfo as IGenericUnresolvedAppointmentSelectForm}
          />
        ),
        select: (
          <FormBuilderSelectField
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            appt_id={appt_id}
            fieldInfo={fieldInfo as IGenericAppointmentSelectForm}
            fetching_options={false}
          />
        ),

        "calculation-result": (
          <FormBuilderCalculationResult
            key={`${index}-${fieldInfo.type}`}
            form={form as any}
            index={fieldInfo.index}
            appt_id={appt_id}
            fieldInfo={fieldInfo as IGenericAppointmentFormCalculationResult}
          />
        ),

        line: <Separator />,
      };

      return map[fieldInfo.type] || <Text>Unmapped Field type: {fieldInfo.type}</Text>;
    };

    return (
      <Grid templateColumns={form_grid} gap="16px" alignItems="flex-end">
        {forms.map((form_data, index) => getFieldComponent(form, form_data, index))}
      </Grid>
    );
  },
});

const FormBuilderTextField = withForm({
  props: { index: -1, fieldInfo: {} as IGenericAppointmentTextForm },
  render(props) {
    const { form, index, fieldInfo } = props;
    return (
      <form.AppField name={`data[${index}].value`}>
        {(field) => (
          <field.TextField
            field={field}
            label={fieldInfo.label}
            // name={`data[${index}]`}
            // defaultValue={fieldInfo?.default_value || ""}
            value={(field.state.value as string) || ""}
            placeholder={fieldInfo?.placeholder || ""}
            onChange={(e) => field.setValue(e.target.value)}
          />
        )}
      </form.AppField>
    );
  },
});

const FormBuilderNumberField = withForm({
  props: { index: -1, fieldInfo: {} as IGenericAppointmentNumberForm },
  render(props) {
    const { form, index, fieldInfo } = props;
    return (
      <form.AppField name={`data[${index}].value`}>
        {(field) => (
          <field.TextField
            type="number"
            field={field}
            label={fieldInfo.label}
            // name={`data[${index}]`}
            // defaultValue={fieldInfo?.default_value || ""}
            value={(field.state.value as string) || ""}
            placeholder={fieldInfo?.placeholder || ""}
            onChange={(e) => field.setValue(e.target.value)}
            min={fieldInfo?.min}
            max={fieldInfo?.max}
            step={fieldInfo?.step}
          />
        )}
      </form.AppField>
    );
  },
});

const FormBuilderOptionsTotal = withForm({
  props: { index: -1, fieldInfo: {} as IGenericAppointmentFormOptionTotal },
  render(props) {
    const { form, index, fieldInfo } = props;
    return (
      <form.Subscribe selector={(state) => (state.values as { data: IGenericAppointmentForm[] }).data}>
        {(values) => {
          const parse_value = (curr: IGenericAppointmentForm) => {
            if (curr.type === "select" && !!curr?.options_is_summable && curr.value) {
              const value = Array.isArray(curr?.value) ? curr?.value?.[0] : curr?.value;
              const parsed = parseInt(value || "0");
              return isNaN(parsed) ? 0 : parsed;
            }
            return 0;
          };

          const total = (values || []).reduce((acc, curr) => {
            if (fieldInfo?.appears_under && fieldInfo?.use_heading_group) {
              if ((curr as any)?.appears_under !== fieldInfo?.appears_under) {
                return acc;
              }

              acc += parse_value(curr);
              return acc;
            }

            acc += parse_value(curr);
            return acc;
          }, 0);

          // form.setFieldValue(`data[${index}].value`, total.toString());

          return (
            <form.AppField name={`data[${index}].value`}>
              {(field) => (
                <field.TextField
                  field={field}
                  label={fieldInfo.label}
                  // name={`data[${index}]`}
                  // defaultValue={fieldInfo?.default_value || ""}
                  value={total.toString()}
                  // placeholder={fieldInfo?.placeholder || ""}
                  onChange={(e) => field.setValue(e.target.value)}
                  disabled={fieldInfo?.disabled}
                />
              )}
            </form.AppField>
          );
        }}
      </form.Subscribe>
    );
  },
});

const FormBuilderScoringRange = withForm({
  props: { index: -1, fieldInfo: {} as IGenericAppointmentFormScoringRange },
  render(props) {
    const { form, index, fieldInfo } = props;
    return (
      <form.Subscribe selector={(state) => (state.values as { data: IGenericAppointmentForm[] }).data}>
        {(values) => {
          const parse_value = (curr: IGenericAppointmentForm) => {
            if (curr.type === "select" && !!curr?.options_is_summable && curr.value) {
              const value = Array.isArray(curr?.value) ? curr?.value?.[0] : curr?.value;
              const parsed = parseInt(value || "0");
              return isNaN(parsed) ? 0 : parsed;
            }
            return 0;
          };

          const total = (values || []).reduce((acc, curr) => {
            if (fieldInfo?.appears_under && fieldInfo?.use_heading_group) {
              if ((curr as any)?.appears_under !== fieldInfo?.appears_under) {
                return acc;
              }

              acc += parse_value(curr);
              return acc;
            }

            acc += parse_value(curr);
            return acc;
          }, 0);

          const range = fieldInfo.range_options.find((item) => total >= item.min && total <= item.max);

          return (
            <form.AppField name={`data[${index}].value`}>
              {(field) => (
                <field.Select
                  field={field}
                  label={fieldInfo.label}
                  // name={`data[${index}]`}
                  // defaultValue={fieldInfo?.default_value || ""}
                  value={[range?.value || ""]}
                  // placeholder={fieldInfo?.placeholder || ""}
                  disabled={fieldInfo?.disabled}
                  items={fieldInfo.range_options.map((item) => ({ label: item.value, value: item.value }))}
                />
              )}
            </form.AppField>
          );
        }}
      </form.Subscribe>
    );
  },
});

const FormBuilderCalculationResult = withForm({
  props: { index: -1, appt_id: "", fieldInfo: {} as IGenericAppointmentFormCalculationResult },
  render(props) {
    const { form, index, fieldInfo } = props;
    return (
      <form.Subscribe selector={(state) => (state.values as { data: IGenericAppointmentForm[] }).data}>
        {(values) => {
          const get_field_values = () => {
            type ResultTypes = IGenericAppointmentSelectForm | IGenericUnresolvedAppointmentSelectForm;

            const get_fields = (vals: typeof values) =>
              (vals ?? []).filter(
                (item) => ["select", "unresolved-select"].includes(item.type) && item.type !== "heading"
              ) as ResultTypes[];

            if (fieldInfo?.result_of) {
              const fields = get_fields(values).filter((item) => fieldInfo?.result_of?.includes(item?.field_name));
              return fields.map((item) => item?.value);
            }

            if (fieldInfo?.appears_under) {
              const fields = get_fields(values).filter((item) => item?.appears_under === fieldInfo?.appears_under);
              return fields.map((item) => item?.value);
            }

            return get_fields(values).map((item) => item?.value);
          };

          const treated_values = get_field_values().map((v) => {
            const value = Array.isArray(v) ? v?.[0] : v;
            const parsed = parseInt(value ?? "0");
            return isNaN(parsed) ? 0 : parsed;
          });

          const result = fieldInfo.calculate_result(treated_values);

          console.log("Calculation Result", { result, treated_values, get_field_values: get_field_values() });

          return (
            <form.AppField name={`data[${index}].value`}>
              {() => (
                <Stack p="16px" bg="primary.50" gap="4px" rounded="8px" border="1px solid" borderColor="primary">
                  <Text fontSize="12px" fontWeight="600" textTransform="uppercase" color="text.3">
                    {fieldInfo.label}
                  </Text>
                  <Text fontSize="md" fontWeight="600">
                    {result}
                  </Text>
                </Stack>
              )}
            </form.AppField>
          );
        }}
      </form.Subscribe>
    );
  },
});

const FormBuilderTextarea = withForm({
  props: { index: -1, fieldInfo: {} as IGenericAppointmentTextareaForm },
  render(props) {
    const { form, index, fieldInfo } = props;
    return (
      <form.AppField name={`data[${index}].value`}>
        {(field) => (
          <Textarea
            field={field}
            label={fieldInfo.label}
            // name={`data[${index}]`}
            // defaultValue={fieldInfo?.default_value || ""}
            value={(field.state.value as string) || ""}
            placeholder={fieldInfo?.placeholder || ""}
            onChange={(e) => field.setValue(e.target.value)}
            minH="100px"
            hideCharCount={fieldInfo?.hideCharCount}
          />
        )}
      </form.AppField>
    );
  },
});

const FormBuilderDateField = withForm({
  props: { index: -1, fieldInfo: {} as IGenericAppointmentDateForm },
  render(props) {
    const { form, index, fieldInfo } = props;
    return (
      <form.AppField name={`data[${index}].value`}>
        {(field) => (
          <field.TextField
            type="date"
            field={field}
            label={fieldInfo.label}
            // name={`data[${index}]`}
            // defaultValue={fieldInfo?.default_value || ""}
            value={(field.state.value as string) || ""}
            placeholder={fieldInfo?.placeholder || ""}
            onChange={(e) => field.setValue(e.target.value)}
          />
        )}
      </form.AppField>
    );
  },
});

function UnresolvedSelectFieldWrapper(props: {
  appt_id: string;
  form: any;
  fieldInfo: IGenericUnresolvedAppointmentSelectForm;
  index: number;
}) {
  const { appt_id, form, fieldInfo, index } = props;

  const options = fieldInfo.options;
  const query_options = options(appt_id);
  const { data, isPending: fetching_options } = useQuery({ ...query_options, enabled: Object.keys(query_options).length > 0 });
  const resolved_options = fieldInfo.transform(data);

  const field_info: IGenericAppointmentSelectForm = { ...fieldInfo, type: "select", options: resolved_options };
  return <FormBuilderSelectField form={form} index={index} appt_id={appt_id} fieldInfo={field_info} fetching_options={fetching_options} />;
}

const FormBuilderSelectField = withForm({
  props: { index: -1, appt_id: "", fieldInfo: {} as IGenericAppointmentSelectForm, fetching_options: false },
  render(props) {
    const { form, index, fieldInfo, fetching_options } = props;

    const options = fieldInfo.options as { label: string; value: string; is_shaded?: boolean }[];

    return (
      <form.AppField name={`data[${index}].value`}>
        {(field) => {
          const value = fieldInfo?.multiple ? (field.state.value as string[]) || [""] : [(field.state.value as string) || ""];
          // const defaultValue = fieldInfo?.multiple ? fieldInfo?.default_value || [""] : fieldInfo?.default_value?.[0] || "";

          return (
            <field.Select
              label={fieldInfo.label}
              // defaultValue={defaultValue as string[]}
              value={value}
              field={field}
              // name={`data[${index}]`}
              placeholder={fieldInfo?.placeholder || ""}
              items={options}
              multiple={fieldInfo?.multiple}
              loading={fetching_options}
              canFilterList={fieldInfo?.can_filter_list}
              onValueChange={(e) => {
                if (fieldInfo?.multiple) {
                  const value_txs = e.items?.map((t) => t.label);
                  field.setValue(e.value);
                  form.setFieldValue(`data[${index}].value_text`, value_txs);
                } else {
                  const value_tx = e.items?.[0]?.label;
                  field.setValue(e.value[0] || "");
                  form.setFieldValue(`data[${index}].value_text`, value_tx);
                }
              }}
            />
          );
        }}
      </form.AppField>
    );
  },
});

function categorizeForms(forms: IGenericAppointmentForm[]) {
  const categorizedByHeading = categorizeFormsByHeading(forms);
  const categorizedByGroup = categorizeFormsByGroup(categorizedByHeading);

  return categorizedByGroup;
}

type HeadingFormType = IGenericAppointmentFormHeading & { forms: IGenericAppointmentForm[] };
function categorizeFormsByHeading(forms: IGenericAppointmentForm[]) {
  const categorized: Record<string, HeadingFormType> = {};

  forms.forEach((form) => {
    if (form.type === "heading") {
      categorized[form.id] = { ...form, forms: [] };
    } else {
      const headingId = form?.appears_under || "default";
      if (!categorized[headingId]) {
        categorized[headingId] = { id: "default", type: "heading", label: "Default", hidden: true, forms: [] };
      }
      categorized[headingId].forms.push(form);
    }
  });

  return categorized;
}

function categorizeFormsByGroup(forms: Record<string, HeadingFormType>) {
  type GroupedFormType = IGenericAppointmentFormHeading & { groups: Record<string, IGenericAppointmentForm[]> };
  const categorized: Record<string, GroupedFormType> = {};

  Object.entries(forms).forEach(([headingId, heading]) => {
    const group_map: Map<string, IGenericAppointmentForm[]> = new Map();
    heading.forms.forEach((form) => {
      if (form.type === "heading") return; // Skip headings in the forms array
      const group = form?.group || "default";
      if (!group_map.has(group)) group_map.set(group, []);
      group_map.get(group)?.push(form);
    });

    categorized[headingId] = {
      ...omit(heading, ["forms"]),
      id: headingId,
      type: "heading",
      groups: Object.fromEntries(group_map.entries()),
    };
  });

  return categorized;
}
