import { AppointmentListRo, IListFilter } from "@/interfaces";
import { getStatusColor } from "@/utils";
import { Clipboard, HStack, Show, Skeleton, SkeletonText, Stack, StackProps, Table, Text, VStack } from "@chakra-ui/react";
import { useNavigate } from "react-router";
import { useMemo } from "react";
import { AppointmentTableDataType, Empty, Icon, Paginator, ProviderStatus, toAppointmentTableData } from "@/components";

interface AppointmentListProps {
  list?: AppointmentListRo;
  filter?: IListFilter;

  onPageChange?: (page: number) => void;
  loading?: boolean;

  emptyListStackProps?: StackProps;
}

export function AppointmentList(props: AppointmentListProps) {
  const { list, filter, loading, onPageChange, emptyListStackProps } = props;

  const navigate = useNavigate();

  const total = useMemo(() => list?.total || 1, [list]);
  const items = useMemo(() => toAppointmentTableData(list?.data ?? raw_items), [list]);
  const isEmpty = useMemo(() => !list || !list.data || list.data.length === 0, [list]);

  return (
    <Stack gap="20px" minH="82svh" justifyContent="space-between">
      <Table.ScrollArea hideBelow="3sm">
        <Table.Root interactive>
          <Table.Header>
            <Table.Row
              bg="input"
              rounded="4px"
              color="text.2"
              css={{
                "& > th": {
                  borderBottom: "none",
                  _first: {
                    borderTopLeftRadius: "4px",
                    borderBottomLeftRadius: "4px",
                  },
                  _last: {
                    borderTopRightRadius: "4px",
                    borderBottomRightRadius: "4px",
                  },
                },
              }}
            >
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Date & Time
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Client name
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Service type
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Booking Type
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Booking Ref
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Mode
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Referred?
                </SkeletonText>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                  Status
                </SkeletonText>
              </Table.ColumnHeader>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {items.map((item) => (
              <Table.Row
                key={item.id}
                // border="none"
                py="100px"
                viewTransitionName={`appointment-${item?.id}`}
                border="1px solid transparent"
                borderBottomColor="stroke.divider"
                _last={{ border: "none" }}
                css={{
                  "& > td": {
                    color: "text.2",
                    borderBottom: "none",
                    py: "14px",
                    _first: {
                      borderTopLeftRadius: "4px",
                      borderBottomLeftRadius: "4px",
                    },
                    _last: {
                      borderTopRightRadius: "4px",
                      borderBottomRightRadius: "4px",
                    },
                  },

                  _hover: {
                    bg: "primary.50",
                    cursor: "pointer",
                  },
                }}
                onClick={() => navigate(`/appointments/${item.id}`, { state: item, viewTransition: true })}
              >
                <Table.Cell>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.datetime}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.member}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell textTransform="capitalize">
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.service}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell textTransform="capitalize">
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.booking_type}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell textTransform="capitalize">
                  <SkeletonText variant="shine" /*h={loading ? ''}*/ noOfLines={1} loading={loading}>
                    <Clipboard.Root value={item?.booking_ref}>
                      <Clipboard.Trigger asChild>
                        <HStack onClick={(e) => e.stopPropagation()}>
                          <Clipboard.ValueText />
                          <Clipboard.Indicator />
                        </HStack>
                      </Clipboard.Trigger>
                    </Clipboard.Root>
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell textTransform="capitalize">
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.comm_mode}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell textTransform="capitalize">
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.referred}
                  </SkeletonText>
                </Table.Cell>
                <Table.Cell color={`${getStatusColor(item?.status as string)} !important`}>
                  <SkeletonText variant="shine" noOfLines={1} loading={loading}>
                    {item.status}
                  </SkeletonText>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </Table.ScrollArea>

      <Stack gap="8px" hideFrom="3sm">
        {items.map((item) => (
          <AppointmentMobileListItem key={item.id} appointment={item} loading={loading} />
        ))}
      </Stack>

      <Show when={isEmpty && !loading}>
        <VStack w="100%" my="10vh" {...emptyListStackProps}>
          <Empty subtitle="Appointments will appear here" />
        </VStack>
      </Show>

      <Show when={filter && !loading}>
        <Paginator
          count={total}
          defaultPage={1}
          page={filter?.page ?? 1}
          pageSize={filter?.item_per_page ?? 1}
          onPageChange={(e) => {
            onPageChange?.(e.page);
          }}
        />
      </Show>
    </Stack>
  );
}

export interface AppointmentMobileListItemProps extends Omit<StackProps, "id"> {
  appointment: AppointmentTableDataType;
  loading?: boolean;
}

export function AppointmentMobileListItem(props: AppointmentMobileListItemProps) {
  const { appointment: appt, loading = true, ...xprops } = props;
  const navigate = useNavigate();

  const { id, status } = appt;

  // console.log("appointment item ID", id);

  return (
    <Stack
      py="16px"
      px="12px"
      // bg="input"
      rounded="8px"
      gap="8px"
      role="button"
      cursor="pointer"
      focusRingColor="primary"
      border="1px solid"
      borderColor="stroke.divider"
      onClick={() =>
        navigate(`/appointments/${id}`, {
          viewTransition: true,
          state: appt,
        })
      }
      {...xprops}
    >
      <HStack justifyContent="space-between">
        <SkeletonText variant="shine" noOfLines={1} loading={loading}>
          <Text fontSize="12px" fontWeight="600" color="text.2" textTransform="uppercase">
            {appt?.datetime}
          </Text>
        </SkeletonText>
        <ProviderStatus fontSize="12px" fontWeight="500" variant="badge" status={status as string} loading={loading} />
      </HStack>

      {/* <SkeletonText variant="shine" h="4px" noOfLines={1} loading={loading}>
        <Separator borderColor="stroke.divider" />
      </SkeletonText> */}

      <HStack justifyContent="space-between" mt="10px">
        <Skeleton variant="shine" loading={loading}>
          <HStack gap="4px" justifyContent="center">
            <Icon name="user" boxSize="14px" color="text.3" />
            <Text fontSize="14px" fontWeight="400" color="text">
              {appt?.member}
            </Text>
          </HStack>
        </Skeleton>

        {/* <Skeleton variant="shine" loading={loading}>
          <Text fontSize="12px" fontWeight="500" color="text.2">

            {appt?.member}
          </Text>
        </Skeleton> */}
      </HStack>

      <HStack>
        <Skeleton variant="shine" loading={loading}>
          <HStack gap="4px" justifyContent="center">
            <Icon name="stethoscope" boxSize="14px" color="text.3" />
            <Text fontSize="14px" fontWeight="400" color="text.2" textTransform="capitalize">
              {appt?.service}
            </Text>
          </HStack>
        </Skeleton>
      </HStack>

      <HStack>
        <Skeleton variant="shine" loading={loading}>
          <HStack gap="4px" justifyContent="center">
            <Icon name="audio_lines" boxSize="14px" color="text.3" />
            <Text fontSize="14px" fontWeight="400" color="text.2" textTransform="capitalize">
              {appt?.comm_mode}
            </Text>
          </HStack>
        </Skeleton>
      </HStack>

      {/* <SkeletonText variant="shine" noOfLines={1} loading={loading}>
        <Text fontSize="12px" fontWeight="400" color="text.3">
          Booked by: {service}
        </Text>
      </SkeletonText> */}
    </Stack>
  );
}

const raw_items = [
  {
    id: 1,
    datetime: "Today • 12:34pm",
    consultant: "Adegboyoga Precious",
    member: "Nneka Chukwu",
    bookedby: "Victor Adams",
    status: "Upcoming",
  },
  {
    id: 2,
    datetime: "Today • 12:34pm",
    consultant: "Jide Kosoko",
    member: "Damilare Usman",
    bookedby: "Victor Adams",
    status: "Pending",
  },
  {
    id: 3,
    datetime: "Today • 12:34pm",
    consultant: "Nneka Chukwu",
    member: "Desmond Tutu",
    bookedby: "Victor Adams",
    status: "Completed",
  },
  {
    id: 4,
    datetime: "Today • 12:34pm",
    consultant: "Adebayo Salami",
    member: "Adebanji Bolaji",
    bookedby: "Victor Adams",
    status: "Canceled",
  },
  {
    id: 5,
    datetime: "Today • 12:34pm",
    consultant: "Desmond Tutu",
    member: "Adegboyoga Precious",
    bookedby: "Victor Adams",
    status: "Completed",
  },
  {
    id: 6,
    datetime: "Today • 12:34pm",
    consultant: "Adebanji Bolaji",
    member: "Jibike Alarape",
    bookedby: "Victor Adams",
    status: "Completed",
  },
  {
    id: 7,
    datetime: "Today • 12:34pm",
    consultant: "Baba Kaothat",
    member: "Adebayo Salami",
    bookedby: "Victor Adams",
    status: "Completed",
  },
  {
    id: 8,
    datetime: "Today • 12:34pm",
    consultant: "Jibike Alarape",
    member: "Eze Chinedu",
    bookedby: "Victor Adams",
    status: "Completed",
  },
  {
    id: 9,
    datetime: "Today • 12:34pm",
    consultant: "Eze Chinedu",
    member: "Baba Kaothat",
    bookedby: "Victor Adams",
    status: "Completed",
  },
];
