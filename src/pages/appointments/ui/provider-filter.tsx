import { <PERSON><PERSON>ard, FilterMenu, Select } from "@/components";
import { useCommonList, usePartialState } from "@/hooks";
import { IListFilter, AppointmentListDataRo } from "@/interfaces";
import { Grid } from "@chakra-ui/react";
import capitalize from "lodash.capitalize";
import { useMemo, useRef } from "react";

type Filter = IListFilter & Partial<Pick<AppointmentListDataRo, "status">>;

interface TableFilterProps {
  filters?: Filter;
  onFilterChange?: (filters: Filter) => void;

  loading?: boolean;
}

export function ProviderFilter(props: TableFilterProps) {
  const { filters: raw_filters, loading, onFilterChange } = props;

  const contentRef = useRef<HTMLDivElement | null>(null);

  const { data: rd } = useCommonList("religion-list");
  const { data: pl } = useCommonList("preferred-lan");
  const { data: bp } = useCommonList("booking-prices");

  //   console.log("Booking prices", bp);

  const [filters, setFilters] = usePartialState<Filter>(raw_filters || {}, [raw_filters]);

  const religion_list = useMemo(() => {
    const list = rd?.data || [];
    return list.map((item) => ({
      label: capitalize(item),
      value: item,
    }));
  }, [rd]);

  const language_list = useMemo(() => {
    const list = pl?.data || [];
    return list.map((item) => ({
      label: capitalize(item),
      value: item,
    }));
  }, [pl]);

  const price_range_list = useMemo(() => {
    const list = bp?.data || [];
    return list.map((item) => ({
      label: item.name,
      value: item.value,
    }));
  }, [bp]);

  const handleApplyFilter = () => {
    onFilterChange?.({ ...filters, page: 1 });
  };

  //   const handleRange = (range: Partial<{ from: string; to: string }>) => {
  //     const { from = "", to = "" } = range;
  //     setRange(range);
  //     set((state) => ({ ...state, amount: `${from}-${to}` }));
  //   };

  return (
    <FilterMenu loading={loading} onApply={handleApplyFilter} ref={contentRef}>
      <FieldCard heading="Filter" p="16px">
        <Grid templateColumns={{ sm: "1fr", md: "repeat(1, 1fr)" }} gap="32px">
          {/* <FilterRadioGroup
            heading="Gender"
            value={filters?.range?.value}
            onValueChange={(e) => setFilters({ gender: e.value as string })}
            items={[
              { label: "Male", value: "male" },
              { label: "Female", value: "female" },
            ]}
          /> */}

          <Select
            label="Gender"
            placeholder="Select"
            value={[filters?.gender || ""]}
            portalContainerRef={contentRef}
            onValueChange={(e) => setFilters({ gender: e.value[0] })}
            items={[
              { label: "Male", value: "male" },
              { label: "Female", value: "female" },
            ]}
          />

          {/* <FilterRadioGroup
            heading="Religion"
            value={filters?.range?.value}
            onValueChange={(e) => setFilters({ religion: e.value as string })}
            items={religion_list}
          />

          <FilterRadioGroup
            heading="Language"
            value={status ? status.toString() : undefined}
            onValueChange={(e) => setFilters({ language: e.value as string })}
            items={language_list}
          /> */}

          <Select
            label="Religion"
            placeholder="Select"
            value={[filters?.religion || ""]}
            portalContainerRef={contentRef}
            onValueChange={(e) => setFilters({ religion: e.value[0] })}
            items={religion_list}
          />

          <Select
            label="Language"
            placeholder="Select"
            value={[filters?.language || ""]}
            portalContainerRef={contentRef}
            onValueChange={(e) => setFilters({ language: e.value[0] })}
            items={language_list}
          />

          <Select
            label="Price Range"
            placeholder="Select"
            value={[filters?.amount || ""]}
            portalContainerRef={contentRef}
            onValueChange={(e) => setFilters({ amount: e.value[0] })}
            items={price_range_list}
          />
        </Grid>

        {/* <FilterRadioGroup heading="Amount" value={"range"} items={[{ label: "Range", value: "range", show_content: true }]}>
          <Grid templateColumns={{ sm: "1fr", md: "1fr .2fr 1fr" }} gap="16px">
            <TextField
              label="From"
              placeholder="1000"
              type={"number"}
              value={from}
              onChange={(e) => handleRange({ from: e.target.value })}
              min={1}
            />
            <Flex justifyContent="center" alignItems="center">
              -
            </Flex>
            <TextField
              label="To"
              placeholder="5000"
              type={"number"}
              value={to}
              onChange={(e) => handleRange({ to: e.target.value })}
              min={1}
            />
          </Grid>
        </FilterRadioGroup> */}
      </FieldCard>
    </FilterMenu>
  );
}
