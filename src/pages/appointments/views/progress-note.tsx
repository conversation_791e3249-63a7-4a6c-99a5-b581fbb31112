import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NavigationBlockerProvider, toaster } from "@/components";
import { Container, Grid, Heading, Show, Skeleton, Stack } from "@chakra-ui/react";
import { useParams } from "react-router";
import { FormBuilder } from "../ui/form-builder";
import { appt_progress_note_forms } from "@/static";
import { useMutation, useNavigationBlocker } from "@/hooks";
import { createProgressNoteMutationOpts, deleteProgressNoteMutationOpts, getAppointmentProgressNoteQueryOpts } from "@/queries";
import { toQueryString, tryCatch } from "@/utils";
import { useQuery } from "@tanstack/react-query";
import groupBy from "lodash.groupby";
import { useCallback, useRef } from "react";
import { IGenericAppointmentForm, IGenericAppointmentFormHeading } from "@/interfaces";

export function AppointmentProgressNote() {
  return (
    <NavigationBlockerProvider>
      <AppointmentProgressNoteWrapper />
    </NavigationBlockerProvider>
  );
}
export function AppointmentProgressNoteWrapper() {
  const params = useParams();

  const appt_id = params?.id;

  const is_dirty = useRef(false);
  const nav_blocker = useNavigationBlocker();

  //   const [searchValue, setSearchValue] = useState("");

  //   const { data, isPending: fetching_dataset } = useQuery(appointmentDiagnosisDatasetQueryOpts(appt_id || ""));
  const { data, isPending: fetching_note } = useQuery(
    getAppointmentProgressNoteQueryOpts(toQueryString({ appointment_id: appt_id || "" }))
  );
  const note_data = (data?.data || [])?.[0];
  const note_form_data = note_data?.data;

  const { mutateAsync, isPending: saving } = useMutation(createProgressNoteMutationOpts(appt_id || ""));
  const { mutateAsync: deleteProgressNote, isPending: deleting } = useMutation(
    deleteProgressNoteMutationOpts(appt_id || "", note_data?.data_id || "")
  );

  /// Group the note form data by field name to make it more easier to get the field value
  /// Note that the field name must be unique
  const group_by_field_names = groupBy(note_form_data, "field_name");

  const form_data = appt_progress_note_forms[0].forms as IGenericAppointmentForm[];

  const getProgressNoteFormDataSavedWithValue = useCallback(() => {
    const forms_with_value = form_data.map((item) => {
      if (item.type === "heading") return item;
      const item_r = item as Exclude<IGenericAppointmentForm, IGenericAppointmentFormHeading>;
      const saved = group_by_field_names?.[item.field_name]?.[0];
      return {
        ...item,
        value: saved?.value || item_r?.value,
      };
    });

    return forms_with_value as IGenericAppointmentForm[];
  }, [group_by_field_names, form_data]);

  console.log("Note data", getProgressNoteFormDataSavedWithValue());

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Appointments", to: "/appointments", is_first: true },
            { title: "Appointment details", to: `/appointments/${appt_id}` },
            { title: "Progress Note", to: "#" },
          ]}
        />

        <Heading as="h5" fontSize="20px" fontWeight="600">
          Progress Note
        </Heading>

        <Show when={fetching_note}>
          <Stack gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
            <Grid templateColumns="repeat(2, 1fr)" gap="16px">
              <Skeleton variant="shine" h="48px" loading></Skeleton>
              <Skeleton variant="shine" h="48px" loading></Skeleton>
              <Skeleton variant="shine" h="48px" loading></Skeleton>
              <Skeleton variant="shine" h="48px" loading></Skeleton>
              <Skeleton variant="shine" h="100px" loading></Skeleton>
              <Skeleton variant="shine" h="100px" loading></Skeleton>
            </Grid>
          </Stack>
        </Show>

        <Show when={!fetching_note}>
          <Stack gap="16px">
            <FormBuilder
              form_name="progress-note"
              form_data={getProgressNoteFormDataSavedWithValue() || []}
              form_id={appt_id || ""}
              appointment_id={appt_id || ""}
              onChange={(changes) => {
                if (changes.added.length > 0 || changes.removed.length > 0 || changes.modified.length > 0) {
                  // console.log("Form Changed", changes);
                  is_dirty.current = true;
                  nav_blocker.set({ block_navigations: true });
                  return;
                }

                is_dirty.current = false;
                nav_blocker.set({ block_navigations: false });
              }}
              onSubmit={async (values) => {
                // console.log("Form submitted", values);

                const promise = mutateAsync({
                  appointment_id: appt_id || "",
                  data: values,
                  data_id: note_data?.data_id,
                });

                const result = await tryCatch(promise);
                if (result.ok) {
                  toaster.success({
                    title: "Success",
                    description: "Progress notes has been submitted successfully",
                  });
                }
              }}
              onDelete={async () => {
                if (note_data?.data_id) {
                  const promise = deleteProgressNote({});
                  const result = await tryCatch(promise);
                  if (result.ok) {
                    toaster.success({
                      title: "Success",
                      description: "Progress notes has been deleted successfully",
                    });
                  }
                }
              }}
              deleting={deleting}
              saving={saving}
            />
          </Stack>
        </Show>
      </Stack>
    </Container>
  );
}
