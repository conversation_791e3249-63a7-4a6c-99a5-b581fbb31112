/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  CalendarIllustration,
  ConfirmationModal,
  Icon,
  IconNames,
  MedicalRecordillustration,
  ProviderStatus,
  success_dialog,
  toaster,
} from "@/components";
import configs from "@/config";
import { useMutation } from "@/hooks";
import { AppointmentListDataRo } from "@/interfaces";
import {
  appointmentByIdQueryOpts,
  appointmentDiagnosisDatasetQueryOpts,
  cancelMutationOpts,
  getAppointmentAssessmentQueryOpts,
  getAppointmentMedicalPrescriptionQueryOpts,
  getAppointmentProgressNoteQueryOpts,
  getAppointmentToolsQueryOpts,
  getAppointmentTreatmentPlanQueryOpts,
  markApptSessionAsCompleteMutationOpts,
  startApptSessionMutationOpts,
} from "@/queries";
import { joinNames, mapField, toQueryString, tryCatch } from "@/utils";
import {
  Avatar,
  Box,
  BoxProps,
  Container,
  Grid,
  HStack,
  Link,
  LinkProps,
  Separator,
  Show,
  Skeleton,
  SkeletonCircle,
  Stack,
  StackProps,
  Text,
  TextProps,
} from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { add, format, isValid, parseISO } from "date-fns";
import { memo, ReactNode, useMemo } from "react";
import { Link as RouterLink, LinkProps as RouterLinkProps, useParams } from "react-router";

let rerender_count = 0;

export function AppointmentDetails() {
  const params = useParams();
  const appt_id = params?.id || "";

  const { data, isPending: loading } = useQuery(appointmentByIdQueryOpts(params.id));

  const { data: diag_data } = useQuery(appointmentDiagnosisDatasetQueryOpts(appt_id, "count"));
  const { data: tool_data } = useQuery(getAppointmentToolsQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" })));
  const { data: asse_data } = useQuery(getAppointmentAssessmentQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" })));
  const { data: prog_data } = useQuery(getAppointmentProgressNoteQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" })));
  const { data: medi_data } = useQuery(
    getAppointmentMedicalPrescriptionQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" }))
  );
  const { data: trtm_data } = useQuery(
    getAppointmentTreatmentPlanQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" }))
  );
  const appt = data?.data;

  const diag_count = useMemo(() => (diag_data?.data as unknown as { total: number })?.total || 0, [diag_data]);
  const asse_count = useMemo(() => (asse_data?.data as unknown as { total: number })?.total || 0, [asse_data]);
  const trtm_count = useMemo(() => (trtm_data?.data as unknown as { total: number })?.total || 0, [trtm_data]);
  const tool_count = useMemo(() => (tool_data?.data as unknown as { total: number })?.total || 0, [tool_data]);
  const prog_count = useMemo(() => (prog_data?.data as unknown as { total: number })?.total || 0, [prog_data]);
  const medi_count = useMemo(() => (medi_data?.data as unknown as { total: number })?.total || 0, [medi_data]);

  const status = useMemo(() => (mapField(appt?.status ?? 0, "appointment") as string).toLowerCase(), [appt]);

  // console.log("Appointment details", { prog_data, medi_data });

  rerender_count += 1;
  console.log("Re-render count", rerender_count);

  // console.log("Appointment details", configs.APPT_SESSION_URL);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Appointments", to: "/appointments", is_first: true },
            { title: "Appointment details", to: "#" },
          ]}
        />

        <Grid templateColumns={{ base: "1fr", "3sm": "1fr", "4sm": "repeat(2, 1fr)" } as any} gap="16px">
          <UserInfoCard appt={appt} loading={loading} />
          <AppointmentInfoCard appt={appt} loading={loading} />
        </Grid>

        <Show when={["live"].includes(status)}>
          <LinkCard
            loading={loading}
            title="Refer this user"
            // label="4 form used"
            description="When the current service does not fully address the individual’s needs, ensure they receive the right support by referring to a provider with the appropriate skills or specialty."
            to={`/appointments/${appt_id}/refer`}
            borderColor="primary"
            bg="primary.50"
          />
        </Show>

        <Show when={["canceled"].includes(status)}>
          <Alert
            status="warning"
            border="1px solid"
            title={"Session Canceled"}
            titleProps={{
              fontSize: "18px",
              fontWeight: 600,
            }}
          >
            <Text fontSize="14px" color="text.2">
              The session has been canceled, no action can be taken at this time
            </Text>
          </Alert>
        </Show>

        <Show when={["completed"].includes(status)}>
          <Alert
            status="success"
            border="1px solid"
            title={"Session Completed"}
            titleProps={{
              fontSize: "18px",
              fontWeight: 600,
            }}
          >
            <Text fontSize="14px" color="text.2">
              This session has been marked as completed. As a result, access to the assessment details is no longer available. If you
              require any further information, please contact the admin team for assistance.
            </Text>
          </Alert>
        </Show>

        {/* Session Completed
         */}

        <Show when={["live", "upcoming", "ended"].includes(status)}>
          <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
            <LinkCard
              loading={loading}
              to={`/appointments/${appt_id}/questionnaires`}
              // label="4 form used"
              title="Booking Questionnaire"
              description="See the response to  the questionnaire filled by the client at the point of booking"
            />

            <LinkCard
              loading={loading}
              title="Medical Records"
              description=" See the overall  documentation of the client"
              leftIcon={<Box as={MedicalRecordillustration} w="28px" h="45px" />}
              to={`/appointments/${appt_id}/medical-records`}
            >
              {/* <Button
              variant="outline"
              borderColor="primary"
              bg="primary.50"
              w="fit-content"
              leftIcon={<Icon name="file_text" color="inherit" />}
            >
              Request Records
            </Button> */}
            </LinkCard>
          </Grid>
        </Show>

        <Show when={["live", "ended"].includes(status)}>
          <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
            <LinkCard
              loading={loading}
              to={`/appointments/${appt_id}/assessment`}
              label={`${asse_count} form used`}
              title="Assessment"
              description="A comprehensive evaluation that gathers relevant background, strengths, and areas of concern to inform care."
            />

            <LinkCard
              loading={loading}
              title="Treatment Plan"
              label={`${trtm_count} form used`}
              description="A structured outline of goals, interventions, and strategies designed to guide ongoing support."
              to={`/appointments/${appt_id}/treatment-plan`}
            />
          </Grid>

          <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
            <LinkCard
              loading={loading}
              to={`/appointments/${appt_id}/tools`}
              label={`${tool_count} form used`}
              title="Tools"
              description="Resources, techniques, or assessments used to facilitate care and measure effectiveness."
            />

            <LinkCard
              loading={loading}
              title="Progress Note"
              label={`${prog_count} form used`}
              description="A documented record of each session or interaction, tracking status, updates, and changes over time."
              to={`/appointments/${appt_id}/progress-note`}
            />

            <LinkCard
              loading={loading}
              title="Diagnosis"
              label={`${diag_count} form used`}
              description="A clinical determination identifying conditions based on evaluation and professional judgement."
              to={`/appointments/${appt_id}/diagnosis`}
            />

            <LinkCard
              loading={loading}
              title="Medication & Prescription"
              label={`${medi_count} form used`}
              description="Information on prescribed medications, dosages, and instructions to support treatment."
              to={`/appointments/${appt_id}/medical-prescription`}
            />
          </Grid>
        </Show>

        {/* <Show when={["live", "upcoming"].includes(status) && appt?.referral_indata?.status === 1}>
          <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
            <LinkCard
              loading={loading}
              // label="4 form used"
              title="See referral comment"
              // description={appt?.referral_indata?.comment || "N/A"}
              description="This "
              hideArrow
            />
          </Grid>
        </Show> */}
      </Stack>
    </Container>
  );
}

interface InfoItemProps extends StackProps {
  icon: IconNames;
  label: string;
}

interface UserInfoCardProps extends StackProps {
  loading?: boolean;
  appt?: AppointmentListDataRo;
}

interface ApptInfoCardProps extends StackProps {
  loading?: boolean;
  appt?: AppointmentListDataRo;
}

function InfoItem(props: InfoItemProps) {
  const { icon, label, ...xprops } = props;

  return (
    <HStack gap="8px" {...xprops}>
      <Icon name={icon} boxSize="16px" color="text.3" />
      <Text fontSize="14px" color="text.2" textTransform="capitalize">
        {label}
      </Text>
    </HStack>
  );
}

function UserInfoCard(props: UserInfoCardProps) {
  const { appt, loading = false, ...xprops } = props;

  const mem = appt?.member_data;

  // const {data: user_data} = useUser();
  // const user = user_data?.data;

  // const user_name = user?.

  return (
    <Skeleton variant="shine" loading={loading}>
      <Stack
        gap="12px"
        p="16px"
        bg="input"
        rounded="8px"
        border="1px solid"
        borderColor="stroke.divider"
        // containerName="user-info-card"
        h="100%"
        {...xprops}
      >
        <HStack gap="16px">
          <Avatar.Root boxSize="80px" border="4px solid" borderColor="primary.50">
            <Avatar.Fallback fontSize="12px" name={joinNames(mem?.first_name, mem?.last_name)} />
            {mem?.avatar && <Avatar.Image src={mem?.avatar} />}
          </Avatar.Root>

          <Stack gap="4px">
            <Text fontSize="16px" fontWeight="500" color="text">
              {joinNames(mem?.first_name, mem?.last_name)}
            </Text>
            <Text fontSize="14px" fontWeight="400" color="text.2" textTransform="capitalize">
              {appt?.member_gender || "N/A"}
            </Text>
          </Stack>
        </HStack>

        <Separator />

        <Grid templateColumns="repeat(2, 1fr)" rowGap="12px" /*columnGap="300px"*/>
          <InfoItem icon="hand_heart" label={mem?.marital_status || "N/A"} />
          <InfoItem icon="ship_wheel" label={mem?.religion || "N/A"} />
        </Grid>

        <Show when={appt?.referral_indata?.status === 1}>
          <ConfirmationModal
            title="Referral comment"
            description={appt?.referral_indata?.comment || "N/A"}
            applyBtnText="Close"
            contentProps={{ maxW: "540px" }}
            hideCancelBtn
          >
            <Button
              variant="ghost"
              p="0px"
              size="sm"
              rounded="4px"
              w="fit-content"
              leftIcon={<Icon name="file_text" color="inherit" />}
              focusRingColor="primary"
              _hover={{
                color: "primary",
                textDecor: "underline",
                bg: "transparent",
                textDecorationColor: "primary/50",
                textUnderlineOffset: "4px",
              }}
            >
              This user was referred, see referral note
            </Button>
          </ConfirmationModal>
        </Show>
      </Stack>
    </Skeleton>
  );
}

function AppointmentInfoCard(props: ApptInfoCardProps) {
  const { appt, loading = false, ...xprops } = props;

  const { mutateAsync, isPending: starting } = useMutation(startApptSessionMutationOpts(appt?.appointment_id || ""));
  const { mutateAsync: markAsComplete, isPending: completing } = useMutation(
    markApptSessionAsCompleteMutationOpts(appt?.appointment_id || "")
  );
  const { mutateAsync: cancelAppt, isPending: cancelling } = useMutation(cancelMutationOpts(appt?.appointment_id || ""));

  const datetime = appt?.appt_schedule;
  const status = mapField(appt?.status ?? 0, "appointment") as string;

  const parsed_datetime = useMemo(() => {
    if (!datetime || (datetime && !isValid(parseISO(datetime)))) return "N/A";
    return format(new Date(parseISO(datetime)), "EEE, do MMM, yyyy");
  }, [datetime]);

  const start_end_time = useMemo(() => {
    if (!datetime || (datetime && !isValid(parseISO(datetime)))) return "N/A";

    const start_date = new Date(parseISO(datetime));
    const end_date = add(start_date, { hours: 1 });

    return `${format(start_date, "h:mm a")} - ${format(end_date, "h:mm a")} (1hr)`;
  }, [datetime]);

  // const can_join_appt = canJoinAppointment(datetime ?? "");

  const handleStartSession = async () => {
    const promise = mutateAsync({ appointment_id: appt?.appointment_id });
    const result = await tryCatch(promise);
    if (result.ok) {
      console.log("Start appointment result", result.data);
      toaster.success({
        title: "Success",
        description: "Appointment session has been started successfully",
      });

      handleJoin();
    }
  };

  /**
   * @param status 2.5 - Ended, 3 - Completed
   */
  const handleMarkAsComplete = async (status: "2.5" | "3") => {
    const promise = markAsComplete({ status });
    const result = await tryCatch(promise);
    if (result.ok) {
      toaster.success({
        title: "Success",
        description: "Appointment session has been marked as complete successfully",
      });
    }
  };

  const handleCancel = async () => {
    const promise = cancelAppt({});
    const result = await tryCatch(promise);
    if (result.ok) {
      toaster.success({
        title: "Success",
        description: "Appointment session has been cancelled successfully",
      });
    }
  };

  const handleJoin = () => {
    if (!appt) return;
    const session_link = getSessionLink(appt);
    if (!session_link) return;

    toaster.info({
      title: "Info",
      description: "Joining appointment session...",
    });

    window.open(session_link, "_blank");
  };

  return (
    <Skeleton variant="shine" rounded="8px" minH="194px" loading={loading}>
      <Stack
        gap="12px"
        p="16px"
        bg="input"
        rounded="8px"
        border="1px solid"
        borderColor="stroke.divider"
        containerName="appt-info-card"
        pos="relative"
        h="100%"
        {...xprops}
      >
        <HStack gap="16px" justifyContent="space-between">
          <Text fontSize="16px" fontWeight="500" color="text" textTransform="capitalize">
            {appt?.service_offer_name || "N/A"}
          </Text>

          <ProviderStatus status={status} variant="badge" loading={false} />
        </HStack>

        <Grid
          maxW={{ base: "94%", md: "84%" }}
          templateColumns={{ sm: "1fr", "1sm": "repeat(2, 1fr)" } as any}
          rowGap="12px"
          columnGap="10px"
        >
          <InfoItem icon="calendar_check" label={parsed_datetime} />
          <InfoItem icon="clock" label={start_end_time} />
          <InfoItem icon="video" label={appt?.comm_mode || "N/A"} />
          <InfoItem icon="badge_check" label={appt?.provider_data?.name || "N/A"} />
        </Grid>

        <Show when={["upcoming", "live", "ended"].includes(status.toLowerCase())}>
          <Separator maxW="50%" mt="8px" />
        </Show>

        <Box as={CalendarIllustration} w={{ base: "88px", md: "108px" }} h="78px" pos="absolute" bottom="16px" right="16px" />

        <Show when={status.toLowerCase() === "upcoming"}>
          <HStack>
            <Button
              w="fit-content"
              leftIcon={<Icon name="clipboard_plus" color="inherit" />}
              disabled={starting}
              loading={starting}
              onClick={handleStartSession}
            >
              Start Session
            </Button>

            <ConfirmationModal
              title="Are You Sure You Want to Cancel?"
              description="Canceling this appointment means you will no longer conduct the session and will forfeit any associated compensation. This action is final and cannot be undone."
              applyBtnText="Proceed"
              loading={cancelling}
              onConfirm={handleCancel}
            >
              <Button
                w="fit-content"
                // bg="red.500"
                color="red.500"
                variant="outline"
                leftIcon={<Icon name="plus" color="inherit" rotate="45deg" />}
                _hover={{ color: "white" }}
                disabled={cancelling}
                loading={cancelling}
              >
                Cancel Session
              </Button>
            </ConfirmationModal>
          </HStack>
        </Show>

        <Show when={["live"].includes(status.toLowerCase())}>
          <HStack>
            <Button w="fit-content" leftIcon={<Icon name="clipboard_plus" color="inherit" />} onClick={handleJoin}>
              Join
            </Button>

            <ConfirmationModal
              title="End Session?"
              description="Are you sure you want to end this appointment?"
              applyBtnText="End Session"
              loading={completing}
              onConfirm={async () => handleMarkAsComplete("2.5")}
            >
              <Button
                w="fit-content"
                leftIcon={<Icon name="badge_check" color="inherit" />}
                disabled={completing}
                loading={completing}
                // onClick={handleMarkAsComplete}
              >
                End Session
              </Button>
            </ConfirmationModal>
          </HStack>
        </Show>

        <Show when={["ended"].includes(status.toLowerCase())}>
          <HStack>
            <ConfirmationModal
              title="Mark as complete"
              description="Are you sure you want to mark this appointment as complete?"
              applyBtnText="Mark as complete"
              loading={completing}
              onConfirm={async () => handleMarkAsComplete("3")}
            >
              <Button
                w="fit-content"
                leftIcon={<Icon name="badge_check" color="inherit" />}
                disabled={completing}
                loading={completing}
                // onClick={handleMarkAsComplete}
              >
                Mark as Complete
              </Button>
            </ConfirmationModal>
          </HStack>
        </Show>

        {/* <Show when={!can_join_appt && status.toLowerCase() === "upcoming"}>
          <Button w="fit-content" leftIcon={<Icon name="clock" color="inherit" />} disabled>
            Coming soon
          </Button>
        </Show> */}

        <success_dialog.Viewport />
      </Stack>
    </Skeleton>
  );
}

interface LinkCardProps extends LinkProps {
  title: string;
  description: string;
  label?: string;
  to?: string;
  leftIcon?: ReactNode;
  loading?: boolean;

  titleProps?: TextProps;
  descriptionProps?: TextProps;
  hideArrow?: boolean;
}

/// NOTE: LinkCard has been wrapped in a memo HOC because it was re-rendering even when the props didn't change,
/// and the re-render was visible to the naked eye, memo helps avoid unneccessary re-renders / flashes.
const LinkCard = memo((props: LinkCardProps) => {
  const {
    title,
    description,
    label,
    to,
    children,
    leftIcon,
    loading = false,
    titleProps,
    descriptionProps,
    hideArrow = false,
    ...xprops
  } = props;

  type MaybeRouterLinkType = RouterLinkProps | BoxProps;

  const MaybeRouterLink = (props: MaybeRouterLinkType) => {
    if (!to) return <Box w="100%" {...(props as BoxProps)} cursor="default" />;
    return <RouterLink viewTransition {...(props as RouterLinkProps)} />;
  };

  return (
    <Link
      asChild={!!to}
      as={to ? undefined : "div"}
      p="16px"
      bg="input"
      rounded="8px"
      border="1px solid"
      borderColor="stroke.divider"
      containerName="link-card"
      textDecor="none"
      focusRingColor="primary"
      {...xprops}
    >
      <MaybeRouterLink to={to}>
        <HStack w="100%" gap="16px" justifyContent="space-between">
          <HStack gap="8px">
            {!!leftIcon && leftIcon}
            <Stack gap="4px">
              <Skeleton variant="shine" w="fit-content" loading={loading}>
                <Text fontSize="14px" fontWeight="500" color="text" {...titleProps}>
                  {title}
                </Text>
              </Skeleton>

              <Skeleton variant="shine" loading={loading}>
                <Text fontSize="12px" fontWeight="400" color="text.2" {...descriptionProps}>
                  {description}
                </Text>
              </Skeleton>

              {!!label && (
                <Skeleton variant="shine" loading={loading}>
                  <Text
                    py="4px"
                    px="8px"
                    fontSize="12px"
                    color="text.2"
                    rounded="8px"
                    bg="white"
                    w="fit-content"
                    border="1px solid"
                    borderColor="stroke.divider"
                  >
                    {label}
                  </Text>
                </Skeleton>
              )}
            </Stack>
          </HStack>

          <SkeletonCircle variant="shine" loading={loading}>
            {!children && !hideArrow && <Icon name="external_link" boxSize="24px" color="primary" />}
          </SkeletonCircle>

          {!!children && children}
        </HStack>
      </MaybeRouterLink>
    </Link>
  );
});

function getSessionLink(appt: AppointmentListDataRo) {
  const { appointment_id, member_id, provider_id } = appt;
  if (!appointment_id || !member_id || !provider_id) return null;

  const base_url = configs.APPT_SESSION_URL;
  return `${base_url}?user_id=${member_id}&provider_id=${provider_id}&appointment_id=${appointment_id}`;
}
