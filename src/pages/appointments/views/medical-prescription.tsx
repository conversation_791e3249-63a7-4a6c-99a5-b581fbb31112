/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Empty, Icon, SubmitButton, toaster, useAppForm } from "@/components";
import {
  Accordion,
  ButtonGroup,
  Container,
  Grid,
  Heading,
  HStack,
  Show,
  Skeleton,
  SkeletonCircle,
  Span,
  Stack,
  VStack,
} from "@chakra-ui/react";
import { useParams } from "react-router";
import { useCallback, useMemo } from "react";
import { useMutation, usePartialState } from "@/hooks";
import {
  createMedicalPrescriptionMutationOpts,
  deleteMedicalPrescriptionMutationOpts,
  getAppointmentMedicalPrescriptionQueryOpts,
} from "@/queries";
import { toQueryString, tryCatch } from "@/utils";
import { useQuery } from "@tanstack/react-query";

import { AddMedicalPrescriptionDto, addMedicalPrescriptionSchema } from "@/schemas";
import { useStore } from "@tanstack/react-form";

function getRandomStr() {
  return Math.random().toString(36).substring(7);
}

const empty_prescription = {
  temp_id: "",
  med_name: "",
  med_dosage: "",
  med_freq: "",
  med_type: "",
  start_date: "",
  end_date: "",
};

export function AppointmentMedicalPrescription() {
  const params = useParams();

  const appt_id = params.id;
  // const [value, setValue] = useState(["second-item"]);
  const { data, isPending: loading } = useQuery(
    getAppointmentMedicalPrescriptionQueryOpts(toQueryString({ appointment_id: appt_id || "" }))
  );
  const medical_presc_data = useMemo(() => data?.data || [], [data]);

  console.log("Medical Prescription Data", medical_presc_data);

  const mapped_data = useMemo(() => {
    return medical_presc_data.map(
      (item) =>
        ({
          temp_id: `presc-${getRandomStr()}`,
          ...item,

          //   category: item.data_name,
          //   forms: item.data,
        } as AddMedicalPrescriptionDto)
    );
  }, [medical_presc_data]);

  // const loading = false;

  const [{ addedPresc = [] }, set] = usePartialState<{
    addedPresc: AddMedicalPrescriptionDto[];
  }>({ addedPresc: mapped_data }, [mapped_data]);

  const addPresc = useCallback(() => {
    const new_presc = { ...empty_prescription, appointment_id: appt_id || "" };
    new_presc.temp_id = `presc-${getRandomStr()}`;

    console.log("New prescription", new_presc);
    set({ addedPresc: [new_presc, ...addedPresc] });
  }, [set, addedPresc, appt_id]);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Appointments", to: "/appointments", is_first: true },
            { title: "Appointment details", to: `/appointments/${appt_id}` },
            { title: "Medication & Prescription", to: "#" },
          ]}
        />

        <Heading as="h5" fontSize="20px" fontWeight="600">
          Medication & Prescription
        </Heading>

        <Stack gap="16px" mt="24px">
          <HStack justifyContent="space-between">
            <Heading as="h6" fontSize="16px" fontWeight="600">
              Medications
            </Heading>

            <Button variant="solid" leftIcon={<Icon name="plus" color="inherit" />} onClick={addPresc}>
              Add Medication
            </Button>
          </HStack>

          <Show when={addedPresc.length < 1 && !loading}>
            <VStack my="10svh">
              <Empty subtitle="No medications added yet" />
            </VStack>
          </Show>

          <Show when={loading}>
            <Accordion.Root collapsible display="flex" flexDir="column" gap="16px">
              {Array.from({ length: 3 }).map((_, index) => (
                <Accordion.Item
                  p="16px"
                  key={index}
                  value={`${index}-item`}
                  border="1px solid"
                  borderColor="stroke.divider"
                  rounded="8px"
                  css={{
                    "&[data-state='open']": {
                      borderColor: "primary",
                      backgroundColor: "primary.50/20",
                    },
                  }}
                >
                  <Accordion.ItemTrigger p="0" justifyContent="space-between">
                    <Skeleton variant="shine" loading={loading}>
                      <Span flex="1">Category Name</Span>
                    </Skeleton>

                    <SkeletonCircle variant="shine" loading={loading}>
                      <Accordion.ItemIndicator />
                    </SkeletonCircle>
                  </Accordion.ItemTrigger>
                  <Accordion.ItemContent>
                    <Accordion.ItemBody pb="0">
                      <MedicalPrescriptionForm appt_id={appt_id || ""} loading />
                    </Accordion.ItemBody>
                  </Accordion.ItemContent>
                </Accordion.Item>
              ))}
            </Accordion.Root>
          </Show>

          <Show when={addedPresc.length > 0 && !loading}>
            <Accordion.Root
              collapsible
              display="flex"
              flexDir="column"
              gap="16px" /*value={value} onValueChange={(e) => setValue(e.value)}*/
            >
              {addedPresc.map((item, index) => (
                <Accordion.Item
                  p="16px"
                  key={index}
                  value={`${index}-item`}
                  border="1px solid"
                  borderColor="stroke.divider"
                  rounded="8px"
                  css={{
                    "&[data-state='open']": {
                      borderColor: "primary",
                      backgroundColor: "primary.50/20",
                    },
                  }}
                  viewTransitionName={`accordion-item-${index}`}
                >
                  <Accordion.ItemTrigger p="0" justifyContent="space-between">
                    <Skeleton variant="shine" loading={loading}>
                      <Span flex="1">{item?.med_name || `Medication ${addedPresc.length - index}`}</Span>
                    </Skeleton>

                    <SkeletonCircle variant="shine" loading={loading}>
                      <Accordion.ItemIndicator />
                    </SkeletonCircle>
                  </Accordion.ItemTrigger>
                  <Accordion.ItemContent>
                    <Accordion.ItemBody pb="0">
                      <MedicalPrescriptionForm
                        appt_id={appt_id || ""}
                        form_data={item}
                        // form_data={item}
                        loading={loading}
                        removeFromSelectedItems={(id) => set({ addedPresc: addedPresc.filter((i) => i?.temp_id !== id) })}
                      />
                    </Accordion.ItemBody>
                  </Accordion.ItemContent>
                </Accordion.Item>
              ))}
            </Accordion.Root>
          </Show>
        </Stack>
      </Stack>
    </Container>
  );
}

function MedicalPrescriptionForm(props: {
  appt_id: string;
  form_data?: AddMedicalPrescriptionDto;
  loading?: boolean;
  removeFromSelectedItems?: (id: string) => void;
}) {
  const { appt_id, form_data, loading = true, removeFromSelectedItems } = props;

  const { mutateAsync, isPending: saving } = useMutation(createMedicalPrescriptionMutationOpts(appt_id));
  const { mutateAsync: deletePrescription, isPending: deleting } = useMutation(
    deleteMedicalPrescriptionMutationOpts(appt_id, form_data?.data_id || "")
  );

  // console.log("Treatment Plan Form", form_data);

  const default_values = { ...empty_prescription, ...form_data, appointment_id: form_data?.appointment_id || appt_id || "" };

  const form = useAppForm({
    defaultValues: default_values,
    validators: {
      onChange: addMedicalPrescriptionSchema,
      onSubmit: addMedicalPrescriptionSchema,
    },
    async onSubmit({ value }) {
      // console.log("Medication & Prescription value", value.prescriptions);

      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "Medication & Prescription has been added successfully",
        });
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  const values = useStore(form.store, (store) => store.values);
  const errors = useStore(form.store, (store) => store.errors);

  console.log("Medication Prescription", { values, errors });

  if (loading) {
    return (
      <HStack justifyContent="space-between">
        <Skeleton variant="shine" h="4px" w="20%" loading={loading} />
        <Skeleton variant="shine" h="4px" w="40%" loading={loading} />
      </HStack>
    );
  }

  return (
    <form.AppForm>
      <Stack gap="16px" as="form" onSubmit={handleSubmit}>
        <Stack gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
          <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)" } as any} gap="16px">
            <form.AppField name="med_name">
              {(field) => (
                <field.TextField
                  label="Medication Name"
                  field={field}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.setValue(e.target.value)}
                />
              )}
            </form.AppField>

            <form.AppField name="med_dosage">
              {(field) => (
                <field.TextField
                  label="Dosage"
                  field={field}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.setValue(e.target.value)}
                />
              )}
            </form.AppField>

            <form.AppField name="med_freq">
              {(field) => (
                <field.TextField
                  label="Frequency"
                  field={field}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.setValue(e.target.value)}
                />
              )}
            </form.AppField>

            <form.AppField name="med_type">
              {(field) => (
                <field.Select
                  label="Type"
                  field={field}
                  value={[field.state.value]}
                  onBlur={field.handleBlur}
                  onValueChange={(e) => field.setValue(e.value[0])}
                  items={[
                    { label: "Oral", value: "Oral" },
                    { label: "Syrup", value: "Syrup" },
                    { label: "Patch", value: "Patch" },
                    { label: "Injection", value: "Injection" },
                    { label: "Inhaler", value: "Inhaler" },
                    { label: "Topical", value: "Topical" },
                    { label: "Suppository", value: "Suppository" },
                    { label: "Other", value: "Other" },
                  ]}
                />
              )}
            </form.AppField>

            <form.AppField name="start_date">
              {(field) => (
                <field.TextField
                  type="date"
                  label="Start Date"
                  field={field}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.setValue(e.target.value)}
                />
              )}
            </form.AppField>

            <form.AppField name="end_date">
              {(field) => (
                <field.TextField
                  type="date"
                  label="End Date"
                  field={field}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.setValue(e.target.value)}
                />
              )}
            </form.AppField>
          </Grid>
        </Stack>

        <HStack py="16px" justifyContent="flex-end" alignItems="center">
          <ButtonGroup gap="24px">
            <Button
              variant="subtle"
              loading={deleting}
              disabled={deleting || saving}
              onClick={async () => {
                // await onDelete?.();
                if (form_data?.data_id) {
                  const promise = deletePrescription({});
                  const result = await tryCatch(promise);
                  if (result.ok) {
                    toaster.success({
                      title: "Success",
                      description: "Medical Prescription has been deleted successfully",
                    });
                  }
                }
                form.reset();
                removeFromSelectedItems?.(form_data?.temp_id || "");
              }}
            >
              Delete
            </Button>

            <SubmitButton disabled={deleting || saving} loading={saving}>
              Save & Update
            </SubmitButton>
          </ButtonGroup>
        </HStack>
      </Stack>
    </form.AppForm>
  );
}
