/* eslint-disable @typescript-eslint/no-explicit-any */
import { Breadcrumb, Icon } from "@/components";

import {
  appointmentDiagnosisDatasetQueryOpts,
  getAssessmentMedicalRecordsQueryOpts,
  getMedicationMedicalRecordsQueryOpts,
  getProgressNoteMedicalRecordsQueryOpts,
  getToolsMedicalRecordsQueryOpts,
  getTreatmentPlanMedicalRecordsQueryOpts,
} from "@/queries";
import { toQueryString } from "@/utils";
import {
  Box,
  BoxProps,
  Container,
  Grid,
  HStack,
  Link,
  LinkProps,
  Skeleton,
  SkeletonCircle,
  Stack,
  Text,
  TextProps,
} from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { memo, ReactNode, useMemo } from "react";
import { Link as RouterLink, LinkProps as RouterLinkProps, useParams } from "react-router";

export function AppointmentMedicalRecords() {
  const params = useParams();
  const appt_id = params?.id || "";

  const { data: diag_data, isPending: loading_diag_data } = useQuery(appointmentDiagnosisDatasetQueryOpts(appt_id, "count"));
  const { data: tool_data, isPending: loading_tool_data } = useQuery(
    getToolsMedicalRecordsQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" }))
  );
  const { data: asse_data, isPending: loading_asse_data } = useQuery(
    getAssessmentMedicalRecordsQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" }))
  );
  const { data: prog_data, isPending: loading_prog_data } = useQuery(
    getProgressNoteMedicalRecordsQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" }))
  );
  const { data: medi_data, isPending: loading_medi_data } = useQuery(
    getMedicationMedicalRecordsQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" }))
  );
  const { data: trtm_data } = useQuery(
    getTreatmentPlanMedicalRecordsQueryOpts(toQueryString({ appointment_id: appt_id, component: "count" }))
  );

  //   const { data: tools } = useQuery(getAppointmentMedicalRecordsToolsQueryOpts(toQueryString({ appointment_id: appt_id })));
  //   const appt = data?.data;

  //   console.log("Tools", tools);

  const loading = loading_diag_data || loading_tool_data || loading_asse_data || loading_prog_data || loading_medi_data;

  const diag_count = useMemo(() => (diag_data?.data as unknown as { total: number })?.total || 0, [diag_data]);
  const asse_count = useMemo(() => (asse_data?.data as unknown as { total: number })?.total || 0, [asse_data]);
  const trtm_count = useMemo(() => (trtm_data?.data as unknown as { total: number })?.total || 0, [trtm_data]);
  const tool_count = useMemo(() => (tool_data?.data as unknown as { total: number })?.total || 0, [tool_data]);
  const prog_count = useMemo(() => (prog_data?.data as unknown as { total: number })?.total || 0, [prog_data]);
  const medi_count = useMemo(() => (medi_data?.data as unknown as { total: number })?.total || 0, [medi_data]);

  //   const status = useMemo(() => (mapField(appt?.status ?? 0, "appointment") as string).toLowerCase(), [appt]);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Appointments", to: "/appointments", is_first: true },
            { title: "Appointment Details", to: `/appointments/${appt_id}` },
            { title: "Medical Records", to: "#" },
          ]}
        />

        <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <LinkCard
            loading={loading}
            to={`/appointments/${appt_id}/medical-records/assessment`}
            label={`${asse_count} form used`}
            title="Assessment"
            description="A comprehensive evaluation that gathers relevant background, strengths, and areas of concern to inform care."
          />

          <LinkCard
            loading={loading}
            title="Treatment Plan"
            label={`${trtm_count} form used`}
            description="A structured outline of goals, interventions, and strategies designed to guide ongoing support."
            to={`/appointments/${appt_id}/medical-records/treatment-plan`}
          />
        </Grid>

        <Grid templateColumns={{ sm: "1fr", "2sm": "repeat(2, 1fr)" } as any} gap="16px">
          <LinkCard
            loading={loading}
            to={`/appointments/${appt_id}/medical-records/tools`}
            label={`${tool_count} form used`}
            title="Tools"
            description="Resources, techniques, or assessments used to facilitate care and measure effectiveness."
          />

          <LinkCard
            loading={loading}
            title="Progress Note"
            label={`${prog_count} form used`}
            description="A documented record of each session or interaction, tracking status, updates, and changes over time."
            to={`/appointments/${appt_id}/medical-records/progress-note`}
          />

          <LinkCard
            loading={loading}
            title="Diagnosis"
            label={`${diag_count} form used`}
            description="A clinical determination identifying conditions based on evaluation and professional judgement."
            to={`/appointments/${appt_id}/medical-records/diagnosis`}
          />

          <LinkCard
            loading={loading}
            title="Medication & Prescription"
            label={`${medi_count} form used`}
            description="Information on prescribed medications, dosages, and instructions to support treatment."
            to={`/appointments/${appt_id}/medical-records/medication`}
          />
        </Grid>
      </Stack>
    </Container>
  );
}

interface LinkCardProps extends LinkProps {
  title: string;
  description: string;
  label?: string;
  to?: string;
  leftIcon?: ReactNode;
  loading?: boolean;

  titleProps?: TextProps;
  descriptionProps?: TextProps;
}

/// NOTE: LinkCard has been wrapped in a memo HOC because it was re-rendering even when the props didn't change,
/// and the re-render was visible to the naked eye, memo helps avoid unneccessary re-renders / flashes.
const LinkCard = memo((props: LinkCardProps) => {
  const { title, description, label, to, children, leftIcon, loading = false, titleProps, descriptionProps, ...xprops } = props;

  type MaybeRouterLinkType = RouterLinkProps | BoxProps;

  const MaybeRouterLink = (props: MaybeRouterLinkType) => {
    if (!to) return <Box w="100%" {...(props as BoxProps)} cursor="default" />;
    return <RouterLink viewTransition {...(props as RouterLinkProps)} />;
  };

  return (
    <Link
      asChild={!!to}
      as={to ? undefined : "div"}
      p="16px"
      bg="input"
      rounded="8px"
      border="1px solid"
      borderColor="stroke.divider"
      containerName="link-card"
      textDecor="none"
      focusRingColor="primary"
      {...xprops}
    >
      <MaybeRouterLink to={to}>
        <HStack w="100%" gap="16px" justifyContent="space-between">
          <HStack gap="8px">
            {!!leftIcon && leftIcon}
            <Stack gap="4px">
              <Skeleton variant="shine" w="fit-content" loading={loading}>
                <Text fontSize="14px" fontWeight="500" color="text" {...titleProps}>
                  {title}
                </Text>
              </Skeleton>

              <Skeleton variant="shine" loading={loading}>
                <Text fontSize="12px" fontWeight="400" color="text.2" {...descriptionProps}>
                  {description}
                </Text>
              </Skeleton>

              {!!label && (
                <Skeleton variant="shine" loading={loading}>
                  <Text
                    py="4px"
                    px="8px"
                    fontSize="12px"
                    color="text.2"
                    rounded="8px"
                    bg="white"
                    w="fit-content"
                    border="1px solid"
                    borderColor="stroke.divider"
                  >
                    {label}
                  </Text>
                </Skeleton>
              )}
            </Stack>
          </HStack>

          <SkeletonCircle variant="shine" loading={loading}>
            {!children && <Icon name="external_link" boxSize="24px" color="primary" />}
          </SkeletonCircle>

          {!!children && children}
        </HStack>
      </MaybeRouterLink>
    </Link>
  );
});
