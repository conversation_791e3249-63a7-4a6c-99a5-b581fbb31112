import { <PERSON>readcrumb, Empty, FieldCard, Icon } from "@/components";
import { Radio, RadioGroup } from "@/components/ui/radio";
import { AppointmentListDataRo } from "@/interfaces";
import { appointmentByIdQueryOpts } from "@/queries";
import { Checkbox, Container, Heading, HStack, Separator, Show, Skeleton, Span, Stack, Text, VStack } from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import capitalize from "lodash.capitalize";

import { useMemo } from "react";
import { useParams } from "react-router";

type QuestionType = AppointmentListDataRo["member_questionnaire"][0];
type GroupedQuestionType = { ques: QuestionType[]; sub_ques: Map<string, QuestionType[]> };

export function AppointmentBookingQuestions() {
  const params = useParams();
  const appt_id = params?.id;

  const { data, isPending: loading } = useQuery(appointmentByIdQueryOpts(appt_id));
  const appt = data?.data;
  const questions = useMemo(() => appt?.member_questionnaire || [], [appt]);
  const is_empty = useMemo(() => questions.length < 1, [questions]);

  const grouped_questions = useMemo(() => {
    const result = questions.reduce(
      (res, curr) => {
        if (curr.sub_question) {
          const sub_ques = res.sub_ques.get(curr.question) || [];
          res.sub_ques.set(curr.question, [...sub_ques, curr]);
        } else {
          res.ques.push(curr);
        }
        return res;
      },
      { ques: [], sub_ques: new Map() } as GroupedQuestionType
    );

    return { ...result, sub_ques: Object.entries(Object.fromEntries(result.sub_ques)) };
  }, [questions]);

  //   console.log("Questions", grouped_questions);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Appointments", to: "/appointments", is_first: true },
            { title: "Appointment details", to: `/appointments/${appt_id}` },
            { title: "Booking Questionnaire", to: "#" },
          ]}
        />

        <Heading as="h5" fontSize="20px" fontWeight="600">
          Booking Questionnaire
        </Heading>

        <Show when={loading}>
          <FieldCard p="16px">
            <Stack gap="12px">
              {Array.from({ length: 6 }).map((_, i) => (
                <>
                  <QuestionItem question={"Question"} answer={"Answer"} loading />
                  {i < 5 && (
                    <Skeleton variant="shine" h="2px" loading>
                      <Separator />
                    </Skeleton>
                  )}
                </>
              ))}
            </Stack>
          </FieldCard>
        </Show>

        <Show when={is_empty && !loading}>
          <FieldCard>
            <VStack my="20vh">
              <Empty subtitle="No questions found" illustrationProps={{ boxSize: "54px" }} />
            </VStack>
          </FieldCard>
        </Show>

        <Show when={!is_empty && !loading}>
          <FieldCard p="16px">
            <Stack gap="12px">
              {grouped_questions.ques.map((item, i) => (
                <>
                  <QuestionItem key={`single-question-${i}`} question={item.question} answer={item.answer} />
                  {i < grouped_questions.ques.length - 1 && <Separator />}
                </>
              ))}
            </Stack>
          </FieldCard>

          {grouped_questions.sub_ques.map(([question, sub_questions], i) => (
            <FieldCard key={`sub-question-group-${i}`} p="16px">
              <Stack gap="12px">
                <Heading as="h5" fontSize="14px" fontWeight="600">
                  {question}
                </Heading>

                {sub_questions.map((item, j) => (
                  <>
                    <QuestionItem key={`sub-question-${j}`} question={item.sub_question} answer={item.answer} is_sub_question />
                    {j < sub_questions.length - 1 && <Separator />}
                  </>
                ))}
              </Stack>
            </FieldCard>
          ))}
        </Show>
      </Stack>
    </Container>
  );
}

function AnswerItem(props: { answer: string | string[]; has_subquestion?: boolean; loading?: boolean }) {
  const { answer, has_subquestion = false, loading = false } = props;

  if (Array.isArray(answer)) {
    return (
      <HStack gap="8px" flexWrap={"wrap"}>
        {answer.map((item, i) => (
          <Answer key={`answer-${i}`} value={item} loading={loading} use_radio={has_subquestion} use_checkbox />
        ))}
      </HStack>
    );
  }

  return <Answer value={answer} loading={loading} use_radio={has_subquestion} />;
}

function Answer(props: { value: string; loading?: boolean; use_radio?: boolean; use_checkbox?: boolean }) {
  const { value, loading = false, use_radio = false, use_checkbox = false } = props;

  const icon = useMemo(() => {
    const no = ["no", "never", "not applicable", "none"].includes(value.toLowerCase());
    const yes = ["yes", "always", "applicable", "all"].includes(value.toLowerCase());

    if (no) return "circle_cancel";
    if (yes) return "circle_check";
    if (use_radio || use_checkbox) return null;
    return "circle_check";
  }, [value, use_checkbox, use_radio]);

  return (
    <Skeleton variant="shine" rounded="full" loading={loading}>
      <HStack w="fit-content" gap="8px" py="8px" px="12px" bg="primary.50" border="1px solid" borderColor="primary" rounded="full">
        {icon && <Icon name={icon} boxSize="16px" color="primary" />}

        {use_radio && (
          <RadioGroup value={"a"} h="20px">
            <Radio value="a" indicatorProps={{ boxSize: "16px" }} />
          </RadioGroup>
        )}

        {use_checkbox && !use_radio && (
          <Checkbox.Root checked pointerEvents="none">
            <Checkbox.HiddenInput />
            <Checkbox.Control
              boxSize="14px"
              rounded="4px"
              css={{
                "&[data-state=checked]": {
                  bg: "primary",
                  borderColor: "primary",

                  "& svg": {
                    strokeWidth: "4px",
                    transform: "scale(1.2)",
                  },
                },
              }}
            />
          </Checkbox.Root>
        )}

        <Span fontSize="14px" fontWeight="400" color="primary">
          {capitalize(value)}
        </Span>
      </HStack>
    </Skeleton>
  );
}

function QuestionItem(props: { question: string; answer: string | string[]; is_sub_question?: boolean; loading?: boolean }) {
  const { question, answer, is_sub_question = false, loading = false } = props;

  if (Array.isArray(answer)) {
    return (
      <Stack gap="8px">
        <Skeleton variant="shine" loading={loading}>
          <Text fontSize="14px" fontWeight="400" color="text">
            {question}
          </Text>
        </Skeleton>

        <AnswerItem answer={answer} loading={loading} has_subquestion={is_sub_question} />
      </Stack>
    );
  }

  return (
    <HStack gap="8px" justifyContent="space-between">
      <Skeleton variant="shine" loading={loading}>
        <Text fontSize="14px" fontWeight="400" color="text">
          {question}
        </Text>
      </Skeleton>

      <AnswerItem answer={answer} loading={loading} has_subquestion={is_sub_question} />
    </HStack>
  );
}
