/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON>read<PERSON><PERSON>b, Button, Empty, FieldCard, Icon, ShowFieldWhen, SubmitButton, toaster, useAppForm } from "@/components";
import {
  Accordion,
  Combobox,
  Container,
  Grid,
  Heading,
  Highlight,
  HStack,
  Portal,
  Show,
  Skeleton,
  SkeletonCircle,
  SkeletonText,
  Span,
  Spinner,
  Stack,
  Text,
  useComboboxContext,
  useFilter,
  useListCollection,
  VStack,
} from "@chakra-ui/react";
import { useParams } from "react-router";
import { SyntheticEvent, useCallback, useMemo, useState } from "react";
import { AddDiagnosisDto, addDiagnosisSchema } from "@/schemas";
import { useQuery } from "@tanstack/react-query";
import {
  appointmentDiagnosisDatasetQueryOpts,
  createDiagnosisMutationOpts,
  deleteDiagnosisMutationOpts,
  getAppointmentDiagnosisQueryOpts,
} from "@/queries";
import { AppointmentDiagnosisDatasetDataRo } from "@/interfaces";
import { useDebouncedCallback } from "use-debounce";
import { useMutation } from "@/hooks";
import { getArrayChanges, toQueryString, tryCatch } from "@/utils";

import { useAsync } from "react-use";

// const empty_icd = (appt_id: string) => ({
//   id: "",
//   appointment_id: appt_id,
//   code9: "",
//   code10: "",
//   description: "",
// });

export function AppointmentDiagnosis() {
  // const qc = useQueryClient()
  const params = useParams();

  const appt_id = params?.id;

  const [searchValue, setSearchValue] = useState("");

  const {
    data,
    isPending: fetching_dataset,
    promise,
  } = useQuery({ ...appointmentDiagnosisDatasetQueryOpts(appt_id || ""), experimental_prefetchInRender: true });
  const { data: diagnosis_data, isPending: fetching_diagnosis } = useQuery(
    getAppointmentDiagnosisQueryOpts(toQueryString({ appointment_id: appt_id || "" }))
  );
  const { mutateAsync } = useMutation(createDiagnosisMutationOpts(appt_id || ""));

  // console.log("Diagnosis data", diagnosis_data);

  const { contains } = useFilter({ sensitivity: "base", ignorePunctuation: true });

  const diagnosis_dataset = useMemo(() => data?.data || [], [data]);
  const saved_diagnosis_list = useMemo(() => diagnosis_data?.data || [], [diagnosis_data]);

  const { collection, filter, set } = useListCollection({
    limit: 80,
    initialItems: diagnosis_dataset,

    filter: contains,
    itemToValue(item) {
      return item.id;
    },
    itemToString(item) {
      return `${item.icd_10_code} ${item.icd_9_code} ${item.description}`.toLowerCase();
    },
  });

  useAsync(async () => {
    const data = await promise;
    // console.log("Promise result", data);
    set(data?.data || []);
  }, [promise]);

  const debounced = useDebouncedCallback((value) => {
    filter(value);
  }, 500);

  const handleInputChange = (details: Combobox.InputValueChangeDetails) => {
    const newValue = details.inputValue;
    setSearchValue(newValue);
    debounced(newValue);
  };

  const default_values = useMemo(() => {
    return saved_diagnosis_list.map((item) => ({
      appointment_id: item?.appointment_id || appt_id || "",
      code9: item.code9,
      code10: item.code10,
      description: item.description,
      data_id: item?.data_id,
    }));
  }, [saved_diagnosis_list, appt_id]);

  const getChanges = useCallback((values: AddDiagnosisDto["icds"]) => getArrayChanges(default_values, values, "data_id"), [default_values]);

  const form = useAppForm({
    defaultValues: {
      icds: default_values,
    } as AddDiagnosisDto,
    validators: {
      onChange: addDiagnosisSchema,
      onSubmit: addDiagnosisSchema,
    },
    listeners: {
      onChange: ({ formApi: { state } }) => {
        const values = state.values;
        const changes = getArrayChanges(default_values, values.icds, "code10");
        console.log("Form Changed", changes, values);
      },
    },
    async onSubmit({ value }) {
      // console.log("Diagnosis value", value.icds);

      const changes = getChanges(value.icds);
      if (!changes.added.length && !changes.removed.length && !changes.modified.length) return;
      console.log("Submission Values", changes);

      const promise = mutateAsync({ appointment_id: appt_id || "", data: changes.added });
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "Diagnosis has been added successfully",
        });
      }
    },
  });

  const handleSubmit = (e: SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  return (
    <form.AppForm>
      <Container maxW="100rem" py="24px">
        <Stack gap="16px" as="form" onSubmit={handleSubmit}>
          <Breadcrumb
            items={[
              { title: "Appointments", to: "/appointments", is_first: true },
              { title: "Appointment details", to: `/appointments/${appt_id}` },
              { title: "Diagnosis", to: "#" },
            ]}
          />

          <Heading as="h5" fontSize="20px" fontWeight="600">
            Diagnostic Impression
          </Heading>

          <FieldCard>
            <Combobox.Root
              multiple
              closeOnSelect
              width="100%"
              openOnClick
              // value={selectedItems}
              inputValue={searchValue}
              collection={collection}
              onValueChange={(e) => {
                const latest = e.items[e.items.length - 1];

                form.insertFieldValue("icds", 0, {
                  appointment_id: appt_id || "",
                  code9: latest.icd_9_code,
                  code10: latest.icd_10_code,
                  description: latest.description,
                });
              }}
              onInputValueChange={handleInputChange}
              // openOnChange={(e) => e.inputValue.length >= 2}
            >
              <Combobox.Control>
                <Combobox.Input
                  placeholder="Type to search diagnosis by ICD-9, ICD-10, or description..."
                  rounded="4px"
                  h="48px"
                  bg="input"
                  borderColor="transparent"
                  focusRingColor="primary"
                  focusRingWidth="2px"
                  _focus={{ bg: "primary.50" }}
                />
                <Combobox.IndicatorGroup>
                  {!fetching_dataset && <Combobox.Trigger />}
                  {fetching_dataset && <Spinner size="xs" color="primary" />}
                </Combobox.IndicatorGroup>
              </Combobox.Control>

              <Portal>
                <Combobox.Positioner>
                  <Combobox.Content>
                    <Combobox.ItemGroup>
                      {collection.items.map((item) => (
                        <ComboboxItem item={item} />
                      ))}

                      <Combobox.Empty>No diagnosis found</Combobox.Empty>
                    </Combobox.ItemGroup>
                  </Combobox.Content>
                </Combobox.Positioner>
              </Portal>
            </Combobox.Root>
          </FieldCard>

          <Stack gap="16px" mt="24px">
            <Heading as="h6" fontSize="16px" fontWeight="600">
              Diagnosis
            </Heading>

            <ShowFieldWhen when={(state) => (state.icds as string[]).length < 1 && !fetching_diagnosis}>
              <VStack my="10svh">
                <Empty subtitle="No diagnosis added yet" />
              </VStack>
            </ShowFieldWhen>

            <Show when={fetching_diagnosis}>
              <Accordion.Root collapsible display="flex" flexDir="column" gap="16px">
                {Array.from({ length: 3 }).map((_, index) => (
                  <Accordion.Item
                    p="16px"
                    key={index}
                    value={`${index}-item`}
                    border="1px solid"
                    borderColor="stroke.divider"
                    rounded="8px"
                    css={{
                      "&[data-state='open']": {
                        borderColor: "primary",
                        backgroundColor: "primary.50/20",
                      },
                    }}
                  >
                    <Accordion.ItemTrigger p="0" justifyContent="space-between">
                      <Skeleton variant="shine" loading>
                        <Span flex="1">Category Name</Span>
                      </Skeleton>

                      <SkeletonCircle variant="shine" loading>
                        <Accordion.ItemIndicator />
                      </SkeletonCircle>
                    </Accordion.ItemTrigger>
                    <Accordion.ItemContent>
                      <Accordion.ItemBody pb="0">
                        <SkeletonText variant="shine" loading />
                      </Accordion.ItemBody>
                    </Accordion.ItemContent>
                  </Accordion.Item>
                ))}
              </Accordion.Root>
            </Show>

            <Show when={!fetching_diagnosis}>
              <Accordion.Root collapsible display="flex" flexDir="column" gap="16px">
                <form.AppField name="icds" mode="array">
                  {(field) =>
                    field.state.value.map((item, index) => (
                      <Accordion.Item
                        p="16px"
                        key={index}
                        value={item.code10}
                        border="1px solid"
                        borderColor="stroke.divider"
                        rounded="8px"
                        css={{
                          "&[data-state='open']": {
                            borderColor: "primary",
                            backgroundColor: "primary.50/20",
                          },
                        }}
                        viewTransitionName={`accordion-item-${item.code10}`}
                      >
                        <Accordion.ItemTrigger p="0" justifyContent="space-between">
                          <HStack>
                            <Span>{item.code10}</Span>
                            <Span>/</Span>
                            <Span color="text.2">{item.code9}</Span>
                            <Span>-</Span>
                            <Span color="text.3" maxW={{ sm: "40svw", "2sm": "100%" } as any} truncate>
                              {item.description}
                            </Span>
                          </HStack>

                          <Accordion.ItemIndicator />
                        </Accordion.ItemTrigger>
                        <Accordion.ItemContent>
                          <Accordion.ItemBody pb="0">
                            <Stack key={index} gap="16px" px="4px">
                              <Grid templateColumns={{ sm: "1fr", "3sm": ".5fr .5fr 1fr" } as any} gap="16px">
                                <form.AppField name={`icds[${index}].code9`}>
                                  {(field) => (
                                    <field.TextField
                                      label="ICD-9"
                                      field={field}
                                      disabled
                                      value={field.state.value}
                                      onBlur={field.handleBlur}
                                      onChange={(e) => field.setValue(e.target.value)}
                                    />
                                  )}
                                </form.AppField>
                                <form.AppField name={`icds[${index}].code10`}>
                                  {(field) => (
                                    <field.TextField
                                      label="ICD-10"
                                      field={field}
                                      disabled
                                      value={field.state.value}
                                      onBlur={field.handleBlur}
                                      onChange={(e) => field.setValue(e.target.value)}
                                    />
                                  )}
                                </form.AppField>

                                <form.AppField name={`icds[${index}].description`}>
                                  {(field) => (
                                    <field.Textarea
                                      label="Description"
                                      placeholder="Description"
                                      h="48px"
                                      disabled
                                      hideCharCount
                                      field={field}
                                      value={field.state.value}
                                      onBlur={field.handleBlur}
                                      onChange={(e) => field.setValue(e.target.value)}
                                    />
                                  )}
                                </form.AppField>
                              </Grid>

                              <HStack>
                                <DeleteButton
                                  appointment_id={appt_id || ""}
                                  data_id={item?.data_id}
                                  removeFromForm={() => form.removeFieldValue("icds", index)}
                                />
                              </HStack>
                            </Stack>
                          </Accordion.ItemBody>
                        </Accordion.ItemContent>
                      </Accordion.Item>
                    ))
                  }
                </form.AppField>
              </Accordion.Root>
            </Show>

            <ShowFieldWhen when={(state) => (state.icds as string[]).length > 0}>
              <HStack justifyContent="flex-end">
                <form.Subscribe selector={(state) => state.values.icds}>
                  {(icds) => {
                    const has_changes = getChanges(icds).added.length > 0;
                    return (
                      <SubmitButton size="md" disabled={!has_changes}>
                        Save & Update
                      </SubmitButton>
                    );
                  }}
                </form.Subscribe>
              </HStack>
            </ShowFieldWhen>
          </Stack>
        </Stack>
      </Container>
    </form.AppForm>
  );
}

function ComboboxItem(props: { item: AppointmentDiagnosisDatasetDataRo }) {
  const { item } = props;
  const combobox = useComboboxContext();

  return (
    <Combobox.Item key={item.id} item={item}>
      <Stack gap="4px" textStyle="sm">
        <HStack>
          <Text>
            <Highlight ignoreCase query={combobox.inputValue} styles={{ bg: "yellow.emphasized", fontWeight: "medium" }}>
              {item.icd_10_code}
            </Highlight>
          </Text>

          <Span>/</Span>

          <Span color="text.2">
            <Highlight ignoreCase query={combobox.inputValue} styles={{ bg: "yellow.emphasized", fontWeight: "medium" }}>
              {item.icd_9_code}
            </Highlight>
          </Span>
        </HStack>

        <Text color="text.3" truncate>
          <Highlight ignoreCase query={combobox.inputValue} styles={{ bg: "yellow.emphasized", fontWeight: "medium" }}>
            {item.description}
          </Highlight>
        </Text>
      </Stack>
      <Combobox.ItemIndicator />
    </Combobox.Item>
  );
}

function DeleteButton(props: { appointment_id: string; data_id?: string; removeFromForm?: () => void }) {
  const { appointment_id, data_id, removeFromForm } = props;
  const { mutateAsync, isPending: deleting } = useMutation(deleteDiagnosisMutationOpts(appointment_id, data_id || ""));

  const handleDelete = async () => {
    if (data_id) {
      const promise = mutateAsync({});
      const result = await tryCatch(promise);

      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "Diagnosis has been deleted successfully",
        });
      }
    }

    // form.removeFieldValue("icds", index);
    removeFromForm?.();
  };

  return (
    <Button
      variant="plain"
      leftIcon={<Icon name="trash" color="inherit" />}
      color="red.500"
      css={{ "--before-bg": "red.500" }}
      onClick={handleDelete}
      loading={deleting}
    >
      Delete
    </Button>
  );
}
