import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Empty, <PERSON><PERSON>, NavigationBlockerProvider, toaster } from "@/components";
import { Accordion, Container, Heading, HStack, Show, Skeleton, SkeletonCircle, Span, Stack, VStack } from "@chakra-ui/react";
import { useParams } from "react-router";
import { FormBuilder } from "../ui/form-builder";
import { useCallback, useMemo, useRef } from "react";
import { useMutation, useNavigationBlocker, usePartialState } from "@/hooks";
import { createTreatmentPlanMutationOpts, deleteTreatmentPlanMutationOpts, getAppointmentTreatmentPlanQueryOpts } from "@/queries";
import { toQueryString, tryCatch } from "@/utils";
import { useQuery } from "@tanstack/react-query";
import {
  AppointmentTreatmentPlanDataRo,
  IGenericAppointmentForm,
  IGenericAppointmentFormCategory,
  IGenericAppointmentFormHeading,
} from "@/interfaces";
import { appt_treatment_plan_forms } from "@/static";

function getRandomStr() {
  return Math.random().toString(36).substring(7);
}

export function AppointmentTreatmentPlan() {
  const params = useParams();

  const appt_id = params.id;
  // const [value, setValue] = useState(["second-item"]);
  const { data, isPending: loading } = useQuery(getAppointmentTreatmentPlanQueryOpts(toQueryString({ appointment_id: appt_id || "" })));
  const treatment_plan_data = useMemo(() => (data?.data || []) as AppointmentTreatmentPlanDataRo[], [data]);

  // const grouped_by_tab = useMemo(() => groupBy(assessment_data, "data_tab"), [assessment_data]);
  // const assessment_tabs = useMemo(() => Object.keys(grouped_by_tab), [grouped_by_tab]);
  const mapped_data = useMemo(() => {
    return treatment_plan_data.map(
      (item) =>
        ({
          id: `plan-${getRandomStr()}`,
          category: item.data_name,
          update_id: item.data_id,
          forms: item.data,
        } as IGenericAppointmentFormCategory & { update_id: string })
    );
  }, [treatment_plan_data]);

  // const loading = false;

  const [{ addedPlans = [] }, set] = usePartialState<{
    addedPlans: (IGenericAppointmentFormCategory & { update_id?: string })[];
  }>({ addedPlans: mapped_data }, [mapped_data]);

  const addPlan = useCallback(() => {
    const new_plan = { ...appt_treatment_plan_forms[0] };
    new_plan.id = `plan-${getRandomStr()}`;
    new_plan.category = `Treatment Plan ${addedPlans.length + 1}`;

    // console.log("New plan", new_plan);
    set({ addedPlans: [new_plan, ...addedPlans] });
  }, [set, addedPlans]);

  // console.log("Appointment Assessment Params", appt_assessment_forms);

  const getTreatmentPlanFormsWithValue = useCallback((plan: IGenericAppointmentFormCategory) => {
    type FormWithSavedValue = IGenericAppointmentFormCategory & { update_id: string };

    // const saved_data = grouped_by_tab[form_category_id]?.[0];
    const category_forms = appt_treatment_plan_forms[0].forms;

    // if (!category) return { id: "", category: "", update_id: "", forms: [] } as FormWithSavedValue;

    const forms_with_value = category_forms.map((item) => {
      // const item_r = item as Exclude<IGenericAppointmentForm, IGenericAppointmentFormHeading>;
      if (item.type === "heading") return item;
      const saved = plan.forms.find((v) => v.label === item.label && v.type === item.type);
      const value = ((saved || item) as Exclude<IGenericAppointmentForm, IGenericAppointmentFormHeading>)?.value || "";

      return {
        ...item,
        value,
      };
    });

    return {
      ...plan,
      forms: forms_with_value,
    } as FormWithSavedValue;
  }, []);

  return (
    <NavigationBlockerProvider>
      <Container maxW="100rem" py="24px">
        <Stack gap="16px">
          <Breadcrumb
            items={[
              { title: "Appointments", to: "/appointments", is_first: true },
              { title: "Appointment details", to: `/appointments/${appt_id}` },
              { title: "Treatment Plan", to: "#" },
            ]}
          />

          <Heading as="h5" fontSize="20px" fontWeight="600">
            Treatment Plan
          </Heading>

          <Stack gap="16px" mt="24px">
            <HStack justifyContent="space-between">
              <Heading as="h6" fontSize="16px" fontWeight="600">
                Plans
              </Heading>

              <Button variant="solid" leftIcon={<Icon name="plus" color="inherit" />} onClick={addPlan}>
                Add Treatment Plan
              </Button>
            </HStack>

            <Show when={addedPlans.length < 1 && !loading}>
              <VStack my="10svh">
                <Empty subtitle="No form added yet" />
              </VStack>
            </Show>

            <Show when={loading}>
              <Accordion.Root collapsible display="flex" flexDir="column" gap="16px">
                {Array.from({ length: 3 }).map((_, index) => (
                  <Accordion.Item
                    p="16px"
                    key={index}
                    value={`${index}-item`}
                    border="1px solid"
                    borderColor="stroke.divider"
                    rounded="8px"
                    css={{
                      "&[data-state='open']": {
                        borderColor: "primary",
                        backgroundColor: "primary.50/20",
                      },
                    }}
                  >
                    <Accordion.ItemTrigger p="0" justifyContent="space-between">
                      <Skeleton variant="shine" loading={loading}>
                        <Span flex="1">Category Name</Span>
                      </Skeleton>

                      <SkeletonCircle variant="shine" loading={loading}>
                        <Accordion.ItemIndicator />
                      </SkeletonCircle>
                    </Accordion.ItemTrigger>
                    <Accordion.ItemContent>
                      <Accordion.ItemBody pb="0">
                        <TreatmentPlanForm appt_id={appt_id || ""} loading />
                      </Accordion.ItemBody>
                    </Accordion.ItemContent>
                  </Accordion.Item>
                ))}
              </Accordion.Root>
            </Show>

            <Show when={addedPlans.length > 0 && !loading}>
              <Accordion.Root
                collapsible
                display="flex"
                flexDir="column"
                gap="16px" /*value={value} onValueChange={(e) => setValue(e.value)}*/
              >
                {addedPlans.map((item, index) => {
                  const problem_field = item.forms.find((v) => v.type !== "heading" && v.label === "Problem");
                  const problem_value = (problem_field as Exclude<IGenericAppointmentForm, IGenericAppointmentFormHeading>)?.value || "";

                  return (
                    <Accordion.Item
                      p="16px"
                      key={index}
                      value={item.id}
                      border="1px solid"
                      borderColor="stroke.divider"
                      rounded="8px"
                      css={{
                        "&[data-state='open']": {
                          borderColor: "primary",
                          backgroundColor: "primary.50/20",
                        },
                      }}
                      viewTransitionName={`accordion-item-${item.id}`}
                    >
                      <Accordion.ItemTrigger p="0" justifyContent="space-between">
                        <Skeleton variant="shine" loading={loading}>
                          <Span flex="1" textTransform="capitalize">
                            {item?.update_id ? problem_value : `Treatment Plan ${addedPlans.length - index}`}
                          </Span>
                        </Skeleton>

                        <SkeletonCircle variant="shine" loading={loading}>
                          <Accordion.ItemIndicator />
                        </SkeletonCircle>
                      </Accordion.ItemTrigger>
                      <Accordion.ItemContent>
                        <Accordion.ItemBody pb="0">
                          <TreatmentPlanForm
                            appt_id={appt_id || ""}
                            form_data={getTreatmentPlanFormsWithValue(item)}
                            // form_data={item}
                            loading={loading}
                            removeFromSelectedItems={(id) => set({ addedPlans: addedPlans.filter((i) => i.id !== id) })}
                          />
                        </Accordion.ItemBody>
                      </Accordion.ItemContent>
                    </Accordion.Item>
                  );
                })}
              </Accordion.Root>
            </Show>
          </Stack>
        </Stack>
      </Container>
    </NavigationBlockerProvider>
  );
}

// const items = [
//   { value: "first-item", title: "First Item", text: "Some value 1..." },
//   { value: "second-item", title: "Second Item", text: "Some value 2..." },
//   { value: "third-item", title: "Third Item", text: "Some value 3..." },
// ];

function TreatmentPlanForm(props: {
  appt_id: string;
  form_data?: IGenericAppointmentFormCategory & { update_id?: string };
  loading?: boolean;
  removeFromSelectedItems?: (id: string) => void;
}) {
  const { appt_id, form_data, loading = true, removeFromSelectedItems } = props;

  const is_dirty = useRef(false);
  const nav_blocker = useNavigationBlocker();

  const { mutateAsync } = useMutation(createTreatmentPlanMutationOpts(appt_id));
  const { mutateAsync: deleteTreatmentPlan, isPending: deleting } = useMutation(
    deleteTreatmentPlanMutationOpts(appt_id, form_data?.update_id || "")
  );

  // console.log("Treatment Plan Form", form_data);

  if (loading) {
    return (
      <HStack justifyContent="space-between">
        <Skeleton variant="shine" h="4px" w="20%" loading={loading} />
        <Skeleton variant="shine" h="4px" w="40%" loading={loading} />
      </HStack>
    );
  }

  return (
    <FormBuilder
      form_name={form_data?.category || ""}
      form_data={form_data?.forms || []}
      form_id={form_data?.id || ""}
      appointment_id={appt_id || ""}
      onChange={(changes) => {
        if (changes.added.length > 0 || changes.removed.length > 0 || changes.modified.length > 0) {
          // console.log("Form Changed", changes);
          is_dirty.current = true;
          nav_blocker.set({ block_navigations: true });
          return;
        }

        is_dirty.current = false;
        nav_blocker.set({ block_navigations: false });
      }}
      onSubmit={async (values) => {
        // console.log("Form submitted", values);
        const problem = values.find((item) => item.label.toLowerCase() === "problem");
        const problem_value = (problem?.value as string) || "";

        const promise = mutateAsync({
          appointment_id: appt_id || "",
          data_name: problem ? problem_value : form_data?.category,
          data_tab: form_data?.id,
          data: values,
          data_id: form_data?.update_id,
        });
        const result = await tryCatch(promise);
        if (result.ok) {
          toaster.success({
            title: "Success",
            description: "Form has been submitted successfully",
          });
        }
      }}
      deleting={deleting}
      onDelete={async () => {
        if (form_data?.update_id) {
          const promise = deleteTreatmentPlan({});
          const result = await tryCatch(promise);
          if (result.ok) {
            toaster.success({
              title: "Success",
              description: `Treatment plan form has been deleted successfully`,
            });
          }
        }

        removeFromSelectedItems?.(form_data?.id || "");
      }}
    />
  );
}
