/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  AppliedListFilters,
  Breadcrumb,
  Button,
  Empty,
  FieldErrorInfo,
  Paginator,
  SearchField,
  SubmitButton,
  toaster,
  useAppForm,
} from "@/components";
import { ButtonGroup, Container, Field, Grid, Heading, HStack, Separator, Show, Stack, Text, VStack } from "@chakra-ui/react";
import { useNavigate, useParams } from "react-router";
import { ProviderCard } from "../ui/provider-card";
import { useCommonList, useListFilter, useMutation, usePostQuery } from "@/hooks";
import { useMemo } from "react";
import { useStore } from "@tanstack/react-form";
import { ListCountRo, ServiceProviderListRo } from "@/interfaces";
import { ReferUserDto, referUserSchema } from "@/schemas";
import { referUserMutationOpts } from "@/queries";
import { tryCatch } from "@/utils";
import { ProviderFilter } from "../ui/provider-filter";

export function ReferUser() {
  const params = useParams();
  const navigate = useNavigate();

  const appt_id = params?.id || "";

  const filters = useListFilter({ page: 1, item_per_page: 10, q: "", amount: "", gender: "", religion: "", language: "" });
  const { filter, setFilter } = filters;

  const { data: offerring_data, isPending: fetching_offerrings } = useCommonList("service-offering");
  const { mutateAsync, isPending: refering } = useMutation(referUserMutationOpts(appt_id));
  const offerrings = useMemo(() => offerring_data?.data || [], [offerring_data]);

  //   console.log("Services", offerrings);

  const form = useAppForm({
    defaultValues: {
      comment: "",
      provider_id: "",
      service_offer_id: "",
    } as ReferUserDto,

    validators: {
      onChange: referUserSchema,
      onSubmit: referUserSchema,
      onMount: referUserSchema,
    },
    async onSubmit({ value }) {
      console.log("Refer user value", value);

      const promise = mutateAsync(value);
      const result = await tryCatch(promise);
      if (result.ok) {
        toaster.success({
          title: "Success",
          description: "User has been referred successfully",
        });
        form.reset();
      }
    },
  });

  const handleSubmit = (e: React.SyntheticEvent) => {
    e.preventDefault();
    form.handleSubmit();
  };

  const service_offer_id = useStore(form.store, (store) => store.values.service_offer_id);

  const filter_data = useMemo(
    () => ({
      //   q: filter?.q,
      //   item_per_page: filter?.item_per_page,
      //   page: filter?.page,
      ...filter,
      user_type: "provider",
      service_offer_id: service_offer_id,
    }),
    [filter, service_offer_id]
  );

  const { data: serv_provider_data, isPending: loading_list } = usePostQuery<ServiceProviderListRo, any>(
    `/members/requests/service-providers`,
    ["service-providers", filter, service_offer_id],
    filter_data,
    {
      enabled: !!service_offer_id,
    }
  );

  const { data: count_data, isPending: loading_count } = usePostQuery<ListCountRo, any>(
    `/members/requests/service-providers`,
    ["service-providers/count", filter, service_offer_id],
    {
      //   q: filter?.q,
      //   item_per_page: filter?.item_per_page,
      //   page: filter?.page,
      ...filter_data,
      component: "count",
    },
    {
      enabled: !!service_offer_id,
    }
  );

  const loading_provider = (loading_list || loading_count) && !!service_offer_id;

  const providers = useMemo(() => serv_provider_data?.data || [], [serv_provider_data]);
  const total = useMemo(() => count_data?.total || 1, [count_data]);
  const is_empty = useMemo(() => !providers || providers.length === 0, [providers]);

  //   console.log("Provider data", { providers, total });

  return (
    <form.AppForm>
      <Container maxW="100rem" py="24px">
        <Stack gap="16px" as="form" onSubmit={handleSubmit}>
          <Breadcrumb
            items={[
              { title: "Appointments", to: "/appointments", is_first: true },
              { title: "Appointment details", to: `/appointments/${appt_id}` },
              { title: "Refer User", to: "#" },
            ]}
          />

          <Heading as="h5" fontSize="20px" fontWeight="600">
            Refer User
          </Heading>

          <Stack gap="16px" border="1px solid" borderColor="stroke.divider" p="16px" rounded="8px">
            <Heading as="h6" fontSize="16px" fontWeight="600">
              Services
            </Heading>

            <form.AppField name="service_offer_id">
              {(field) => (
                <field.Select
                  label="Select a Service"
                  placeholder="Select service"
                  field={field}
                  loading={fetching_offerrings}
                  value={[field.state.value]}
                  onBlur={field.handleBlur}
                  onValueChange={(e) => field.setValue(e.value[0])}
                  items={offerrings.map((item) => ({
                    label: item.name,
                    value: item.service_offer_id,
                    description: item?.description,
                  }))}
                  itemLabelProps={{ textTransform: "capitalize" }}
                  triggerProps={{ textTransform: "capitalize" }}
                />
              )}
            </form.AppField>

            <form.AppField name="comment">
              {(field) => (
                <field.Textarea
                  label="Comments"
                  placeholder="Add comments for the user"
                  minH="194px"
                  value={field.state.value}
                  onChange={(e) => field.setValue(e.target.value)}
                />
              )}
            </form.AppField>

            <Separator />

            <Stack gap="16px">
              <form.AppField name="provider_id">
                {(field) => {
                  const invalid = !!field.state.meta.errors.length && !!service_offer_id;
                  return (
                    <Field.Root invalid={invalid}>
                      <Stack gap="0">
                        <Heading as="h6" fontSize="16px" fontWeight="600">
                          Providers
                        </Heading>
                        <Text fontSize="14px" color="text.2">
                          Select a provider you'd like to refer the user to.
                        </Text>
                      </Stack>
                      <FieldErrorInfo field={field} />
                    </Field.Root>
                  );
                }}
              </form.AppField>

              <HStack justifyContent="space-between">
                <SearchField value={filter?.q} onChange={(value) => setFilter({ q: value })} />

                <HStack>
                  <ProviderFilter filters={filter} onFilterChange={setFilter} loading={loading_provider} />
                </HStack>
              </HStack>

              <AppliedListFilters keyPath="provider" loading={loading_provider} {...filters} />

              <Show when={!loading_provider && is_empty}>
                <VStack my="5vh">
                  <Empty subtitle={!service_offer_id ? "Select a service to get providers" : "No providers found"} />
                </VStack>
              </Show>

              <Show when={loading_provider}>
                <Grid templateColumns="repeat(5, 1fr)" gap="16px">
                  {Array.from({ length: 5 })
                    .fill(0)
                    .map((_, i) => (
                      <ProviderCard
                        key={`provider-${i}`}
                        name="Increase Nexxy"
                        profession="Counsellor"
                        status="active"
                        //   avatar="https://msmtavatarstorage.blob.core.windows.net/profile-picture/670d88fb78b9e634ea0b555c.JPG"
                        avatar=""
                        selected
                        loading
                      />
                    ))}
                </Grid>
              </Show>

              <Show when={!loading_provider && !is_empty}>
                <form.AppField name="provider_id">
                  {(field) => (
                    <Grid templateColumns="repeat(5, 1fr)" gap="16px">
                      {providers.map(({ charge_from, provider_data: prov }, i) => (
                        <ProviderCard
                          key={`provider-${i}`}
                          name={prov.name}
                          profession={prov?.specialty || "--"}
                          status=""
                          //   accountType={prov?.account_type}
                          charge={charge_from}
                          avatar={
                            prov?.avatar ||
                            `https://placehold.co/<EMAIL>?text=${(prov?.name || "Increase Nexxy")
                              .trim()
                              .split(" ")
                              .map((n) => n[0]?.toUpperCase())
                              .join("")}`
                          }
                          selected={prov?.user_id === field.state.value}
                          onClick={() => field.setValue(prov?.user_id || "")}
                          borderColor={field.state.meta.errors.length > 0 ? "red.500" : "stroke.divider"}
                        />
                      ))}
                    </Grid>
                  )}
                </form.AppField>
              </Show>

              <Show when={!is_empty && !loading_provider}>
                <Paginator
                  count={total}
                  defaultPage={1}
                  page={filter?.page}
                  pageSize={filter?.item_per_page || 1}
                  onPageChange={(e) => setFilter({ page: e.page })}
                />
              </Show>
            </Stack>
          </Stack>

          <ButtonGroup py="20px" w="100%" justifyContent="flex-end" gap="16px">
            <Button variant="subtle" disabled={refering} onClick={() => navigate(-1)}>
              Go Back
            </Button>
            <SubmitButton disabled={refering} loading={refering}>
              Refer User
            </SubmitButton>
          </ButtonGroup>
        </Stack>
      </Container>
    </form.AppForm>
  );
}
