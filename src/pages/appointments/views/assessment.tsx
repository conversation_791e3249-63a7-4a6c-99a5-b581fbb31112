/* eslint-disable @typescript-eslint/no-explicit-any */
import { AppErrorBoundary, Breadcrumb, Empty, FieldCard, NavigationBlockerProvider, toaster } from "@/components";
import { appt_assessment_forms } from "@/static";
import {
  Accordion,
  Combobox,
  Container,
  Heading,
  Highlight,
  HStack,
  Portal,
  Show,
  Skeleton,
  SkeletonCircle,
  Span,
  Spinner,
  Stack,
  Text,
  useComboboxContext,
  useFilter,
  useListCollection,
  VStack,
} from "@chakra-ui/react";
import { useParams } from "react-router";
import { FormBuilder } from "../ui/form-builder";
import { useCallback, useMemo, useRef, useState } from "react";
import { useMutation, useNavigationBlocker, usePartialState } from "@/hooks";
import { createAssessmentMutationOpts, deleteAssessmentMutationOpts, getAppointmentAssessmentQueryOpts } from "@/queries";
import { toQueryString, tryCatch } from "@/utils";
import { useQuery } from "@tanstack/react-query";
import {
  AppointmentAssessmentDataRo,
  IGenericAppointmentForm,
  IGenericAppointmentFormCategory,
  IGenericAppointmentFormHeading,
} from "@/interfaces";
import groupBy from "lodash.groupby";
import { SubstanceAbuseForm } from "../ui/substance-abuse-form";

export function AppointmentAssessment() {
  const params = useParams();

  const appt_id = params.id;
  const [accordion_val, setAccordionVal] = useState("");

  const { data, isPending: loading } = useQuery(getAppointmentAssessmentQueryOpts(toQueryString({ appointment_id: appt_id || "" })));
  const assessment_data = useMemo(() => (data?.data || []) as AppointmentAssessmentDataRo[], [data]);

  const grouped_by_tab = useMemo(() => groupBy(assessment_data, "data_tab"), [assessment_data]);
  const assessment_tabs = useMemo(() => Object.keys(grouped_by_tab), [grouped_by_tab]);

  const [{ searchValue = "", selectedItems = [] }, set] = usePartialState<{ searchValue: string; selectedItems: string[] }>(
    { searchValue: "", selectedItems: assessment_tabs },
    [assessment_tabs]
  );

  // console.log("Assessments", grouped_by_tab);

  const has_new_items = useMemo(() => selectedItems.some((item) => !assessment_tabs.includes(item)), [selectedItems, assessment_tabs]);

  const filtered_search_items = useMemo(
    () =>
      appt_assessment_forms.filter((item) =>
        [item.category, item.id]
          .map((s) => s.toLowerCase())
          .join(" ")
          .includes(searchValue.toLowerCase())
      ),
    [searchValue]
  );

  const filtered_selected_items = useMemo(() => appt_assessment_forms.filter((item) => selectedItems.includes(item.id)), [selectedItems]);

  const form_categories = useMemo(
    () => filtered_search_items.filter((item) => !selectedItems.includes(item.id)),
    [filtered_search_items, selectedItems]
  );

  // console.log("Appointment Assessment Params", appt_assessment_forms);

  const getAssessmentFormsWithValue = useCallback(
    (form_category_id: string) => {
      type FormWithSavedValue = IGenericAppointmentFormCategory & { update_id: string };

      if (form_category_id === "substance_abuse") {
        return {
          ...appt_assessment_forms.find((item) => item.id === form_category_id),
          id: "substance_abuse",
          update_id: grouped_by_tab[form_category_id]?.[0]?.data_id || "",
          forms: grouped_by_tab[form_category_id]?.[0]?.data || [],
        };
      }

      const category = appt_assessment_forms.find((item) => item.id === form_category_id);
      const saved_data = grouped_by_tab[form_category_id]?.[0];
      if (!category) return { id: "", category: "", update_id: "", forms: [] } as FormWithSavedValue;

      const forms_with_value = (category.forms as IGenericAppointmentForm[]).map((item) => {
        const item_r = item as Exclude<IGenericAppointmentForm, IGenericAppointmentFormHeading>;
        const saved = saved_data?.data?.find((v) => v.label === item.label && v.type === item.type);
        return {
          ...item,
          value: saved?.value || item_r?.value,
        };
      });

      return {
        ...category,
        update_id: saved_data?.data_id,
        forms: forms_with_value,
      } as FormWithSavedValue;
    },
    [grouped_by_tab]
  );

  const { contains } = useFilter({ sensitivity: "base", ignorePunctuation: true });

  const { collection, filter } = useListCollection({
    limit: 40,
    initialItems: form_categories,

    filter: contains,

    itemToValue(item) {
      return item.id;
    },
    itemToString(item) {
      return `${item.id} ${item.category}`.toLowerCase();
    },
  });

  // const debounced = useDebouncedCallback((value) => {
  //   filter(value);
  // }, 500);

  const handleInputChange = (details: Combobox.InputValueChangeDetails) => {
    const newValue = details.inputValue;
    set({ searchValue: newValue });
    filter(newValue);
  };

  return (
    <AppErrorBoundary>
      {/* This nav-blocker provider really isn't necessary but it's here for patching an issue with the useContext hook */}
      {/* TODO: Find a better way to handle this */}
      <NavigationBlockerProvider>
        <Container maxW="100rem" py="24px">
          <Stack gap="16px">
            <Breadcrumb
              items={[
                { title: "Appointments", to: "/appointments", is_first: true },
                { title: "Appointment details", to: `/appointments/${appt_id}` },
                { title: "Assessment", to: "#" },
              ]}
            />

            <Heading as="h5" fontSize="20px" fontWeight="600">
              Assessment
            </Heading>

            <FieldCard>
              {/* <SearchField
            placeholder="Search assessment..."
            w="100%"
            maxW="unset"
            startElement={null}
            endElement={<Icon name="search" color="text.3" />}
            value={searchValue}
            onChange={(value) => set({ searchValue: value })}
          /> */}

              <Combobox.Root
                multiple
                closeOnSelect
                openOnClick
                width="100%"
                // value={selectedItems}
                inputValue={searchValue}
                collection={collection}
                onValueChange={(e) => {
                  const latest = e.value[e.items.length - 1];
                  set({ selectedItems: [latest, ...selectedItems], searchValue: "" });
                  // reset();
                }}
                onInputValueChange={handleInputChange}
                // openOnChange={(e) => e.inputValue.length >= 2}
              >
                <Combobox.Control>
                  <Combobox.Input
                    placeholder="Type to search assessment..."
                    rounded="4px"
                    h="48px"
                    bg="input"
                    borderColor="transparent"
                    focusRingColor="primary"
                    focusRingWidth="2px"
                    _focus={{ bg: "primary.50" }}
                  />
                  <Combobox.IndicatorGroup>
                    {!loading && <Combobox.Trigger />}
                    {loading && <Spinner size="xs" color="primary" />}
                  </Combobox.IndicatorGroup>
                </Combobox.Control>

                <Portal>
                  <Combobox.Positioner>
                    <Combobox.Content>
                      <Combobox.Empty>No form found</Combobox.Empty>
                      <Combobox.ItemGroup>
                        {collection.items
                          .filter((item) => !selectedItems.includes(item.id))
                          .map((item) => (
                            <ComboboxItem item={item} />
                          ))}
                      </Combobox.ItemGroup>
                    </Combobox.Content>
                  </Combobox.Positioner>
                </Portal>
              </Combobox.Root>

              {/* <Wrap gap="2">
            {form_categories.map((item) => (
              // <Badge rounded="full" key={item.id}>
              //   {item.category}
              // </Badge>

              <Skeleton variant="shine" rounded="full" loading={loading} viewTransitionName={`assessment-form-${item.id}`}>
                <Button
                  key={item.id}
                  variant="outline"
                  borderColor="stroke.divider"
                  color="text.3"
                  leftIcon={<Icon name="plus" color="inherit" />}
                  _hover={{ color: "white", bg: "black" }}
                  onClick={() => set({ selectedItems: [item.id, ...selectedItems] })}
                  css={{ "--before-bg": "transparent" }}
                >
                  {item.category}
                </Button>
              </Skeleton>
            ))}
          </Wrap> */}
            </FieldCard>

            <Stack gap="16px" mt="24px">
              <Heading as="h6" fontSize="16px" fontWeight="600">
                Forms Used
              </Heading>

              <Show when={filtered_selected_items.length < 1 && !loading}>
                <VStack my="10svh">
                  <Empty subtitle="No form added yet" />
                </VStack>
              </Show>

              <Show when={loading}>
                <Accordion.Root collapsible display="flex" flexDir="column" gap="16px">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Accordion.Item
                      p="16px"
                      key={index}
                      value={`${index}-item`}
                      border="1px solid"
                      borderColor="stroke.divider"
                      rounded="8px"
                      css={{
                        "&[data-state='open']": {
                          borderColor: "primary",
                          backgroundColor: "primary.50/20",
                        },
                      }}
                    >
                      <Accordion.ItemTrigger p="0" justifyContent="space-between">
                        <Skeleton variant="shine" loading={loading}>
                          <Span flex="1">Category Name</Span>
                        </Skeleton>

                        <SkeletonCircle variant="shine" loading={loading}>
                          <Accordion.ItemIndicator />
                        </SkeletonCircle>
                      </Accordion.ItemTrigger>
                      <Accordion.ItemContent>
                        <Accordion.ItemBody pb="0">
                          <AssessmentForm appt_id={appt_id || ""} loading />
                        </Accordion.ItemBody>
                      </Accordion.ItemContent>
                    </Accordion.Item>
                  ))}
                </Accordion.Root>
              </Show>

              <Show when={filtered_selected_items.length > 0 && !loading}>
                <Accordion.Root
                  collapsible
                  display="flex"
                  flexDir="column"
                  gap="16px"
                  value={[accordion_val]}
                  onValueChange={(e) => setAccordionVal(e.value[0])}
                >
                  {filtered_selected_items.map((item, index) => (
                    <Accordion.Item
                      p="16px"
                      key={index}
                      value={item.id}
                      border="1px solid"
                      borderColor="stroke.divider"
                      rounded="8px"
                      css={{
                        "&[data-state='open']": {
                          borderColor: "primary",
                          backgroundColor: "primary.50/20",
                        },
                      }}
                      viewTransitionName={`accordion-item-${item.id}`}
                    >
                      <Accordion.ItemTrigger p="0" justifyContent="space-between">
                        <Skeleton variant="shine" loading={loading}>
                          <Span flex="1">{item.category}</Span>
                        </Skeleton>

                        <SkeletonCircle variant="shine" loading={loading}>
                          <Accordion.ItemIndicator />
                        </SkeletonCircle>
                      </Accordion.ItemTrigger>
                      <NavigationBlockerProvider>
                        <Accordion.ItemContent>
                          <Accordion.ItemBody pb="0">
                            <AssessmentForm
                              appt_id={appt_id || ""}
                              form_data={getAssessmentFormsWithValue(item.id) as any}
                              loading={loading}
                              has_new_items={has_new_items}
                              accordionCloseFn={() => setAccordionVal("")}
                              removeFromSelectedItems={(id) => set({ selectedItems: selectedItems.filter((i) => i !== id) })}
                            />
                          </Accordion.ItemBody>
                        </Accordion.ItemContent>
                      </NavigationBlockerProvider>
                    </Accordion.Item>
                  ))}
                </Accordion.Root>
              </Show>
            </Stack>
          </Stack>
        </Container>
      </NavigationBlockerProvider>
    </AppErrorBoundary>
  );
}

// const items = [
//   { value: "first-item", title: "First Item", text: "Some value 1..." },
//   { value: "second-item", title: "Second Item", text: "Some value 2..." },
//   { value: "third-item", title: "Third Item", text: "Some value 3..." },
// ];

function AssessmentForm(props: {
  appt_id: string;
  form_data?: IGenericAppointmentFormCategory & { update_id?: string };
  loading?: boolean;
  has_new_items?: boolean;
  accordionCloseFn?: VoidFunction;
  removeFromSelectedItems?: (id: string) => void;
}) {
  const { appt_id, form_data, accordionCloseFn, loading = true, removeFromSelectedItems } = props;

  const is_dirty = useRef(false);
  const nav_blocker = useNavigationBlocker();

  // const nav_blocker = {
  //   set: ({ block_navigations }: { block_navigations: boolean }) => {
  //     console.log("Blocker", block_navigations);
  //   },
  // };

  const { mutateAsync } = useMutation(createAssessmentMutationOpts(appt_id));
  const { mutateAsync: deleteAssessment, isPending: deleting } = useMutation(
    deleteAssessmentMutationOpts(appt_id, form_data?.update_id || "")
  );

  // console.log("Assessment Form", form_data);

  // const is_array_of_array = form_data?.forms.(Array.isArray);

  if (loading) {
    return (
      <HStack justifyContent="space-between">
        <Skeleton variant="shine" h="4px" w="20%" loading={loading} />
        <Skeleton variant="shine" h="4px" w="40%" loading={loading} />
      </HStack>
    );
  }

  // if (!Array.isArray(form_data?.forms)) {
  //   return (
  //     <VStack gap="16px">
  //       {form_data?.forms.map((item, index) => (
  //         <FormBuilder
  //           key={index}
  //           form_name={form_data?.category || ""}
  //           form_data={(item as IGenericAppointmentForm[]) || []}
  //           form_id={form_data?.id || ""}
  //           appointment_id={appt_id || ""}
  //           onSubmit={async (values) => {
  //             // console.log("Form submitted", values);

  //             const promise = mutateAsync({
  //               appointment_id: appt_id || "",
  //               data_name: form_data?.category,
  //               data_tab: form_data?.id,
  //               data: values,
  //               data_id: form_data?.update_id,
  //             });

  //             const result = await tryCatch(promise);
  //             if (result.ok) {
  //               toaster.success({
  //                 title: "Success",
  //                 description: "Form has been submitted successfully",
  //               });
  //             }
  //           }}
  //           deleting={deleting}
  //           onDelete={async () => {
  //             if (form_data?.update_id) {
  //               const promise = deleteAssessment({});
  //               const result = await tryCatch(promise);
  //               if (result.ok) {
  //                 toaster.success({
  //                   title: "Success",
  //                   description: `${form_data?.category || "Assessment"} form has been deleted successfully`,
  //                 });
  //               }
  //             }

  //             removeFromSelectedItems?.(form_data?.id || "");
  //           }}
  //         />
  //       ))}
  //     </VStack>
  //   );
  // }

  if (form_data?.id === "substance_abuse") {
    return <SubstanceAbuseForm {...props} />;
  }

  return (
    <FormBuilder
      form_name={form_data?.category || ""}
      form_data={(form_data?.forms as IGenericAppointmentForm[]) || []}
      form_id={form_data?.id || ""}
      appointment_id={appt_id || ""}
      onChange={(changes) => {
        if (changes.added.length > 0 || changes.removed.length > 0 || changes.modified.length > 0) {
          // console.log("Form Changed", changes);
          is_dirty.current = true;
          nav_blocker.set({ block_navigations: true });
          return;
        }

        is_dirty.current = false;
        nav_blocker.set({ block_navigations: false });
      }}
      onSubmit={async (values) => {
        // console.log("Form submitted", values);

        const promise = mutateAsync({
          appointment_id: appt_id || "",
          data_name: form_data?.category,
          data_tab: form_data?.id,
          data: values,
          data_id: form_data?.update_id,
        });

        const result = await tryCatch(promise);
        if (result.ok) {
          accordionCloseFn?.();
          nav_blocker.set({ block_navigations: false });
          toaster.success({
            title: "Success",
            description: "Form has been submitted successfully",
          });
        }
      }}
      deleting={deleting}
      onDelete={async () => {
        if (form_data?.update_id) {
          const promise = deleteAssessment({});
          const result = await tryCatch(promise);
          if (result.ok) {
            toaster.success({
              title: "Success",
              description: `${form_data?.category || "Assessment"} form has been deleted successfully`,
            });
          }
        }

        removeFromSelectedItems?.(form_data?.id || "");
      }}
    />
  );
}

function ComboboxItem(props: { item: IGenericAppointmentFormCategory }) {
  const { item } = props;
  const combobox = useComboboxContext();

  return (
    <Combobox.Item key={item.id} item={item}>
      <Stack gap="4px" textStyle="sm">
        <Text>
          <Highlight ignoreCase query={combobox.inputValue} styles={{ bg: "yellow.emphasized", fontWeight: "medium" }}>
            {item.category}
          </Highlight>
        </Text>
      </Stack>
      <Combobox.ItemIndicator />
    </Combobox.Item>
  );
}
