import { Breadcrumb, Empty } from "@/components";
import { getMedicationMedicalRecordsQueryOpts } from "@/queries";
import { toQueryString } from "@/utils";
import { Accordion, Container, Heading, HStack, Show, Skeleton, SkeletonCircle, Span, Stack, VStack } from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { useMemo } from "react";
import { useParams } from "react-router";
import { FormViewer } from "../ui/form-viewer";

export function MedicationPrescriptionMedicalRecords() {
  const params = useParams();
  const appt_id = params?.id || "";

  const { data, isPending: loading } = useQuery(getMedicationMedicalRecordsQueryOpts(toQueryString({ appointment_id: appt_id || "" })));

  const mrs = useMemo(() => data?.data || [], [data]);
  const filtered_items = useMemo(() => mrs.filter((item) => item.data.length > 0), [mrs]);

  //   console.log("Medical Record Tools", data);

  return (
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Breadcrumb
          items={[
            { title: "Appointments", to: "/appointments", is_first: true },
            { title: "Appointment Details", to: `/appointments/${appt_id}` },
            { title: "Medical Records", to: `/appointments/${appt_id}/medical-records` },
            { title: "Medication & Prescription", to: "#" },
          ]}
        />

        <Heading as="h5" fontSize="20px" fontWeight="600">
          Medication & Prescription
        </Heading>

        <Stack gap="16px" mt="24px">
          <Heading as="h6" fontSize="16px" fontWeight="600">
            Forms Used
          </Heading>

          <Show when={filtered_items.length < 1 && !loading}>
            <VStack my="10svh">
              <Empty subtitle="No form added yet" />
            </VStack>
          </Show>

          <Show when={loading}>
            <Accordion.Root collapsible display="flex" flexDir="column" gap="16px">
              {Array.from({ length: 3 }).map((_, index) => (
                <Accordion.Item
                  p="16px"
                  key={index}
                  value={`${index}-item`}
                  border="1px solid"
                  borderColor="stroke.divider"
                  rounded="8px"
                  css={{
                    "&[data-state='open']": {
                      borderColor: "primary",
                      backgroundColor: "primary.50/20",
                    },
                  }}
                >
                  <Accordion.ItemTrigger p="0" justifyContent="space-between">
                    <Skeleton variant="shine" loading={loading}>
                      <Span flex="1">Category Name</Span>
                    </Skeleton>

                    <SkeletonCircle variant="shine" loading={loading}>
                      <Accordion.ItemIndicator />
                    </SkeletonCircle>
                  </Accordion.ItemTrigger>
                  <Accordion.ItemContent>
                    <Accordion.ItemBody pb="0">{/* <AssessmentForm appt_id={appt_id || ""} loading /> */}</Accordion.ItemBody>
                  </Accordion.ItemContent>
                </Accordion.Item>
              ))}
            </Accordion.Root>
          </Show>

          <Show when={filtered_items.length > 0 && !loading}>
            <Accordion.Root
              collapsible
              display="flex"
              flexDir="column"
              gap="16px" /*value={value} onValueChange={(e) => setValue(e.value)}*/
            >
              {filtered_items.map((item, index) => (
                <Accordion.Item
                  p="16px"
                  key={index}
                  value={`item-${index}`}
                  border="1px solid"
                  borderColor="stroke.divider"
                  rounded="8px"
                  css={{
                    "&[data-state='open']": {
                      borderColor: "primary",
                      backgroundColor: "primary.50/20",
                    },
                  }}
                  viewTransitionName={`accordion-item-${index}`}
                >
                  <Accordion.ItemTrigger p="0" justifyContent="space-between">
                    <HStack w="100%" justifyContent="space-between">
                      <Stack gap="0px">
                        <Skeleton w="fit-content" variant="shine" loading={loading}>
                          <Span flex="1" fontSize="10px" color="text.3">
                            PROVIDER
                          </Span>
                        </Skeleton>
                        <HStack>
                          <Skeleton w="fit-content" variant="shine" loading={loading}>
                            <Span flex="1">{item.provider_name}</Span>
                          </Skeleton>
                          <Skeleton w="fit-content" variant="shine" loading={loading}>
                            <Span flex="1">/</Span>
                          </Skeleton>

                          <Skeleton w="fit-content" variant="shine" loading={loading}>
                            <Span flex="1" textTransform="capitalize" color="text.2">
                              {item.provider_specialty}
                            </Span>
                          </Skeleton>
                        </HStack>
                      </Stack>

                      <Skeleton w="fit-content" variant="shine" loading={loading}>
                        <Span flex="1">{format(new Date(item.createdAt), "do MMM, yyyy")}</Span>
                      </Skeleton>
                    </HStack>

                    <SkeletonCircle variant="shine" loading={loading}>
                      <Accordion.ItemIndicator />
                    </SkeletonCircle>
                  </Accordion.ItemTrigger>
                  <Accordion.ItemContent>
                    <Accordion.ItemBody pb="0">
                      <FormViewer record_type="medication" data={item.data} />
                    </Accordion.ItemBody>
                  </Accordion.ItemContent>
                </Accordion.Item>
              ))}
            </Accordion.Root>
          </Show>
        </Stack>
      </Stack>
    </Container>
  );
}
