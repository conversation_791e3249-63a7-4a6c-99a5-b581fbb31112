import { AppliedListFilters, <PERSON>con, <PERSON>Field } from "@/components";
import { Contain<PERSON>, <PERSON><PERSON>, <PERSON>S<PERSON>ck, IconButton, Stack } from "@chakra-ui/react";
import { TableFilter } from "./ui/table-filter";
import { useLoaderData } from "react-router";
import { useQuery } from "@tanstack/react-query";
import { appointmentListCountQueryOpts, appointmentsQueryOpts } from "@/queries";
import { useListFilter } from "@/hooks";
import { toQueryString } from "@/utils";
import { AppointmentList } from "./ui/appointment-list";
import { AppointmentLoaderDataType } from "./loader";

// TODO: Work on the table search input, upcoming appointment cancel, and reschedule feature
export function AppointmentIndex() {
  const [initial_list, , initial_count] = useLoaderData() as AppointmentLoaderDataType;
  // const user = useAuthStore((store) => store.user);

  const filters = useListFilter({ page: 1, item_per_page: 10, q: "" });
  const { filter, setFilter } = filters;

  const { data: list, isFetching, isPending } = useQuery({ ...appointmentsQueryOpts(toQueryString(filter)), initialData: initial_list });
  const { data: count } = useQuery({ ...appointmentListCountQueryOpts(toQueryString(filter)), initialData: initial_count });

  // const { data: upcoming } = useQuery({ ...upcomingAppointmentQueryOpts(), initialData: initial_upcoming });

  // console.log("Appointment Page loads", { list, user });
  // console.log("Upcoming appointment", upcoming);

  // console.log("appointment count", count);

  const handlePageChange = (page: number) => {
    setFilter({ page });
  };

  return (
    // <Grid templateColumns={{ sm: "1fr", md: ".7fr .3fr" }} gap="24px" viewTimelineName="appointment-card">
    // <Grid templateColumns="1fr" gap="24px" viewTimelineName="appointment-card">
    <Container maxW="100rem" py="24px">
      <Stack gap="16px">
        <Heading as="h5" fontSize="20px" fontWeight="600">
          Appointments
        </Heading>

        <HStack justifyContent="space-between">
          <SearchField placeholder="Search table" value={filter?.q} onChange={(value) => setFilter({ q: value })} />

          <HStack>
            <IconButton
              variant="plain"
              aria-label="Download appointments"
              size="md"
              w="40px"
              h="32px"
              _hover={{
                "& :where(svg)": {
                  color: "white !important",
                },
              }}
              display="none"
            >
              <Icon name="download" color="black" />
            </IconButton>

            <TableFilter filters={filter} onFilterChange={setFilter} />
          </HStack>
        </HStack>

        <AppliedListFilters keyPath="appointment" loading={isFetching && isPending} {...filters} />

        <AppointmentList
          filter={filter}
          onPageChange={handlePageChange}
          loading={isFetching && isPending}
          list={{ ...list, ...count?.data }}
          emptyListStackProps={{ my: "25.5svh" }}
        />

        {/* <UpcomingAppointment display="none" pos="sticky" top="60px" rating={4.5} appointment={upcoming?.data?.[0]} />
    </Grid> */}
      </Stack>
    </Container>
  );
}
