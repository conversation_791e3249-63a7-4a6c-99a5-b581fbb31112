import { QueryClient } from "@tanstack/react-query";
import { LoaderFunctionArgs } from "react-router";
import { appointmentListCountQueryOpts, appointmentsQueryOpts, upcomingAppointmentQueryOpts } from "@/queries";
import { ensureQueryData } from "@/libs";

export function appointmentsLoader(query_client: QueryClient) {
  return async ({ request }: LoaderFunctionArgs) => {
    const fetch = ensureQueryData(query_client);
    const search = new URL(request.url).search;
    const params = new URLSearchParams(search);
    if (!params.has("item_per_page")) params.set("item_per_page", "10");

    return await Promise.all([
      fetch(appointmentsQueryOpts(params.toString())),
      fetch(upcomingAppointmentQueryOpts()),
      fetch(appointmentListCountQueryOpts(params.toString())),
    ]);
  };
}

export type AppointmentLoaderDataType = Awaited<ReturnType<ReturnType<typeof appointmentsLoader>>>;
