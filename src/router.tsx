import { createBrowser<PERSON>outer, RouterProvider } from "react-router";
import { ProtectedRoute } from "@/components";
import {
  AppointmentAssessment,
  AppointmentBookingQuestions,
  AppointmentDetails,
  AppointmentDiagnosis,
  AppointmentIndex,
  AppointmentIndexSkeleton,
  AppointmentMedicalPrescription,
  AppointmentMedicalRecords,
  AppointmentProgressNote,
  appointmentsLoader,
  AppointmentTools,
  AppointmentTreatmentPlan,
  AssessmentMedicalRecords,
  CompleteProfileIndex,
  DashboardIndex,
  DiagnosisMedicalRecords,
  MedicationPrescriptionMedicalRecords,
  notificationIndexLoader,
  NotificationsIndex,
  NotificationsIndexSkeleton,
  PartnerAppointments,
  PartnerDetails,
  PartnerEarnings,
  partnerIndexLoader,
  PartnersIndex,
  ProfileIndex,
  ProfileRoutes,
  ProgressNoteMedicalRecords,
  ReferUser,
  ResetPassword,
  Root,
  SetPassword,
  SetupAvailability,
  SetupExclusiveServices,
  SetupIndex,
  SetupServiceRates,
  Signin,
  Signup,
  ToolsMedicalRecords,
  TreatmentPlanMedicalRecords,
  VerifyEmail,
  WalletIndex,
  walletIndexLoader,
  WalletIndexSkeleton,
} from "@/pages";
import { useQueryClient } from "@tanstack/react-query";

function usePageRouter() {
  const qc = useQueryClient();

  const router = createBrowserRouter([
    {
      path: "/",
      element: (
        <ProtectedRoute>
          <Root />
        </ProtectedRoute>
      ),
      // ErrorBoundary: ErrorPage,
      // children: authenticated_route_children,
      children: [
        {
          path: "/",
          element: <DashboardIndex />,
        },

        /// APPOINTMENT ROUTES
        {
          path: "/appointments",
          element: <AppointmentIndex />,
          HydrateFallback: AppointmentIndexSkeleton,
          loader: appointmentsLoader(qc),
        },
        {
          path: "/appointments/:id",
          element: <AppointmentDetails />,
        },
        {
          path: "/appointments/:id/questionnaires",
          element: <AppointmentBookingQuestions />,
        },
        {
          path: "/appointments/:id/assessment",
          element: <AppointmentAssessment />,
        },
        {
          path: "/appointments/:id/diagnosis",
          element: <AppointmentDiagnosis />,
        },
        {
          path: "/appointments/:id/treatment-plan",
          element: <AppointmentTreatmentPlan />,
        },
        {
          path: "/appointments/:id/tools",
          element: <AppointmentTools />,
        },
        {
          path: "/appointments/:id/progress-note",
          element: <AppointmentProgressNote />,
        },
        {
          path: "/appointments/:id/medical-prescription",
          element: <AppointmentMedicalPrescription />,
        },
        {
          path: "/appointments/:id/medical-records",
          element: <AppointmentMedicalRecords />,
        },
        {
          path: "/appointments/:id/medical-records/assessment",
          element: <AssessmentMedicalRecords />,
        },
        {
          path: "/appointments/:id/medical-records/tools",
          element: <ToolsMedicalRecords />,
        },
        {
          path: "/appointments/:id/medical-records/diagnosis",
          element: <DiagnosisMedicalRecords />,
        },
        {
          path: "/appointments/:id/medical-records/treatment-plan",
          element: <TreatmentPlanMedicalRecords />,
        },
        {
          path: "/appointments/:id/medical-records/medication",
          element: <MedicationPrescriptionMedicalRecords />,
        },
        {
          path: "/appointments/:id/medical-records/progress-note",
          element: <ProgressNoteMedicalRecords />,
        },
        {
          path: "/appointments/:id/refer",
          element: <ReferUser />,
        },

        /// PARTNERS ROUTES
        {
          path: "/partners",
          element: <PartnersIndex />,
          loader: partnerIndexLoader(qc),
          hydrateFallbackElement: <>Loading...</>,
        },
        { path: "/partners/:id", element: <PartnerDetails /> },
        { path: "/partners/:id/appointments", element: <PartnerAppointments /> },
        { path: "/partners/:id/earnings", element: <PartnerEarnings /> },

        /// WALLET ROUTES
        {
          path: "/wallet",
          HydrateFallback: WalletIndexSkeleton,
          loader: walletIndexLoader(qc),
          element: <WalletIndex />,
        },

        /// NOTIFICATION ROUTES
        {
          path: "/notifications",
          element: <NotificationsIndex />,
          HydrateFallback: NotificationsIndexSkeleton,
          loader: notificationIndexLoader(qc),
        },

        /// SETUP ROUTES

        {
          path: "/setup",
          element: <SetupIndex />,
        },
        {
          path: "/setup/service-rates",
          element: <SetupServiceRates />,
        },
        {
          path: "/setup/exclusive-services",
          element: <SetupExclusiveServices />,
        },
        {
          path: "/setup/availability",
          element: <SetupAvailability />,
        },

        /// PROFILE ROUTES
        {
          path: "/profile",
          element: <ProfileIndex />,
          children: ProfileRoutes,
        },
        {
          path: "/profile/complete-profile",
          element: <CompleteProfileIndex />,
        },
      ],
    },

    /// Onboarding routes
    { path: "/signin", element: <Signin /> },
    { path: "/signup", element: <Signup /> },
    { path: "/set-password", element: <SetPassword /> },
    { path: "/reset-password", element: <ResetPassword /> },
    { path: "/verify-email", element: <VerifyEmail /> },
  ]);

  return router;
}

export function PageRoutes() {
  const router = usePageRouter();
  return <RouterProvider router={router} />;
}
