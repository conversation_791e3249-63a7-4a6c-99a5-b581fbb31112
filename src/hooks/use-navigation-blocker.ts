/**
 * A custom hook that provides access to the NavigationBlocker context.
 * This hook allows components to interact with the navigation blocking functionality.
 *
 * @throws {Error} If used outside of a NavigationBlockerProvider.
 * @returns {IContext} The NavigationBlocker context object containing state and setter function.
 */

import { createContext, useContext } from "react";
import usePartialState from "./use-partial-state";

export interface NavigationBlockerProps {
  block_navigations?: boolean;
  block_reloads?: boolean;
  onConfirm?: () => Promise<void>;
}

export interface INavigationBlockerContext {
  state: ReturnType<typeof usePartialState<NavigationBlockerProps>>[0];
  set: ReturnType<typeof usePartialState<NavigationBlockerProps>>[1];
}

export const NavigationBlockerContext = createContext<INavigationBlockerContext | null>(null);

export function useNavigationBlocker() {
  const ctx = useContext(NavigationBlockerContext);
  console.log("Navigation Blocker Context", ctx);
  if (!ctx) throw new Error("useNavigationBlocker should be used within a NavigationBlockerProvider");
  return ctx;
}
