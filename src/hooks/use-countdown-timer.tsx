import { useState, useEffect, useRef, useMemo } from "react";

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  total: number;
}

interface ReturnValues {
  timeLeft: TimeLeft;
  asDate: Date;
  asSeconds: number;
  isRunning: boolean;
}

interface UseCountdownTimerOptions {
  onFinish?: () => void;
}

export function useCountdownTimer(
  targetDate: Date,
  opts?: UseCountdownTimerOptions
): ReturnValues {
  const { onFinish } = opts || {};
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft(targetDate));
  const intervalRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const asDate = useMemo(() => {
    const { hours, minutes, seconds } = timeLeft;
    const date = new Date();
    date.setHours(hours, minutes, seconds);
    return date;
  }, [timeLeft]);

  const asSeconds = useMemo(() => {
    const { hours, minutes, seconds } = timeLeft;
    return toSeconds({ hrs: hours, min: minutes, sec: seconds });
  }, [timeLeft]);

  const isRunning = useMemo(() => timeLeft.total > 0, [timeLeft.total]);

  useEffect(() => {
    const tick = () => {
      const newTimeLeft = calculateTimeLeft(targetDate);
      setTimeLeft(newTimeLeft);

      if (newTimeLeft.total <= 0) {
        onFinish?.();
        clearInterval(intervalRef.current);
      }
    };

    const id = setInterval(tick, 1000);
    intervalRef.current = id;

    return () => {
      clearInterval(intervalRef.current);
    };
  }, [targetDate, onFinish]);

  return { timeLeft, asDate, asSeconds, isRunning };
}

function calculateTimeLeft(targetDate: Date): TimeLeft {
  const difference = targetDate.getTime() - Date.now();
  const days = Math.floor(difference / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  );
  const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((difference % (1000 * 60)) / 1000);

  return {
    days,
    hours,
    minutes,
    seconds,
    total: difference,
  };
}

function toSeconds(data: { hrs: number; min: number; sec: number }) {
  // Convert the date to seconds
  // const hrs = date.getHours() * 60 * 60;
  // const min = date.getMinutes() * 60;
  // const sec = date.getSeconds();
  const hrs = data.hrs * 60 * 60;
  const min = data.min * 60;
  const sec = data.sec;
  const totalSeconds = hrs + min + sec;
  return totalSeconds;
}
