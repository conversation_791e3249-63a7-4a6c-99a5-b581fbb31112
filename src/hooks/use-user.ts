import { UserRo } from "@/interfaces";
import { getUserProfileQueryOpts } from "@/queries";

import { useAuthStore } from "@/stores";
import { useQuery, UseQueryOptions } from "@tanstack/react-query";

export function useUser(options?: Omit<UseQueryOptions, "queryKey" | "queryFn">) {
  const user = useAuthStore((store) => store.user);

  const { data, ...rest } = useQuery({
    ...getUserProfileQueryOpts(),
    ...options,
    placeholderData: { data: user, status: "success" },
  });

  return { data: data as UserRo, ...rest };
}
