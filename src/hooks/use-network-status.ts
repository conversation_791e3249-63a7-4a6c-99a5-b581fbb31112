/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from "react";
import { upsertToast } from "@/components/ui/toaster";
import { animate } from "motion";

type NetworkStatus = {
  online: boolean;
  lastChanged: Date | null;
  reconnecting: boolean;
};

let toastId: string | undefined;

export function useNetworkStatus() {
  const [status, setStatus] = useState<NetworkStatus>({
    online: navigator.onLine,
    lastChanged: null,
    reconnecting: false,
  });

  useEffect(() => {
    const handleOnline = () => {
      setStatus((prev) => {
        if (!prev.online) {
          // Show reconnected toast with animation
          toastId = upsertToast({
            id: toastId,
            type: "success",
            title: "Back online",
            description: "Your connection has been restored",
            meta: { closable: true },
          });
        }

        return {
          online: true,
          lastChanged: new Date(),
          reconnecting: false,
        };
      });
    };

    const handleOffline = () => {
      setStatus((prev) => {
        if (prev.online) {
          // Show offline toast with animation
          toastId = upsertToast({
            id: toastId,
            type: "error",
            title: "You're offline",
            description: "Please check your connection",
            meta: { closable: true },
          });
        }

        return {
          online: false,
          lastChanged: new Date(),
          reconnecting: false,
        };
      });
    };

    // Start reconnection attempt animation
    const startReconnecting = () => {
      if (!status.online && !status.reconnecting) {
        setStatus((prev) => ({ ...prev, reconnecting: true }));

        // Animate reconnection indicator if it exists
        const reconnectIndicator = document.querySelector(
          "#reconnect-indicator"
        );
        if (reconnectIndicator) {
          animate(
            reconnectIndicator,
            {
              opacity: [0.5, 1, 0.5],
              scale: [0.95, 1, 0.95],
            } as any,
            {
              duration: 2,
              repeat: Infinity,
              easing: "ease-in-out",
            } as any
          );
        }
      }
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    // Setup periodic connection check
    const checkInterval = setInterval(() => {
      if (!navigator.onLine && !status.reconnecting) {
        startReconnecting();

        // Try to ping the server
        fetch(`${window.location.origin}/ping`, {
          method: "HEAD",
          cache: "no-store",
          mode: "no-cors",
        })
          .then(() => {
            if (!navigator.onLine) {
              // Sometimes the browser doesn't update navigator.onLine
              // Force an online status update
              handleOnline();
            }
          })
          .catch(() => {
            // Still offline, continue reconnecting animation
          });
      }
    }, 30000); // Check every 30 seconds

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
      clearInterval(checkInterval);
    };
  }, [status.online, status.reconnecting]);

  return status;
}
