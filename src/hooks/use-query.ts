import { get, post } from "@/utils";
import { QueryObserver, useQueryClient, UseQueryOptions, useQuery as useQueryRaw } from "@tanstack/react-query";
import { AxiosRequestConfig } from "axios";
import { useMemo } from "react";

export interface IUseQueryOptions {
  retry?: UseQueryOptions["retry"];
  enabled?: boolean;
  headers?: AxiosRequestConfig<unknown>["headers"];
}

export function useQuery<T = unknown>(url: string, query_keys: [string] | string[], options: IUseQueryOptions = {}) {
  const { headers, ...rest } = options;

  const query = useQueryRaw({
    queryKey: query_keys,
    queryFn: async () => await get<T>(url, headers),
    throwOnError: false,
    ...rest,
  });

  return { ...query, query_keys };
}

export function useQueryObserver<T = unknown>(url: string, query_keys: [string] | string[], options: IUseQueryOptions = {}) {
  const query_client = useQueryClient();

  const observer = useMemo(
    () =>
      new QueryObserver(query_client, {
        queryKey: query_keys,
        queryFn: async () => await get<T>(url),
        ...options,
      }),
    [query_client, query_keys, options, url]
  );

  return observer;
}

export function usePostQuery<R = unknown, T = Record<string, never>>(
  url: string,
  query_keys: [string] | string[] | UseQueryOptions["queryKey"],
  data?: T,
  options: IUseQueryOptions = {}
) {
  const { headers, ...rest } = options;

  const result = useQueryRaw({
    queryKey: query_keys,
    queryFn: async () => await post<R, T>(url, data || ({} as T), headers),
    select: (response) => response,
    ...rest,
  });

  return result;
}
