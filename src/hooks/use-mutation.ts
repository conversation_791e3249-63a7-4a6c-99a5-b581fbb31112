/* eslint-disable @typescript-eslint/no-explicit-any */
import configs from "@/config";
import { AppError, useErrorStore } from "@/stores";
import { makeRequest } from "@/utils";
import { useMutation as useMutationRaw, useQueryClient } from "@tanstack/react-query";
import { AxiosRequestConfig, AxiosResponse } from "axios";
import toLower from "lodash.tolower";
import { useState } from "react";

export type HttpMethod = "PUT" | "DELETE" | "POST" | "PATCH";

export interface IUseMutationOptions<R = any> {
  url: string;
  method: HttpMethod;
  baseUrl?: string;
  invalidationKeys?: string[];
  onSuccess?: (data?: AxiosResponse<R, any>) => void;
  headers?: AxiosRequestConfig<any>["headers"];
}

// type WithMethodURL = {
//   /** There are some cases where the url needs to be passed dynamically to the mutation function, method_url is used for that */
//   method_url?: string
//  };

export function useMutation<R = any, D = object>(opts: IUseMutationOptions<R>) {
  const [progress, setProgress] = useState(0);
  const query_client = useQueryClient();
  const { actions } = useErrorStore((state) => state);

  const mutation = useMutationRaw<R, Error, D>({
    mutationFn: async (input: D) => {
      const { method_url, ...data } = input as any;
      return (
        await makeRequest<D, R>({
          data,
          method: opts.method,
          headers: opts?.headers,
          url: method_url ?? opts.url,
          baseURL: opts?.baseUrl || configs.BASE_URL,

          onUploadProgress(ev) {
            setProgress(Math.round((ev.loaded * 100) / (ev?.total ?? 1)));
          },
        })
      ).data;
    },
    onSuccess: (data: any) => {
      if (opts?.onSuccess) opts.onSuccess(data);
      // Invalidate and refetch
      if (opts?.invalidationKeys) opts?.invalidationKeys.forEach((key) => query_client.invalidateQueries({ queryKey: [key] }));
    },
    onError(error, variables /*, context */) {
      const err = error as unknown as AppError;
      //   console.log("Mutation Error Occurred", { err, variables, context });
      actions!.setError({
        status: err.status || err?.code,
        title: "Failed",
        message: err?.msg || err?.error,
        action: {
          type: toLower(opts.method),
          payload: variables,
        },
        showUser: true,
      });
    },
  });

  return { ...mutation, progress };
}
