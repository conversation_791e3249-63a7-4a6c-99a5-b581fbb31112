import { useMemo } from "react";

import { usePostQuery } from "@/hooks";
import {
  BankListRo,
  BookingPricesRo,
  CountryListDataRo,
  CountryListRo,
  EducationLevelListRo,
  ExclusiveServiceListRo,
  MaritalStatusListRo,
  PreferredLanguageListRo,
  ProviderCertificationListRo,
  ProviderDocumentListRo,
  ProviderSpecialTrainingRo,
  ReligionListRo,
  ServiceCategoryRo,
  ServiceOfferringListRo,
} from "@/interfaces";
import configs from "@/config";
import { useAuthStore } from "@/stores";

/**
 * Identity type - returns the same string union type that was passed in
 */
type Identity<T extends string> = T;

/**
 * Declaration type with component name and additional properties
 */
type Decl<A extends string, R> = { component: Identity<A> } & R;

/**
 * Common list response object type
 */
type CommonListRo = {
  "marital-status": MaritalStatusListRo;
  "religion-list": ReligionListRo;
  "preferred-lan": PreferredLanguageListRo;

  "provider-doc-list": ProviderDocumentListRo;

  "bank-list": BankListRo;
  "country-list": CountryListRo;
  "service-category": ServiceCategoryRo;
  "education-level": EducationLevelListRo;
  "service-offering": ServiceOfferringListRo;
  "provider-special-training": ProviderSpecialTrainingRo;
  "provider-certification": ProviderCertificationListRo;
  "booking-prices": BookingPricesRo;
  "exclusive-service": ExclusiveServiceListRo;
};

type ComponentTypes = keyof CommonListRo;
type ComponentReturnType<T extends ComponentTypes> = CommonListRo[T];

export type PhoneCodeType = {
  label: string;
  value: string;
  code: string;
  valueText: string;
};

function getComponent<T extends ComponentTypes>(type: Identity<T>): Decl<T, unknown> {
  return { component: type } as Decl<T, unknown>;
}

/**
 * Hook for accessing common list data
 */
export function useCommonList<T extends ComponentTypes>(component: Identity<T>, enabled = true) {
  const auth = useAuthStore((store) => store.auth);
  const token = auth?.token || configs.LIST_REQUEST_TOKEN || "";

  const queryResult = usePostQuery<ComponentReturnType<T>, { component: T }>(
    `/members/requests/variables?component=${component}`,
    [component],
    getComponent(component),
    {
      retry: false,
      enabled: enabled,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  // const serviceOfferData = usePostQuery<ComponentReturnType<T>, { component: T }>(
  //   `/members/requests/variables?component=${component}`,
  //   ["service-offering-list"],
  //   getComponent(component),
  //   {
  //     enabled: component === "service-offering" && enabled,
  //     headers: {
  //       Authorization: `Bearer ${configs.LIST_REQUEST_TOKEN}`,
  //     },
  //   }
  // );

  // const bankListData = usePostQuery<ComponentReturnType<T>, { component: T }>(
  //   `/members/requests/variables?component=${component}`,
  //   ["bank-list"],
  //   getComponent(component),
  //   {
  //     enabled: component === "bank-list" && enabled,
  //     headers: {
  //       Authorization: `Bearer ${configs.LIST_REQUEST_TOKEN}`,
  //     },
  //   }
  // );

  // console.log("Service offer data", service_offer_data);

  const phoneCodes: PhoneCodeType[] = useMemo(() => {
    if (component !== "country-list") return [];
    const { data, isPending } = queryResult;
    if (isPending || component !== "country-list") return [];
    const list = data?.data as CountryListDataRo[];
    const codes = list?.sort().map((item) => ({
      label: String("+").concat(item.phone_code).concat(` (${item.name})`),
      value: item.iso2,
      code: item.phone_code,
      valueText: String("+").concat(item.phone_code),
    }));

    return codes || [];
  }, [queryResult, component]);

  return {
    phoneData: { ...queryResult, list: queryResult.data, phoneCodes },
    // serviceOfferData: { ...serviceOfferData, list: serviceOfferData.data as ComponentReturnType<T> },
    // bankListData: { ...bankListData, list: bankListData.data as ComponentReturnType<T> },
    ...queryResult,
  };
}

export function findCountryInfoByPhoneCode(phoneCodes: PhoneCodeType[], phone_code: string) {
  if (phoneCodes.length < 1)
    return {
      label: `+${phone_code}`,
      value: `any`,
      code: phone_code,
      valueText: `+${phone_code}`,
    } as PhoneCodeType;
  const item = phoneCodes.find((item) => item.code === phone_code);
  return (item || {}) as PhoneCodeType;
}

/// discriminated union
