/* eslint-disable @typescript-eslint/no-explicit-any */
import { Draft, produce, Producer } from "immer";
import isEqual from "lodash.isequal";
import { useCallback, useEffect, useRef, useState, useSyncExternalStore } from "react";
import { createStore } from "zustand";

export default usePartialState;

export function usePartialState<T>(initial_state: Partial<T>, dependencies: any[] = []) {
  const [store] = useState(createStore<typeof initial_state>(() => ({ ...initial_state })));
  const state = useSyncExternalStore(store.subscribe, store.getState);

  const depRef = useRef<any[]>([]);

  type ImmerDraft<T> = Producer<T>;

  const setState = store.setState;

  const set = useCallback(
    (update: Partial<T> | ImmerDraft<T>, replace = false) => {
      const is_obj = typeof update === "object" && update !== null;
      if (is_obj) {
        if (!replace) return store.setState((state) => ({ ...state, ...update }));
        return store.setState(update, replace);
      }

      if (typeof update === "function") return store.setState(produce((draft: any) => update(draft as Draft<T>)));
    },
    [store]
  );

  const reset = useCallback(() => {
    store.setState(store.getInitialState(), true);
  }, [store]);

  useEffect(() => {
    if (!isEqual(depRef.current, dependencies)) {
      set({ ...initial_state });
      depRef.current = dependencies;
    }
  }, [depRef, dependencies, initial_state, set]);

  type ret_t = [typeof state, typeof set, typeof reset, typeof setState];
  return [state, set, reset, setState] as ret_t;
}
