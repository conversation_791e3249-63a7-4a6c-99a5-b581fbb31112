/* eslint-disable @typescript-eslint/no-explicit-any */
import { IListFilter } from "@/interfaces";
import usePartialState from "./use-partial-state";
import { useLocation, useNavigate } from "react-router";
import { toQueryString } from "@/utils";
import { useMemo } from "react";

export function useListFilter<T extends IListFilter>(initial_filter?: T, update_url_params = true) {
  const location = useLocation();
  const navigate = useNavigate();

  const url_filters = useMemo(() => {
    const params = new URLSearchParams(location.search);
    const entries = Object.fromEntries(params.entries());
    const { start_date, end_date, ...rest } = entries;
    const props = { ...rest };
    if (start_date || end_date) Object.assign(props, { range: { value: "custom", start_date, end_date } });
    return props;
  }, [location.search]);

  const initial = useMemo(() => ({ ...initial_filter, ...url_filters } as T), [initial_filter, url_filters]);

  const [filter, set, , rawSetState] = usePartialState<T>(initial, [initial]);

  // console.log("List filter", filter, location);

  /**
   * Redirects to the specified path with filter values as query parameters
   * @param path - The base path to navigate to
   * @returns void
   */
  function updateUrlParams(path: string, values = filter) {
    const { range, ...rest } = values;
    const params = { ...rest, start_date: range?.start_date, end_date: range?.end_date };
    navigate(`${path}?${toQueryString(params as IListFilter)}`);
    return;
  }

  /**
   * Updates the filter state and provides a function to navigate to a path with the filter values as query parameters
   * @param values - The new filter values to set
   * @returns A function that takes a path string and navigates to that path with the filter values as query parameters
   */
  const handleFilter = (values: typeof filter, replace = false) => {
    console.log("Filter to set", values);
    const { range, ...rest } = values;
    set(rest as typeof filter, replace);

    if (range) {
      setRange(range);
    }

    if (update_url_params) {
      updateUrlParams(location.pathname, values);
    }

    return (path: string) => updateUrlParams(path, values);
  };

  function setRange(value: IListFilter["range"]) {
    rawSetState((state) => ({ ...state, range: { ...state?.range, ...value } }));
  }

  /**
   * Removes a specific filter or resets all filters to their initial state
   * @param key - The specific filter key to remove, or "all" to reset all filters
   * @returns void
   */
  function clearFilter(key: keyof T | "all") {
    // console.log("Filter to clear", key);
    if (!key) return;

    if (key === "all") {
      const updateUrlParams = handleFilter(initial_filter || {}, true);
      if (update_url_params) updateUrlParams(location.pathname);
      return;
    }

    if (!(key in filter)) return;

    if (key === "range") {
      rawSetState((state) => {
        const new_state = { ...state, range: undefined };
        if (update_url_params) updateUrlParams(location.pathname, new_state as T);
        return new_state;
      });
      return;
    }

    set((prev) => {
      const k = key as keyof typeof prev;
      (prev as any)[k] = undefined;
      if (update_url_params) updateUrlParams(location.pathname, prev as T);
      return prev;
    });
  }

  return { filter, setFilter: handleFilter, clearFilter, setRange };
}
