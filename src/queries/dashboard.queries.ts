import configs from "@/config";
import { makeRequest } from "@/utils";

type ComponentType = "dashboard-booking-stat" | "dashboard-booking-graph" | "dashboard-booking-service" | "dashboard-booking-day";

export function dashboardStatsQueryOpts<T = unknown>(component?: ComponentType, year?: string, month?: string) {
  // let called = 0;
  return {
    retry: false,
    throwOnError: false,
    revalidateIfStale: false,
    queryKey: ["dashboard/stats", component, year, month],
    enabled: true,
    queryFn: async () => {
      // called++;
      // console.log("Called", called);
      const url = `/users/providers/dashboard-stats?component=${component}&start_year=${
        year ?? new Date().getFullYear().toString()
      }&start_month=${(month ?? "1").padStart(2, "0")}`;
      // if (year) url.concat(`&start_year=${year}`);
      // if (month) url.concat(`&start_month=${month}`);

      const result = await makeRequest<void, T>({
        method: "GET",
        baseURL: configs.BOOKING_SERVICE_URL,
        url,
      });
      return result.data;
    },

    // retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
  };
}
