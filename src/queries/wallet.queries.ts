import configs from "@/config";
import { IUseMutationOptions } from "@/hooks";
import { InitFundWalletRo, WalletListRo, WalletStatRo } from "@/interfaces";
import { makeRequest } from "@/utils";

/// QUERIES
export function walletListQueryOpts(query?: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["wallet/list", query],
    queryFn: async () =>
      (
        await makeRequest<void, WalletListRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/earnings?${query || ""}`,
        })
      ).data,
  };
}

export function walletListCountQueryOpts(query?: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["wallet/list-count", query],
    queryFn: async () =>
      (
        await makeRequest<void, WalletListRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/earnings?component=count&${query || ""}`,
        })
      ).data,
  };
}

export function walletStatsQueryOpts() {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["wallet/stats"],
    queryFn: async () =>
      (
        await makeRequest<void, WalletStatRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/earnings?component=count-status`,
        })
      ).data,
  };
}

/// MUTATIONS
export function initFundWalletMutationOpts(): IUseMutationOptions<InitFundWalletRo> {
  return {
    invalidationKeys: [],
    baseUrl: configs.BOOKING_SERVICE_URL,
    method: "POST",
    url: `/users/orgs/wallets`,
  };
}

export function completeFundWalletMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["wallet/list", "wallet/stats"],
    baseUrl: configs.BOOKING_SERVICE_URL,
    method: "PUT",
    url: `/users/orgs/wallets`,
  };
}

// export function paystackTransaction(opts: {pk: string, amount: number, email: string})
