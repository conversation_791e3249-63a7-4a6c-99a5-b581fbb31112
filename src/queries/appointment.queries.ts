import configs from "@/config";
import { IUseMutationOptions } from "@/hooks";
import {
  AppointmentAssessmentRo,
  AppointmentByIdRo,
  AppointmentDiagnosisDatasetRo,
  AppointmentDiagnosisRo,
  AppointmentListRo,
  AppointmentMedicalPrescriptionRo,
  AppointmentProgressNoteRo,
  AppointmentStatsRo,
  AppointmentTreatmentPlanRo,
  ListCountRo,
  MedicalRecordRo,
  MedicationMedicalRecordRo,
} from "@/interfaces";
import { makeRequest } from "@/utils";

export function appointmentsQueryOpts(query?: string) {
  return {
    queryKey: ["appointment/list", query],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentListRo>({
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/appointments?${query || ""}`,
          method: "GET",
        })
      ).data,
  };
}

export function upcomingAppointmentQueryOpts() {
  return {
    queryKey: ["appointment/upcomings"],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentListRo>({
          baseURL: configs.BOOKING_SERVICE_URL,
          url: "/users/providers/appointments?status=1",
          method: "GET",
        })
      ).data,
  };
}

export function appointmentByIdQueryOpts(id?: string) {
  return {
    enabled: !!id,
    queryKey: ["appointment/byId", id],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentByIdRo>({
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/appointments/${id}`,
          method: "GET",
        })
      ).data,
  };
}

export function appointmentListCountQueryOpts(query?: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/list-count", query],
    queryFn: async () =>
      (
        await makeRequest<void, ListCountRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/appointments?component=count&${query || ""}`,
        })
      ).data,
  };
}

export function appointmentStatsQueryOpts(query?: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/list-stats", query],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentStatsRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/appointments?component=count-status&${query || ""}`,
        })
      ).data,
  };
}

export function appointmentDiagnosisDatasetQueryOpts(id: string, component: "dataset" | "count" = "dataset") {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/diagnosis-dataset", id, component],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentDiagnosisDatasetRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/booking-diagnosis?component=${component}&appointment_id=${id}`,
        })
      ).data,
  };
}

export function getAppointmentAssessmentQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/assessment-forms", query],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentAssessmentRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/booking-assessments?${query}`,
        })
      ).data,
  };
}

export function getAssessmentMedicalRecordsQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/assessment-mr", query],
    queryFn: async () =>
      (
        await makeRequest<void, MedicalRecordRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/mr-assessments?${query}`,
        })
      ).data,
  };
}

export function getToolsMedicalRecordsQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/tools-mr", query],
    queryFn: async () =>
      (
        await makeRequest<void, MedicalRecordRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/mr-tools?${query}`,
        })
      ).data,
  };
}

export function getAppointmentDiagnosisQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/diagnosis-forms", query],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentDiagnosisRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/booking-diagnosis?${query}`,
        })
      ).data,
  };
}

export function getDiagnosisMedicalRecordsQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/diagnosis-mr", query],
    queryFn: async () =>
      (
        await makeRequest<void, MedicalRecordRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/mr-diagnosis?${query}`,
        })
      ).data,
  };
}

export function getTreatmentPlanMedicalRecordsQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/treatment-plan-mr", query],
    queryFn: async () =>
      (
        await makeRequest<void, MedicalRecordRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/mr-treatment-plans?${query}`,
        })
      ).data,
  };
}

export function getAppointmentTreatmentPlanQueryOpts<T = AppointmentTreatmentPlanRo>(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/treatment-plan-forms", query],
    queryFn: async () =>
      (
        await makeRequest<void, T>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/booking-treatment-plans?${query}`,
        })
      ).data,
  };
}

export function getAppointmentToolsQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/tools-forms", query],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentTreatmentPlanRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/booking-tools?${query}`,
        })
      ).data,
  };
}

export function getAppointmentMedicalRecordsToolsQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/tools-forms", query],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentTreatmentPlanRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/mr-tools?${query}`,
        })
      ).data,
  };
}

export function getMedicationMedicalRecordsQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/medication-mr", query],
    queryFn: async () =>
      (
        await makeRequest<void, MedicationMedicalRecordRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/mr-medication-plans?${query}`,
        })
      ).data,
  };
}

export function getAppointmentProgressNoteQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/progress-note-forms", query],
    queryFn: async () =>
      (
        await makeRequest<void, AppointmentProgressNoteRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/booking-progress-notes?${query}`,
        })
      ).data,
  };
}

export function getProgressNoteMedicalRecordsQueryOpts(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/progress-note-mr", query],
    queryFn: async () =>
      (
        await makeRequest<void, MedicalRecordRo>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/mr-progress-notes?${query}`,
        })
      ).data,
  };
}

export function getAppointmentMedicalPrescriptionQueryOpts<T = AppointmentMedicalPrescriptionRo>(query: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["appointment/medical-prescription-forms", query],
    queryFn: async () =>
      (
        await makeRequest<void, T>({
          method: "GET",
          baseURL: configs.BOOKING_SERVICE_URL,
          url: `/users/providers/booking-med-managements?${query}`,
        })
      ).data,
  };
}

/// MUTATIONS

export function startApptSessionMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/list", "appointment/byId", id],
    method: "POST",
    url: `/users/providers/appointments`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function markApptSessionAsCompleteMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/list", "appointment/byId", id],
    method: "PUT",
    url: `/users/providers/appointments/${id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function cancelMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/list", "appointment/byId", id],
    method: "DELETE",
    url: `/users/providers/appointments/${id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function createAssessmentMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/assessment-forms", "appointment/byId", id],
    method: "POST",
    url: `/users/providers/booking-assessments`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function deleteAssessmentMutationOpts(appointment_id: string, form_update_id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/assessment-forms", "appointment/byId", appointment_id],
    method: "DELETE",
    url: `/users/providers/booking-assessments/${form_update_id}?appointment_id=${appointment_id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function createDiagnosisMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/diagnosis-forms", "appointment/byId", id],
    method: "POST",
    url: `/users/providers/booking-diagnosis`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function deleteDiagnosisMutationOpts(appointment_id: string, icd_data_id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/diagnosis-forms", "appointment/byId", appointment_id],
    method: "DELETE",
    url: `/users/providers/booking-diagnosis/${icd_data_id}?appointment_id=${appointment_id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function createTreatmentPlanMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/treatment-plan-forms", "appointment/byId", id],
    method: "POST",
    url: `/users/providers/booking-treatment-plans`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function deleteTreatmentPlanMutationOpts(appointment_id: string, form_update_id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/treatment-plan-forms", "appointment/byId", appointment_id],
    method: "DELETE",
    url: `/users/providers/booking-treatment-plans/${form_update_id}?appointment_id=${appointment_id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function createToolsMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/tools-forms", "appointment/byId", id],
    method: "POST",
    url: `/users/providers/booking-tools`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function deleteToolsMutationOpts(appointment_id: string, form_update_id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/tools-forms", "appointment/byId", appointment_id],
    method: "DELETE",
    url: `/users/providers/booking-tools/${form_update_id}?appointment_id=${appointment_id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function createProgressNoteMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/progress-note-forms", "appointment/byId", id],
    method: "POST",
    url: `/users/providers/booking-progress-notes`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function deleteProgressNoteMutationOpts(appointment_id: string, form_update_id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/progress-note-forms", "appointment/byId", appointment_id],
    method: "DELETE",
    url: `/users/providers/booking-progress-notes/${form_update_id}?appointment_id=${appointment_id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function createMedicalPrescriptionMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/medical-prescription-forms", "appointment/byId", id],
    method: "POST",
    url: `/users/providers/booking-med-managements`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function deleteMedicalPrescriptionMutationOpts(appointment_id: string, form_update_id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/medical-prescription-forms", "appointment/byId", appointment_id],
    method: "DELETE",
    url: `/users/providers/booking-med-managements/${form_update_id}?appointment_id=${appointment_id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}

export function referUserMutationOpts(appointment_id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["appointment/list", "appointment/byId", appointment_id],
    method: "PATCH",
    url: `/users/providers/appointments/${appointment_id}`,
    baseUrl: configs.BOOKING_SERVICE_URL,
  };
}
