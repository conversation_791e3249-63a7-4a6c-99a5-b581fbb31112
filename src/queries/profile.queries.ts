import { IUseMutationOptions } from "@/hooks";
import {
  AssociationRo,
  CertificationRo,
  EducationLevelRo,
  ProviderDocumentRo,
  PublicationRo,
  SpecialTrainingRo,
  UserRo,
} from "@/interfaces";
import { makeRequest } from "@/utils";
// import { ListCountRo } from "@/interfaces";
// import { makeRequest } from "@/utils";

export function getUserProfileQueryOpts(enabled = true) {
  return {
    enabled,
    throwOnError: false,
    queryKey: ["user/get-profile", enabled],
    queryFn: async () => {
      if (!enabled) return {} as UserRo;

      const result = await makeRequest<void, UserRo>({
        method: "GET",
        url: `/providers/accounts/`,
      });
      return result.data;
    },
  };
}

export function getSpecialTrainingsQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/training-list", query],
    queryFn: async () => {
      const result = await makeRequest<void, SpecialTrainingRo>({
        method: "GET",
        url: `/providers/accounts/special-trainings/?${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getEducationLevelsQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/education-list", query],
    queryFn: async () => {
      const result = await makeRequest<void, EducationLevelRo>({
        method: "GET",
        url: `/providers/accounts/education-levels?${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getPublicationsQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/publication-list", query],
    queryFn: async () => {
      const result = await makeRequest<void, PublicationRo>({
        method: "GET",
        url: `/providers/accounts/publications/?${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getCertificationsQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/certification-list", query],
    queryFn: async () => {
      const result = await makeRequest<void, CertificationRo>({
        method: "GET",
        url: `/providers/accounts/certifications/?${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getAssociationQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/association-list", query],
    queryFn: async () => {
      const result = await makeRequest<void, AssociationRo>({
        method: "GET",
        url: `/providers/accounts/med-associations/?${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getUploadedDocumentsQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/document-list", query],
    queryFn: async () => {
      const result = await makeRequest<void, ProviderDocumentRo>({
        method: "GET",
        url: `/providers/accounts/documents?${query || ""}`,
      });
      return result.data;
    },
  };
}

/// Mutations

export function uploadAvatarMutationOpts(dont_invalidate?: boolean): IUseMutationOptions {
  return {
    invalidationKeys: dont_invalidate ? [] : ["user/get-profile"],
    method: "POST",
    url: `/providers/files/profile-avatar`,
    headers: {
      "Content-Type": "multipart/form-data",
      Accept: "application/form-data",
    },
  };
}

export function uploadDocumentMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/document-list"],
    method: "POST",
    url: `/providers/files/document`,
    headers: {
      "Content-Type": "multipart/form-data",
      Accept: "application/form-data",
    },
  };
}

export function deleteUploadedDocumentMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/document-list"],
    method: "DELETE",
    url: `/providers/accounts/documents/${id}`,
  };
}

export function updateProfileInfoMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile"],
    method: "PUT",
    url: `/providers/accounts/`,
  };
}

///// EDUCATION MUTATIONS /////

export function addEducationMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/education-list"],
    method: "POST",
    url: `/providers/accounts/education-levels`,
  };
}

export function updateEducationMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/education-list"],
    method: "PUT",
    url: `/providers/accounts/education-levels/${id}`,
  };
}

export function deleteEducationMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/education-list"],
    method: "DELETE",
    url: `/providers/accounts/education-levels/${id}`,
  };
}

//// PUBLICATION MUTATIONS ////

export function addPublicationMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/publication-list"],
    method: "POST",
    url: `/providers/accounts/publications`,
  };
}

export function updatePublicationMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/publication-list"],
    method: "PUT",
    url: `/providers/accounts/publications/${id}`,
  };
}

export function deletePublicationMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/publication-list"],
    method: "DELETE",
    url: `/providers/accounts/publications/${id}`,
  };
}

//// CERTIFICATION MUTATIONS ////

export function addCertificationMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/certification-list"],
    method: "POST",
    url: `/providers/accounts/certifications`,
  };
}

export function updateCertificationMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/certification-list"],
    method: "PUT",
    url: `/providers/accounts/certifications/${id}`,
  };
}

export function deleteCertificationMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/certification-list"],
    method: "DELETE",
    url: `/providers/accounts/certifications/${id}`,
  };
}

///// PROFESSIONAL TRAINING MUTATIONS ////

export function addTrainingMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: [/*"user/get-profile", */ "user/training-list", "user/trainingById"],
    method: "POST",
    url: `/providers/accounts/special-trainings`,
  };
}

export function updateTrainingMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/training-list", "user/trainingById"],
    method: "PUT",
    url: `/providers/accounts/special-trainings/${id}`,
  };
}

export function deleteTrainingMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/training-list", "user/traingById", id],
    method: "DELETE",
    url: `/providers/accounts/special-trainings/${id}`,
  };
}

///// ASSOCIATION MUTATIONS ////

export function addAssociationMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/association-list", "user/associationById"],
    method: "POST",
    url: `/providers/accounts/med-associations/`,
  };
}

export function updateAssociationMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/association-list", "user/associationById"],
    method: "PUT",
    url: `/providers/accounts/med-associations//${id}`,
  };
}

export function deleteAssociationMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/association-list", "user/traingById", id],
    method: "DELETE",
    url: `/providers/accounts/med-associations//${id}`,
  };
}

//// SERVICE OFFER MUTATIONS ////

export function addServiceOfferMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/service-rate-list"],
    method: "POST",
    url: `/providers/accounts/service-offers`,
  };
}

export function addAvailabilityMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/availability-list"],
    method: "POST",
    url: `/providers/accounts/schedules`,
  };
}

// export function suspendOrActivateProviderMutationOpts(id: string): IUseMutationOptions {
//   return {
//     invalidationKeys: ["provider/list", "provider/byId", id],
//     method: "PATCH",
//     url: `/orgs/accounts/org-providers/${id}`,
//   };
// }
