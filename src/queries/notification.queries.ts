import { IUseMutationOptions } from "@/hooks";
import { ListCountRo, NotificationListRo } from "@/interfaces";
import { makeRequest } from "@/utils";

export function getNotificationListQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["notification/list", query],
    queryFn: async () => {
      const result = await makeRequest<void, NotificationListRo>({
        method: "GET",
        url: `/providers/accounts/notifications?${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getNotificationListCountQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["notification/list-count", query],
    queryFn: async () => {
      const result = await makeRequest<void, ListCountRo>({
        method: "GET",
        url: `/providers/accounts/notifications?component=count&${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getUnreadNotificationCountQueryOpts() {
  return {
    throwOnError: false,
    queryKey: ["notification/unread-count"],
    queryFn: async () => {
      const result = await makeRequest<void, ListCountRo>({
        method: "GET",
        url: `/providers/accounts/notifications?status=0&component=count`,
      });
      return result.data;
    },
  };
}

/// MUTATIONS
export function markAllNotificationAsReadMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["notification/list", "notification/list-count", "notification/unread-count"],
    method: "PUT",
    url: `/providers/accounts/notifications`,
  };
}
