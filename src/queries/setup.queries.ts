import { IUseMutationOptions } from "@/hooks";
import { AvailabilityListRo, ExclusiveServiceDbListRo, ResolveBankAccountNumberRo, ServiceRateListRo } from "@/interfaces";
import { makeRequest } from "@/utils";

export function getServiceRatesQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/service-rate-list", query],
    queryFn: async () => {
      const result = await makeRequest<void, ServiceRateListRo>({
        method: "GET",
        url: `/providers/accounts/service-offers?${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getAvailabilityQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/availability-list", query],
    queryFn: async () => {
      const result = await makeRequest<void, AvailabilityListRo>({
        method: "GET",
        url: `/providers/accounts/schedules?${query || ""}`,
      });
      return result.data;
    },
  };
}

export function getExclusiveServicesQueryOpts(query?: string) {
  return {
    throwOnError: false,
    queryKey: ["user/exclusive-services", query],
    queryFn: async () => {
      const result = await makeRequest<void, ExclusiveServiceDbListRo>({
        method: "GET",
        url: `/providers/accounts/service-exclusive?${query || ""}`,
      });
      return result.data;
    },
  };
}

/// MUTATIONS

export function changePasswordMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile"],
    method: "PATCH",
    url: `/providers/accounts/`,
  };
}

export function deleteServiceRateMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/service-rate-list"],
    method: "DELETE",
    url: `/providers/accounts/service-offers/${id}`,
  };
}

export function deleteAvailabilityMutationOpts(id: string): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/availability-list"],
    method: "DELETE",
    url: `/providers/accounts/schedules/${id}`,
  };
}

export function resolveBankAccountNoMutationOpts(): IUseMutationOptions<ResolveBankAccountNumberRo> {
  return {
    invalidationKeys: ["user/get-profile"],
    method: "POST",
    url: `/providers/accounts/verify-account-number`,
  };
}
export function updateAvailabilityStatusMutationOpts(id: string): IUseMutationOptions<unknown> {
  return {
    invalidationKeys: ["user/availability-list"],
    method: "PUT",
    url: `/providers/accounts/schedules/${id}`,
  };
}

//// EXCLUSIVE SERVICES MUTATIONS ////
export function updateExclusiveServicesMutationOpts(): IUseMutationOptions {
  return {
    invalidationKeys: ["user/get-profile", "user/exclusive-services"],
    method: "POST",
    url: `/providers/accounts/service-exclusive`,
  };
}
