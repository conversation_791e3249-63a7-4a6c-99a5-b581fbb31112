import configs from "@/config";
import { IUseMutationOptions } from "@/hooks";
import { InitFundWalletRo, ListCountRo, PartnerByIdRo, PartnerEarningRo, PartnerListRo, WalletStatRo } from "@/interfaces";
import { makeRequest } from "@/utils";

/// QUERIES
export function partnerListQueryOpts(query?: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["partner/list", query],
    queryFn: async () =>
      (
        await makeRequest<void, PartnerListRo>({
          method: "GET",
          url: `/providers/accounts/org-partners/?${query || ""}`,
        })
      ).data,
  };
}

export function partnerListCountQueryOpts(query?: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["partner/list-count", query],
    queryFn: async () =>
      (
        await makeRequest<void, ListCountRo>({
          method: "GET",
          url: `/providers/accounts/org-partners?component=count&${query || ""}`,
        })
      ).data,
  };
}

export function getPartnerByIdQueryOpts(id: string) {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["partner/by-id", id],
    queryFn: async () =>
      (
        await makeRequest<void, PartnerByIdRo>({
          method: "GET",
          url: `/providers/accounts/org-partners/${id}`,
        })
      ).data,
  };
}

export function partnerStatsQueryOpts() {
  return {
    suspense: false,
    throwOnError: false,
    queryKey: ["partner/stats"],
    queryFn: async () =>
      (
        await makeRequest<void, WalletStatRo>({
          method: "GET",
          url: `/providers/accounts/org-partners?component=count-status`,
        })
      ).data,
  };
}

export function partnerEarningsQueryOpts(id?: string, query?: string) {
  return {
    throwOnError: false,
    queryKey: ["partner/earnings", id, query],
    queryFn: async () => {
      const result = await makeRequest<void, PartnerEarningRo>({
        method: "GET",
        baseURL: configs.BOOKING_SERVICE_URL,
        url: `/users/providers/earnings/${id ?? ""}?${query || ""}`,
      });
      return result.data;
    },
  };
}

/// MUTATIONS
export function declineSuspendOrActivatePartnerMutationOpts(id: string): IUseMutationOptions<InitFundWalletRo> {
  return {
    invalidationKeys: ["partner/list", "partner/list-count", "partner/by-id", "partner/stats"],
    method: "PATCH",
    url: `/providers/accounts/org-partners/${id}`,
  };
}

// export function paystackTransaction(opts: {pk: string, amount: number, email: string})
