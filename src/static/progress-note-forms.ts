import {
  AppointmentDiagnosisRo,
  AppointmentTreatmentPlanDataRo,
  AppointmentTreatmentPlanRo,
  IGenericAppointmentFormCategory,
} from "@/interfaces";
import { getAppointmentDiagnosisQueryOpts, getAppointmentTreatmentPlanQueryOpts } from "@/queries";
import { toQueryString } from "@/utils";
import { appt_tools_forms } from "./tools-forms";

export const progress_note: IGenericAppointmentFormCategory = {
  id: "progress-note",
  category: "Progress note",
  forms: [
    {
      label: "Assessment tool(s) used",
      type: "select",
      field_name: "assessment-tools-used",
      group: "two",
      value: [""],
      // options: [
      //   { label: "General Anxiety Disorder (GAD-7)", value: "General Anxiety Disorder (GAD-7)" },
      //   { label: "Suicide Screening", value: "Suicide Screening" },
      //   { label: "Mood Disorder Questionnaire MDQ9", value: "Mood Disorder Questionnaire MDQ9" },
      //   { label: "Client Health Questionnaire PHQ-9", value: "Client Health Questionnaire PHQ-9" },
      //   {
      //     label: "Substance Abuse Assessment Tool The CRAFT Screening Questions",
      //     value: "Substance Abuse Assessment Tool The CRAFT Screening Questions",
      //   },
      //   { label: "Substance Abuse Assessment Tool MAST", value: "Substance Abuse Assessment Tool MAST" },
      //   { label: "Substance Abuse Assessment Tool Gambling Screen", value: "Substance Abuse Assessment Tool Gambling Screen" },
      //   { label: "Substance Abuse Assessment Tool DAST", value: "Substance Abuse Assessment Tool DAST" },
      //   { label: "Substance Abuse Assessment Tool SASSI", value: "Substance Abuse Assessment Tool SASSI" },
      // ],
      options: appt_tools_forms.map((item) => ({ label: item.category, value: item.id })),
      can_filter_list: true,
    },
    {
      label: "Attendance",
      type: "select",
      field_name: "attendance",
      group: "two",
      value: [""],
      options: [
        { label: "Present", value: "present" },
        { label: "No Show", value: "no show" },
        { label: "Canceled", value: "canceled" },
        { label: "No Schedule", value: "no schedule" },
      ],
    },
    {
      label: "Treatment Plan",
      type: "unresolved-select",
      field_name: "treatment-plan",
      group: "two",
      value: [""],
      placeholder: "Select Treatment Plan",
      options: (...args: string[]) => getAppointmentTreatmentPlanQueryOpts(toQueryString({ appointment_id: args[0] })),
      transform: (data) => {
        const ro = data as AppointmentTreatmentPlanRo;
        const plans = (ro?.data || []) as AppointmentTreatmentPlanDataRo[];
        return plans.map((item) => ({ label: item.data_name, value: item.data_id }));
      },
    },
    {
      label: "Diagnostic Impression",
      type: "unresolved-select",
      field_name: "diagnosis",
      group: "two",
      value: [""],
      placeholder: "Select Diagnosis",
      options: (...args: string[]) => getAppointmentDiagnosisQueryOpts(toQueryString({ appointment_id: args[0] })),
      transform: (data) => {
        const ro = data as AppointmentDiagnosisRo;
        const diagnosis = ro?.data || [];
        return diagnosis.map((item) => ({ label: `${item.code10} / ${item.code9}`, value: item.code10, description: item.description }));
      },
    },
    {
      type: "line",
      label: "",
      field_name: "line",
      value: "",
    },
    {
      type: "select",
      label: "Appearance",
      field_name: "appearance",
      placeholder: "Select Option",
      group: "two-a",
      value: [""],
      options: [
        { label: "Appropriate Dress", value: "Appropriate Dress" },
        { label: "Inappropriate Dress", value: "Inappropriate Dress" },
        { label: "Good Hygiene", value: "Good Hygiene" },
        { label: "Poor Hygiene", value: "Poor Hygiene" },
        { label: "Appropriate Eye Contact", value: "Appropriate Eye Contact" },
        { label: "Poor Eye Contact", value: "Poor Eye Contact" },
        { label: "Other", value: "Other" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "appearance-comment",
      group: "two-a",
      value: "",
    },
    {
      type: "select",
      label: "Manifest Behavior",
      field_name: "manifest-behavior",
      placeholder: "Select Option",
      group: "two-a",
      value: [""],
      options: [
        { label: "Cooperative", value: "Cooperative" },
        { label: "Oppositional", value: "Oppositional" },
        { label: "Passive", value: "Passive" },
        { label: "Destructive", value: "Destructive" },
        { label: "Attention Seeking", value: "Attention Seeking" },
        { label: "Restless", value: "Restless" },
        { label: "Tics", value: "Tics" },
        { label: "Aggressive", value: "Aggressive" },
        { label: "Other", value: "Other" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "manifest-behavior-comment",
      group: "two-a",
      value: "",
    },
    {
      type: "select",
      label: "Speech",
      field_name: "speech",
      placeholder: "Select Option",
      group: "two-a",
      value: [""],
      options: [
        { label: "Normal Rate", value: "Normal Rate" },
        { label: "Normal Rhythm", value: "Normal Rhythm" },
        { label: "Normal Tone", value: "Normal Tone" },
        { label: "Articulation Defect", value: "Articulation Defect" },
        { label: "Pressured", value: "Pressured" },
        { label: "Loud", value: "Loud" },
        { label: "Slurred", value: "Slurred" },
        { label: "Slowed", value: "Slowed" },
        { label: "Soft", value: "Soft" },
        { label: "Mute", value: "Mute" },
        { label: "Other", value: "Other" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "speech-comment",
      group: "two-a",
      value: "",
    },
    {
      type: "select",
      label: "Affect",
      field_name: "affect",
      placeholder: "Select Option",
      group: "two-a",
      value: [""],
      options: [
        { label: "Appropriate", value: "Appropriate" },
        { label: "Anxious", value: "Anxious" },
        { label: "Depressed", value: "Depressed" },
        { label: "Flat", value: "Flat" },
        { label: "Labile", value: "Labile" },
        { label: "Hostile", value: "Hostile" },
        { label: "Other", value: "Other" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "affect-comment",
      group: "two-a",
      value: "",
    },
    {
      type: "line",
      label: "",
      field_name: "line",
      group: "one-b",
      value: "",
    },
    {
      type: "select",
      label: "Thought processes",
      field_name: "thought-processes",
      placeholder: "Select Option",
      group: "two-b",
      value: [""],
      options: [
        { label: "Goal Directed", value: "Goal Directed" },
        { label: "Circumstantial", value: "Circumstantial" },
        { label: "Tangential", value: "Tangential" },
        { label: "Flight of ideas", value: "Flight of ideas" },
        { label: "Blocking", value: "Blocking" },
        { label: "Normal", value: "Normal" },
        { label: "Other", value: "Other" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "thought-processes-comment",
      group: "two-b",
      value: "",
    },
    {
      type: "select",
      label: "Perceptual disturbances",
      field_name: "perceptual-disturbances",
      placeholder: "Select Option",
      group: "two-b",
      value: [""],
      options: [
        { label: "Auditory Hallucinations", value: "Auditory Hallucinations" },
        { label: "Visual Hallucinations", value: "Visual Hallucinations" },
        { label: "Tactile Hallucinations", value: "Tactile Hallucinations" },
        { label: "Other Hallucinations", value: "Other Hallucinations" },
        { label: "Illusions", value: "Illusions" },
        { label: "Not Elicited", value: "Not Elicited" },
        { label: "Other", value: "Other" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "perceptual-disturbances-comment",
      group: "two-b",
      value: "",
    },

    {
      type: "select",
      label: "Attention",
      field_name: "attention",
      placeholder: "Select Option",
      group: "two-b",
      value: [""],
      options: [
        { label: "Distractible", value: "Distractible" },
        { label: "Appropriate", value: "Appropriate" },
        { label: "Good Concentration", value: "Good Concentration" },
        { label: "Fair Concentration", value: "Fair Concentration" },
        { label: "Poor Concentration", value: "Poor Concentration" },
        { label: "Others", value: "Others" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "attention-comment",
      group: "two-b",
      value: "",
    },
    {
      type: "select",
      label: "Insight/judgement",
      field_name: "insight-judgement",
      placeholder: "Select Option",
      group: "two-b",
      value: [""],
      options: [
        { label: "Good", value: "Good" },
        { label: "Fair", value: "Fair" },
        { label: "Poor", value: "Poor" },
        { label: "Others", value: "Others" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "insight-judgement-comment",
      group: "two-b",
      value: "",
    },
    {
      type: "select",
      label: "Reported behavior elsewhere",
      field_name: "reported-behavior",
      placeholder: "Select Option",
      group: "two-b",
      value: [""],
      options: [
        { label: "Good (Home)", value: "Good (Home)" },
        { label: "Fair (Home)", value: "Fair (Home)" },
        { label: "Poor (Home)", value: "Poor (Home)" },
        { label: "Good (School)", value: "Good (School)" },
        { label: "Fair (School)", value: "Fair (School)" },
        { label: "Poor (School)", value: "Poor (School)" },
        { label: "Good (Community)", value: "Good (Community)" },
        { label: "Fair (Community)", value: "Fair (Community)" },
        { label: "Poor (Community)", value: "Poor (Community)" },
        { label: "Others", value: "Others" },
      ],
    },
    {
      type: "text",
      label: "Comment (if any)",
      field_name: "reported-behavior-comment",
      group: "two-b",
      value: "",
    },
    {
      type: "textarea",
      label: "Comments: (include 1. What client, caretaker, and therapist discussed; 2. Therapist's clinical assessment; and 3. next step)",
      field_name: "comments",
      group: "one-ba",
      value: "",
      hideCharCount: true,
    },
    {
      type: "text",
      label: "Signature",
      field_name: "signature",
      group: "two-ba",
      value: "",
    },
    {
      type: "date",
      label: "Date",
      field_name: "date",
      group: "two-ba",
      value: "",
    },
  ],
};

export const appt_progress_note_forms: IGenericAppointmentFormCategory[] = [progress_note];
