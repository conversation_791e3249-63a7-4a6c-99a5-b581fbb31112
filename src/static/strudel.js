// samples({
//   bd: "bd/BT0A0D0.wav",
//   sn: "sn/ST0T0S3.wav",
//   hh: "hh/000_hh3closedhh.wav",
//   cp: "cp/HANDCLP0.wav",
// }, 'https://loophole-letters.vercel.app/samples/tidal/')

const h = (x) => x.transpose("<0@2 5 0 7 5 0 -5>/2");

stack(
  s("<<bd*2 bd> sn> [<hh [hh ~ hh hh]>]").bank("tr909").fast(2).gain(0.5),
  "[c2 a1 bb1 ~] ~"
    .echo(2, 1 / 16, 1)
    .slow(2)
    .layer(h)
    .note()
    .s("sawtooth")
    .clip(0.4)
    .cutoff(200)
    .decay(0.12)
    .sustain(0.05),
  "[g2,[c3 eb3]]"
    .iter(4)
    .echoWith(4, 1 / 8, (x, n) => x.transpose(n * 12).velocity(Math.pow(0.4, n)))
    .layer(h)
    .note()
    .clip(0.4),
  "[g3,[c4 eb4]]"
    .echoWith(4, 1 / 8, (x, n) => x.transpose(n * 12).velocity(Math.pow(0.4, n)))
    .layer(h)
    .note()
    .clip(0.4)
)
  .fast(2 / 3)
  .room("<1 2 3 4 5>")
  .phaser(0.5);
