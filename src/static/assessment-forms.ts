import { IGenericAppointmentFormCategory } from "@/interfaces";

export const PLAIN_YES_NO_OPTIONS = [
  { label: "Yes", value: "yes" },
  { label: "No", value: "no" },
  // { label: "N/A", value: "n/a" },
];

const presenting_problem: IGenericAppointmentFormCategory = {
  id: "presenting_problem",
  category: "Presenting Problem",
  forms: [
    {
      label: "Client's Presenting Problem",
      type: "text",
      field_name: "presenting-problem",
      group: "one",
      value: "",
    },
    {
      label: "Sexual Orientation",
      type: "select",
      options: [
        { label: "Male", value: "male" },
        { label: "Female", value: "female" },
      ],
      field_name: "sexual-orientation",
      group: "three",
      value: [],
    },
    {
      label: "Gender Expression",
      type: "text",
      field_name: "gender-expression",
      group: "three",
      value: "",
    },
    {
      label: "Risk to Others?",
      type: "select",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      field_name: "risk-to-others",
      group: "three",
    },
    {
      label: "If yes, explian",
      type: "text",
      field_name: "explain",
      group: "three",
      value: "",
    },
    {
      label: "1. Has the client ever received counselling, psychiatric, or psychological services before?",
      type: "select",
      field_name: "client-counselling",
      group: "two",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      value: [""],
    },
    {
      label: "If yes, please identify where, with whom, and date of begining, and ending of treatment",
      type: "text",
      field_name: "client-counselling-answer",
      group: "two",
      value: "",
    },
    {
      label: "2. Is there a family history of psychiatric, emotional, drug and psychological problem?",
      type: "select",
      field_name: "psychiatric-history",
      group: "two",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      value: [""],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "sychiatric-history-answer",
      group: "two",
      value: "",
    },

    {
      label: "3. Is there a history of reported sexual, emotional or physical abuse of client?",
      type: "select",
      field_name: "sexual-abuse",
      group: "two",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      value: [""],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "sexual-abuse-answer",
      group: "two",
      value: "",
    },
    {
      label: "4a. Has the client ever had suicidal thoughts?",
      type: "select",
      field_name: "suicidal-thoughts",
      group: "two",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      value: [""],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "sexual-thoughts-answer",
      group: "two",
      value: "",
    },
    {
      label: "4b. Does client currently have suicidal thoughts?",
      type: "select",
      field_name: "currently-sucidal",
      group: "two",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],

      value: [""],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "currently-sucidal-answer",
      group: "two",
      value: "",
    },
    {
      label: "4c. Is there a family history of suicidal attempts / death?",
      type: "select",
      field_name: "family-history",
      group: "two",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      value: [""],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "family-history-answer",
      group: "two",
      value: "",
    },
  ],
};

const medical_hx: IGenericAppointmentFormCategory = {
  id: "medical_hx",
  category: "Medical History",
  forms: [
    {
      label: "1a. Is the client in good Medical Health?",
      type: "select",
      field_name: "good-health",
      group: "two",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      value: [""],
    },
    {
      label: "1b. Name of client primary care physician",
      type: "text",
      field_name: "primary-care-physician",
      group: "two",
      value: "",
    },
    {
      label: "Address",
      type: "text",
      field_name: "address",
      group: "two",
      value: "",
    },
    {
      label: "Phone Number",
      type: "text",
      field_name: "Phone",
      group: "two",
      value: "",
    },

    {
      label: "1c. When did the client have a physical examination completed",
      type: "date",
      field_name: "Phone",
      group: "three",
      value: "",
    },

    {
      label: "1d. Is client on medication? (incl. vitamins and OTC)",
      type: "select",
      field_name: "client-medication",
      group: "three",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "1e. What medication/dosages",
      type: "text",
      field_name: "medication-dosages",
      group: "three",
      value: "",
    },

    {
      label: "1f. How Often",
      type: "text",
      field_name: "often",
      group: "six",
      value: "",
    },
    {
      label: "When Medication Began",
      type: "date",
      field_name: "medication-began",
      group: "six",
      value: "",
    },
    {
      label: "1g. Is client compliant with medication?",
      type: "select",
      field_name: "compliant-medication",
      group: "six",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "1h. Who is responsible for monitoring client's compliance with medication?",
      type: "text",
      field_name: "monitoring-medication",
      group: "six",
      value: "",
    },
    {
      label: "1i. Has client ever been hospitalized for any medical reasons?",
      type: "select",
      field_name: "hospitalized-reasons",
      group: "six",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "If so, for what reason?",
      type: "text",
      field_name: "yes-reason",
      group: "six",
      value: "",
    },
    {
      label: "If so, Where?",
      type: "text",
      field_name: "yes-where",
      group: "six",
      value: "",
    },
    {
      label: "If so, When?",
      type: "date",
      field_name: "yes-when",
      group: "six",
      value: "",
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "one",
      value: "",
    },

    {
      label: "2a. Does client have any known allergies?",
      type: "select",
      field_name: "known-allergies",
      group: "eight",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "If so, please describe",
      type: "text",
      field_name: "yes-describe",
      group: "eight",
      value: "",
    },
    {
      label: "2b. Is there any history of medical problems in client's family?",
      type: "select",
      field_name: "history-problems",
      group: "eight",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      value: [""],
    },
    {
      label: "If so, please describe",
      type: "text",
      field_name: "history-describe",
      group: "eight",
      value: "",
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "four",
      value: "",
    },

    {
      label: "3a. Has client experienced any of the following medical problems?",
      type: "select",
      field_name: "experienced-medical-problems",
      //    group: 'nine',
      value: [""],
      options: [
        { label: "ACCIDENTS", value: "accidents" },
        { label: "ANEMIA", value: "anemia" },
        { label: "BOWEL PROBLEMS", value: "bowel problems" },
        { label: "CONVULSIONS", value: "convulsions" },
        { label: "CHRONIC ILLNESS", value: "chronic illness" },
        { label: "HEAD INJURY", value: "head injury" },
        { label: "HEADACHES", value: "headaches" },
        { label: "BIRTH DEFECTS", value: "birth defects" },
        { label: "POISONINGS", value: "poisonings" },
        { label: "PROBLEMS EATING", value: "problems eating" },
        { label: "PROBLEMS SEEING", value: "problems seeing" },
        { label: "PROBLEMS HEARING", value: "problems hearing" },
        { label: "PROBLEMS SPEAKING", value: "problems speaking" },
        { label: "SKIN PROBLEMS", value: "skin problems    " },
        { label: "OTHER HEALTH PROBLEMS", value: "other health problems" },
      ],
      multiple: true,
    },
    // {
    //   label: "",
    //   type: "text",
    //   field_name: "experienced-problems-answer",
    //   //    group: 'nine',
    //   value: "",
    // },
    {
      label: "Please explain any selections",
      type: "textarea",
      field_name: "selections-explained",
      //    group: 'five',
      value: "",
      hideCharCount: true,
    },

    {
      label: "3b. Current Medical Needs",
      type: "text",
      field_name: "current-medical-needs",
      //    group: 'ten',
      value: "",
    },
    {
      label: "3c. Efficacy of current or previously used medication",
      type: "text",
      field_name: "efficacy-medication",
      //    group: 'ten',
      value: "",
    },
    {
      label: "3d. Is client pregnant?",
      type: "select",
      field_name: "client-pregnant",
      //    group: 'ten',
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Explain",
      type: "textarea",
      field_name: "explain-pregnancy",
      //    group: 'ten',
      value: "",
      hideCharCount: true,
    },
  ],
};

const developmental_hx: IGenericAppointmentFormCategory = {
  id: "developmental_hx",
  category: "Developmental History",
  forms: [
    {
      label: "Was trauma experienced?",
      type: "select",
      field_name: "experienced?",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "experienced-answer",
      group: "two",
      value: "",
    },
    {
      label: "Was trauma witnessed?",
      type: "select",
      field_name: "witnessed",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "witnessed-answer",
      group: "two",
      value: "",
    },
    {
      label: "Has client been abused?",
      type: "select",
      field_name: "abused?",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "abused-answer",
      group: "two",
      value: "",
    },
    {
      label: "Has client been neglected?",
      type: "select",
      field_name: "neglected?",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "neglected-answer",
      group: "two",
      value: "",
    },
    {
      label: "Has client experienced violence?",
      type: "select",
      field_name: "violence?",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "If yes, please explain",
      type: "text",
      field_name: "violence-answer",
      group: "two",
      value: "",
    },
    {
      label: "Major life events",
      type: "textarea",
      field_name: "life-events",
      group: "four",
      value: "",
    },
  ],
};

const education_and_family: IGenericAppointmentFormCategory = {
  id: "education_and_family",
  category: "Education and Family",
  forms: [
    {
      label: "1. What was the client's highest grade obtained?",
      type: "select",
      field_name: "degree-level",
      group: "two",
      value: [""],
      options: [
        { label: "NO EDUCATION", value: "no education" },
        { label: "PRIMARY 1", value: "primary 1" },
        { label: "PRIMARY 2", value: "primary 2" },
        { label: "PRIMARY 3", value: "primary 3" },
        { label: "PRIMARY 4", value: "primary 4" },
        { label: "PRIMARY 5", value: "primary 5" },
        { label: "PRIMARY 6", value: "primary 6" },
        { label: "JSS 1", value: "jss 1" },
        { label: "JSS 2", value: "jss 2" },
        { label: "JSS 3", value: "jss 3" },
        { label: "SSS 1", value: "sss 1" },
        { label: "SSS 2", value: "sss 2" },
        { label: "SSS 3", value: "sss 3" },
        { label: "NCE", value: "nce" },
        { label: "DIPLOMA", value: "diploma" },
        { label: "ASSOCIATE DEGREE", value: "associate degree" },
        { label: "VOCATIONAL DIPLOMA", value: "vocational diploma" },
        { label: "SOME COLLEGE-NO DEGREE", value: "some college-no degree" },
        { label: "MASTERS DEGREE", value: "masters degree" },
        { label: "BACHELORS DEGREE", value: "bachelors degree" },
        { label: "HND", value: "hnd" },
        { label: "OND", value: "ond" },
        { label: "PH.D", value: "ph.d" },
      ],
    },
    {
      label: "2. List additional training or certification obtained after high school or college:",
      type: "text",
      field_name: "additional-certification",
      group: "two",
      value: "",
    },
    {
      label: "3a. If you are currently married, please describe your relationship with your spouse:",
      type: "text",
      field_name: "married-relationship-with-spouse",
      group: "two",
      value: "",
    },
    {
      label: "3b. If you are divorced or separated, please describe your past relationship with your spouse:",
      type: "text",
      field_name: "divorced-relationship-with-spouse",
      group: "two",
      value: "",
    },
    {
      label: "3c. What is the client's relationship with the following people?",
      type: "text",
      field_name: "relationship-mother",
      group: "two",
      value: "",
    },
    {
      label: "Mother",
      type: "text",
      field_name: "father",
      group: "two",
      value: "",
    },
    {
      label: "Father",
      type: "text",
      field_name: "father",
      group: "two",
      value: "",
    },
    {
      label: "Children",
      type: "text",
      field_name: "children",
      group: "two",
      value: "",
    },
    {
      label: "Sibling",
      type: "text",
      field_name: "sibling",
      group: "two",
      value: "",
    },
    {
      label: "Spouse",
      type: "text",
      field_name: "spouse",
      group: "two",
      value: "",
    },
    {
      label: "Friends",
      type: "text",
      field_name: "friends",
      group: "two",
      value: "",
    },
    {
      label: "4. General Information",
      type: "text",
      field_name: "",
      group: "one-a",
      value: "",
    },
    {
      label: "(ddd, accidents, hospitalization, deaths, relocation, conflict at home, finances, lost job etc)",
      type: "text",
      field_name: "",
      group: "one-a",
      value: "",
    },
    {
      label: "Is the client or anyone in the family currently experiencing any of the listed problems?",
      type: "text",
      field_name: "family-client",
      group: "two-a",
      value: "",
    },
    {
      label: "If so please describe",
      type: "text",
      field_name: "describe-family",
      group: "two-a",
      value: "",
    },
  ],
};

const living: IGenericAppointmentFormCategory = {
  id: "living",
  category: "Living/Social/Financial",
  forms: [
    {
      id: "living_conditions",
      label: "1. Living Conditions",
      fontWeight: "500",
      type: "heading",
    },
    {
      label: "a. How many people including the client, live at this address?",
      type: "text",
      field_name: "number-of-people",
      group: "three",
      value: "",
      appears_under: "living_conditions",
    },
    {
      label: "b. Who owns this house/apartment?",
      type: "text",
      field_name: "house-owner",
      group: "three",
      value: "",
      appears_under: "living_conditions",
    },
    {
      label: "c. How long has client been living there?",
      type: "date",
      field_name: "living-duration",
      group: "three",
      value: "",
      appears_under: "living_conditions",
    },
    {
      id: "legal_status",
      label: "2. Legal Status",
      fontWeight: "500",
      type: "heading",
    },

    {
      label: "a. Has client ever been arrested for any illegal activities?",
      type: "select",
      field_name: "client-arrested",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
      appears_under: "legal_status",
    },

    {
      label: "b. Are there outstanding charges against you?",
      type: "select",
      field_name: "outstanding-charges",
      group: "two",
      // value: "",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      appears_under: "legal_status",
    },

    {
      label: "If yes, explain",
      type: "textarea",
      field_name: "client-arrested-explain",
      group: "one",
      value: "",
      appears_under: "legal_status",
      hideCharCount: true,
    },

    {
      label: "When?",
      type: "date",
      field_name: "when-arrested",
      group: "two-a",
      value: "",
      appears_under: "legal_status",
    },

    {
      label: "c. Is client currently on probation?",
      type: "select",
      field_name: "is-client-probation",
      group: "two-a",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS.filter((item) => item.value !== "n/a"),
      appears_under: "legal_status",
    },

    {
      label: "When did the probation start?",
      type: "date",
      field_name: "probation-date",
      group: "six",
      value: "",
      appears_under: "legal_status",
    },
    {
      label: "When is it ending?",
      type: "date",
      field_name: "probation-end",
      group: "six",
      value: "",
      appears_under: "legal_status",
    },

    {
      id: "occupation",
      label: "3. Occupation",
      type: "heading",
      fontWeight: "500",
    },

    {
      label: "a. Is client currently working?",
      type: "select",
      field_name: "client-working",
      group: "eight",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      appears_under: "occupation",
    },
    {
      label: "If yes, where?",
      type: "text",
      field_name: "where-client-working",
      group: "eight",
      value: "",
      appears_under: "occupation",
    },

    {
      label: "b. If client answered no, please list your previous experiences or training",
      type: "text",
      field_name: "training",
      group: "eight",
      value: "",
      appears_under: "occupation",
    },
    {
      label: "c. How many hours per week does client work?",
      type: "text",
      field_name: "work-hours",
      group: "eight",
      value: "",
      appears_under: "occupation",
    },

    {
      id: "fin_status",
      label: "4. Financial Status",
      type: "heading",
      fontWeight: "500",
    },

    {
      label: "a. Please list all sources of income",
      type: "text",
      field_name: "income-sources",
      group: "ten",
      value: "",
      appears_under: "fin_status",
    },
    {
      label: "b. Has client recently applied for any benefits?",
      type: "text",
      field_name: "client-benefits",
      group: "ten",
      value: "",
      appears_under: "fin_status",
    },

    {
      label: "c. What was the outcome of the applications?",
      type: "text",
      field_name: "applications-outcome",
      group: "ten",
      value: "",
      appears_under: "fin_status",
    },
    {
      label: "d. Does client need a referral for eligibility benefits?",
      type: "text",
      field_name: "referral-eligibility",
      group: "ten",
      value: "",
      appears_under: "fin_status",
    },

    {
      id: "acitivities",
      label: "5. Activities and Social Group Participation",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "Please list all leisure, structured and cultural activities you are currently participating in:",
      type: "textarea",
      field_name: "leisure",
      group: "thirteen",
      value: "",
      // fontWeight: "500",
      appears_under: "acitivities",
    },

    {
      label: "Spiritual benefits?",
      type: "select",
      field_name: "spiritual-benefits?",
      group: "twelve",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
      appears_under: "acitivities",
    },
    {
      label: "If yes, explain",
      type: "text",
      field_name: "spiritual-benefits-explain",
      group: "twelve",
      value: "",
      appears_under: "acitivities",
    },
  ],
};

const mental_status: IGenericAppointmentFormCategory = {
  id: "mental_status",
  category: "Mental Status",
  forms: [
    {
      id: "appearance",
      label: "Appearance",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "Dressing and Grooming",
      type: "select",
      field_name: "dressing-grooming",
      group: "two",
      value: [""],
      options: [
        { label: "Appropriate", value: "appropriate" },
        { label: "Unappropriate", value: "unappropriate" },
        { label: "Unkempt", value: "unkempt" },
        { label: "Poor Hygiene", value: "poor-hygiene" },
        { label: "Well Groomed", value: "well-groomed" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "appearance",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "dressing-comment",
      group: "two",
      value: "",
      appears_under: "appearance",
    },
    {
      label: "Facial Expressions",
      type: "select",
      field_name: "facial-expressions",
      group: "two",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Sad", value: "sad" },
        { label: "Impassive", value: "impassive" },
        { label: "Grimacing", value: "grimacing" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "appearance",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "facial-comment",
      group: "two",
      value: "",
      appears_under: "appearance",
    },
    {
      label: "Eye Contact",
      type: "select",
      field_name: "eye-contact",
      group: "two",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Avoids Eye Contact", value: "avoids-eye-contact" },
        { label: "Stares Into Space", value: "stares-into-space" },
        { label: "Glances About Suspiciously", value: "glances-about-suspiciously" },
        { label: "Others", value: "others" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "appearance",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "eye-contact-comment",
      group: "two",
      value: "",
      appears_under: "appearance",
    },
    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   value: "",
    // },

    {
      id: "motor_behavior",
      label: "Motor Behavior",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "Motor Activity",
      type: "select",
      field_name: "motor-activity",
      group: "six",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Decreased", value: "decreased" },
        { label: "Increased", value: "increased" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "motor_behavior",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "motor-activity-comment",
      group: "six",
      value: "",
      appears_under: "motor_behavior",
    },
    {
      label: "Level of Anxiety",
      type: "select",
      field_name: "level-anxiety",
      group: "six",
      value: [""],
      options: [
        { label: "Mild", value: "mild" },
        { label: "Moderate", value: "moderate" },
        { label: "Severe", value: "severe" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "motor_behavior",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "anxiety-comment",
      group: "six",
      value: "",
      appears_under: "motor_behavior",
    },

    {
      label: "Level of Agitation",
      type: "select",
      field_name: "level-agitation",
      group: "six",
      value: [""],
      options: [
        { label: "Mild", value: "mild" },
        { label: "Tense", value: "tense" },
        { label: "Restless", value: "restless" },
        { label: "Tends to Wander", value: "tends-to-wander" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "motor_behavior",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "level-agitation-comment",
      group: "six",
      value: "",
      appears_under: "motor_behavior",
    },

    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   value: "",
    // },
    {
      id: "speech",
      label: "Speech",
      type: "heading",
      fontWeight: "500",
    },

    {
      label: "Production of Speech",
      type: "select",
      field_name: "production-speech",
      group: "eight",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Reduced", value: "reduced" },
        { label: "Excessive", value: "excessive" },
        { label: "Mute", value: "mute" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "speech",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "production-speech-comment",
      group: "eight",
      value: "",
      appears_under: "speech",
    },
    {
      label: "Rate of Speech",
      type: "select",
      field_name: "rate-speech",
      group: "eight",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Reduced", value: "reduced" },
        { label: "Excessive", value: "excessive" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "speech",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "rate-speech-comment",
      group: "eight",
      value: "",
      appears_under: "speech",
    },
    {
      label: "Volume of Speech",
      type: "select",
      field_name: "volume-speech",
      group: "eight",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Soft", value: "soft" },
        { label: "Loud", value: "loud" },
        { label: "Whispered", value: "whispered" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "speech",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "volume-speech-comment",
      group: "eight",
      value: "",
      appears_under: "speech",
    },
    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   value: "",
    // },
    {
      id: "mood_and_affect",
      label: "Mood and Affect",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "Mood",
      type: "select",
      field_name: "mood",
      group: "nine",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Reduced", value: "reduced" },
        { label: "Elated", value: "elated" },
        { label: "Anxious", value: "anxious" },
        { label: "Apathetic", value: "apathetic" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "mood_and_affect",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "mood-comment",
      group: "nine",
      value: "",
      appears_under: "mood_and_affect",
    },
    {
      label: "Affect",
      type: "select",
      field_name: "affect",
      group: "nine",
      value: [""],
      options: [
        { label: "Appropriate", value: "appropriate" },
        { label: "Flat", value: "flat" },
        { label: "Blunted", value: "blunted" },
        { label: "Labile", value: "labile" },
        { label: "Constricted", value: "constricted" },
        { label: "Inappropriate", value: "inappropriate" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "mood_and_affect",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "affect-comment",
      group: "nine",
      value: "",
      appears_under: "mood_and_affect",
    },

    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   value: "",
    // },
    {
      id: "thought_process",
      label: "Thought Process",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "Flow of Thought",
      type: "select",
      field_name: "flow-thought",
      group: "ten",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Tangential", value: "tangential" },
        { label: "Blocking", value: "blocking" },
        { label: "Circumstantial", value: "circumstantial" },
        { label: "Repetitive", value: "repetitive" },
        { label: "Flight of Ideas", value: "flight-of-ideas" },
        { label: "Indecisive", value: "indecisive" },
        { label: "Preservation", value: "preservation" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "thought_process",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "flow-thought-comment",
      group: "ten",
      value: "",
      appears_under: "thought_process",
    },
    {
      label: "Perceptual Function",
      type: "select",
      field_name: "perceptual-function",
      group: "ten",
      value: [""],
      options: [
        { label: "Unremarkable", value: "unremarkable" },
        { label: "Illusions", value: "illusions" },
        { label: "Derealization", value: "derealization" },
        { label: "Depersonalization", value: "depersonalization" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "thought_process",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "perceptual-function-comment",
      group: "ten",
      value: "",
      appears_under: "thought_process",
    },
    {
      label: "Hallucinations",
      type: "select",
      field_name: "hallucinations",
      group: "ten",
      value: [""],
      options: [
        { label: "None", value: "none" },
        { label: "Auditory", value: "auditory" },
        { label: "Visual", value: "visual" },
        { label: "Tactile", value: "tactile" },
        { label: "Gustatory", value: "gustatory" },
        { label: "Olfactory", value: "olfactory" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "thought_process",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "hallucinations-comment",
      group: "ten",
      value: "",
      appears_under: "thought_process",
    },

    {
      label: "Delusions",
      type: "select",
      field_name: "delusions",
      group: "ten",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "Incomplete Information", value: "incomplete-information" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "thought_process",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "delusions-comment",
      group: "ten",
      value: "",
      appears_under: "thought_process",
    },

    {
      label: "Thought Content",
      type: "select",
      field_name: "thought-content",
      group: "ten",
      value: [""],
      options: [
        { label: "Rational", value: "rational" },
        { label: "Depressive", value: "depressive" },
        { label: "Suspicious", value: "suspicious" },
        { label: "Phobias", value: "phobias" },
        { label: "Obessive", value: "obessive" },
        { label: "Somatic Preoccupating", value: "somatic-preoccupating" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "thought_process",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "thought-content-comment",
      group: "ten",
      value: "",
      appears_under: "thought_process",
    },
    {
      label: "Suicidal behaviour",
      type: "select",
      field_name: "suicidal-behaviour",
      group: "ten",
      value: [""],
      options: [
        { label: "None", value: "none" },
        { label: "Ideation", value: "ideation" },
        { label: "Plan", value: "plan" },
        { label: "Gesture", value: "gesture" },
        { label: "Attempt", value: "attempt" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "thought_process",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "suicidal-behaviour-comment",
      group: "ten",
      value: "",
      appears_under: "thought_process",
    },
    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   value: "",
    // },
    {
      id: "bodily_functions",
      label: "Bodily Functions (Describe)",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "Appetite",
      type: "select",
      field_name: "appetite",
      group: "twelve",
      value: [""],
      options: [
        { label: "Normal", value: "normal" },
        { label: "Abnormal", value: "abnormal" },
        { label: "Decreased", value: "decreased" },
        { label: "Increased", value: "increased" },
        { label: "No Complications", value: "no-complications" },
        { label: "Complications as specified", value: "complications-as-specified" },
        { label: "Medication side effect", value: "medication-side-effect" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "bodily_functions",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "appetite-comment",
      group: "twelve",
      value: "",
      appears_under: "bodily_functions",
    },

    {
      label: "Sleep",
      type: "select",
      field_name: "sleep",
      group: "twelve",
      value: [""],
      options: [
        { label: "Normal", value: "normal" },
        { label: "Abnormal", value: "abnormal" },
        { label: "Decreased", value: "decreased" },
        { label: "Increased", value: "increased" },
        { label: "No Complications", value: "no-complications" },
        { label: "Complications as specified", value: "complications-as-specified" },
        { label: "Medication side effect", value: "medication-side-effect" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "bodily_functions",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "sleep-comment",
      group: "twelve",
      value: "",
      appears_under: "bodily_functions",
    },

    {
      label: "Elimination",
      type: "select",
      field_name: "elimination",
      group: "twelve",
      value: [""],
      options: [
        { label: "Normal", value: "normal" },
        { label: "Abnormal", value: "abnormal" },
        { label: "Decreased", value: "decreased" },
        { label: "Increased", value: "increased" },
        { label: "No Complications", value: "no-complications" },
        { label: "Complications as specified", value: "complications-as-specified" },
        { label: "Medication side effect", value: "medication-side-effect" },
        { label: "Other", value: "other" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "bodily_functions",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "elimination-comment",
      group: "twelve",
      value: "",
      appears_under: "bodily_functions",
    },

    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   value: "",
    // },
    {
      id: "intellectual_functioning",
      label: "Intellectual Functioning",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "State of Consciousness",
      type: "select",
      field_name: "state-consciousness",
      group: "fourtenth",
      value: [""],
      options: [
        { label: "Alert", value: "alert" },
        { label: "Clouded", value: "clouded" },
        { label: "Lethargic", value: "lethargic" },
        { label: "Drowsy", value: "drowsy" },
        { label: "Stuporous", value: "stuporous" },
        { label: "Comatose", value: "comatose" },
        { label: "Others", value: "others" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "intellectual_functioning",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "state-consciousness-comment",
      group: "fourtenth",
      value: "",
      appears_under: "intellectual_functioning",
    },

    {
      label: "Sensorium (Oriented)",
      type: "select",
      field_name: "sensorium",
      group: "fourtenth",
      value: [""],
      options: [
        { label: "Time", value: "time" },
        { label: "Place", value: "place" },
        { label: "Person", value: "person" },
        { label: "Others", value: "others" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "intellectual_functioning",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "sensorium-comment",
      group: "fourtenth",
      value: "",
      appears_under: "intellectual_functioning",
    },

    {
      label: "Memory",
      type: "select",
      field_name: "memory",
      group: "fourtenth",
      value: [""],
      options: [
        { label: "Immediate", value: "immediate" },
        { label: "Past", value: "past" },
        { label: "Present", value: "present" },
        { label: "Others", value: "others" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "intellectual_functioning",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "memory-comment",
      group: "fourtenth",
      value: "",
      appears_under: "intellectual_functioning",
    },
    {
      label: "Judgement and Insight",
      type: "select",
      field_name: "reasoning-ability",
      group: "fourtenth",
      value: [""],
      options: [
        { label: "Abstractions", value: "abstractions" },
        { label: "Similarities", value: "similarities" },
        { label: "Others", value: "others" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "intellectual_functioning",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "judgement-insight-comment",
      group: "fourtenth",
      value: "",
      appears_under: "intellectual_functioning",
    },
    {
      label: "Impulse Control",
      type: "select",
      field_name: "reasoning-ability",
      group: "fourtenth",
      value: [""],
      options: [
        { label: "Good", value: "good" },
        { label: "Fair", value: "fair" },
        { label: "Poor", value: "poor" },
        { label: "Others", value: "others" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "intellectual_functioning",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "impulse-comment",
      group: "fourtenth",
      value: "",
      appears_under: "intellectual_functioning",
    },
    {
      label: "Insight",
      type: "select",
      field_name: "reasoning-ability",
      group: "fourtenth",
      value: [""],
      options: [
        { label: "Aware of having a problem", value: "aware-of-having-a-problem" },
        { label: "Will cooperate with treatment", value: "will-cooperate-with-treatment" },
        { label: "Aware of social norms", value: "aware-of-social-norms" },
        { label: "Refused treatment", value: "refused-treatment" },
      ],
      appears_under: "intellectual_functioning",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "insight-comment",
      group: "fourtenth",
      value: "",
      appears_under: "intellectual_functioning",
    },
    {
      label: "Judgement",
      type: "select",
      field_name: "judgement",
      group: "fourtenth",
      value: [""],
      options: [
        { label: "Good", value: "good" },
        { label: "Fair", value: "fair" },
        { label: "Poor", value: "poor" },
        { label: "Others", value: "others" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "intellectual_functioning",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "judgement-comment",
      group: "fourtenth",
      value: "",
      appears_under: "intellectual_functioning",
    },
    {
      label: "Reaction to Interview",
      type: "select",
      field_name: "reaction-interview",
      group: "fourtenth",
      value: [""],
      options: [
        { label: "Cooperative", value: "cooperative" },
        { label: "Uncooperative", value: "uncooperative" },
        { label: "Angry", value: "angry" },
        { label: "Negativistic", value: "negativistic" },
        { label: "Excessive politeness", value: "excessive-politeness" },
        { label: "Silly", value: "silly" },
        { label: "Withdrawn", value: "withdrawn" },
        { label: "Evasive", value: "evasive" },
        { label: "Others", value: "others" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "intellectual_functioning",
    },
    {
      label: "Comments",
      type: "text",
      field_name: "reaction-interview-comment",
      group: "fourtenth",
      value: "",
      appears_under: "intellectual_functioning",
    },
  ],
};

const content_thought: IGenericAppointmentFormCategory = {
  id: "content_thought",
  category: "Content of Thought",
  forms: [
    {
      id: "thought_content",
      label: "Content of Thought (Check if present and describe frequency, intensity, and duration)",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "Suicidal thoughts, intent or plans",
      type: "select",
      field_name: "sucidal-thoughts",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
    },
    {
      label: "Comments",
      type: "text",
      field_name: "sucidal-thoughts-comment",
      group: "two",
      value: "",
    },

    {
      label: "Obsessions",
      type: "select",
      field_name: "obsessions",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "obsessions-comment",
      group: "two",
      value: "",
    },

    {
      label: "Compulsion",
      type: "select",
      field_name: "compulsion",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "compulsion-comment",
      group: "two",
      value: "",
    },

    {
      label: "Suspicious",
      type: "select",
      field_name: "suspicious",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "suspicious-comment",
      group: "two",
      value: "",
    },
    {
      label: "Feelings of unreality",
      type: "select",
      field_name: "feelings",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "feelings-comment",
      group: "two",
      value: "",
    },
    {
      label: "Ideas of hopelessness/worthlessness",
      type: "select",
      field_name: "ideas",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "ideas-comment",
      group: "two",
      value: "",
    },
    {
      label: "Phobias",
      type: "select",
      field_name: "phobias",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "phobias-comment",
      group: "two",
      value: "",
    },
    {
      label: "Thought of running away",
      type: "select",
      field_name: "thought",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "thought-comment",
      group: "two",
      value: "",
    },
    {
      label: "Excessive religiousity",
      type: "select",
      field_name: "religiousity",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "religiousity-comment",
      group: "two",
      value: "",
    },
    {
      label: "Somatic (bodily) complaints",
      type: "select",
      field_name: "somatic",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "somatic-comment",
      group: "two",
      value: "",
    },
    {
      label: "Guilt ideas",
      type: "select",
      field_name: "guilt",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "guilt-comment",
      group: "two",
      value: "",
    },
    {
      label: "Sexual Preoccupation",
      type: "select",
      field_name: "sexual-preoccupation",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "sexual-preoccupation-comment",
      group: "two",
      value: "",
    },
    {
      label: "Blames others",
      type: "select",
      field_name: "blame",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "blame-comment",
      group: "two",
      value: "",
    },
    {
      label: "Delusions (paranoid, somatic, of grandeur, of reference, systematized, or other)",
      type: "select",
      field_name: "delusions-somatic",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "delusions-somatic-comment",
      group: "two",
      value: "",
    },
    {
      label: "Hallucinations (auditory, visual, factory, gustatory, kinesthetic, tactile)",
      type: "select",
      field_name: "hallucinations-somatic",
      group: "two",
      options: PLAIN_YES_NO_OPTIONS,
      value: [""],
    },
    {
      label: "Comments",
      type: "text",
      field_name: "hallucinations-comment",
      group: "two",
      value: "",
    },

    {
      label: "Indicate if client was not assessed on the above information",
      type: "textarea",
      field_name: "indicate",
      group: "four",
      value: "",
    },
  ],
};

const case_management: IGenericAppointmentFormCategory = {
  id: "case_management",
  category: "Case Management",
  forms: [
    {
      label: "Independent living Skills",
      type: "select",
      field_name: "living-skills",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Personal Hygiene",
      type: "select",
      field_name: "personal-hygiene",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to manage money",
      type: "select",
      field_name: "manage-money",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to cope with daily stress",
      type: "select",
      field_name: "sexual-thoughts-answer",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to take prescribed medication without been monitored",
      type: "select",
      field_name: "prescribed-medication",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to move around without assistance",
      type: "select",
      field_name: "move-around",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to access local transportation services",
      type: "select",
      field_name: "local-transportation",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Accessibilty to local recreational facilty",
      type: "select",
      field_name: "local-recreational",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to interact positively with Peers/Adults",
      type: "select",
      field_name: "interact-positively",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to function in structured social setting, job, etc.",
      type: "select",
      field_name: "social-settings",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Vocational or career skills to maintain job",
      type: "select",
      field_name: "vocational-career",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to seek a job without assistance",
      type: "select",
      field_name: "seek-job",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to read and comprehend information",
      type: "select",
      field_name: "comprehend",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Ability to access local resources",
      type: "select",
      field_name: "local-resources",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Access to adequate housing",
      type: "select",
      field_name: "adequate-housing",
      group: "two",
      value: [""],
      options: [
        { label: "Adequate", value: "adequate" },
        { label: "Good", value: "good" },
        { label: "Below Average", value: "below-average" },
        { label: "Needs Assistance", value: "needs-assistance" },
        { label: "N/A", value: "n/a" },
      ],
    },
    // {
    //   label: "",
    //   type: "text",
    //   field_name: "",
    //   group: "two",
    //   value: "",
    // },
    // {
    //   label: "",
    //   type: "text",
    //   field_name: "",
    //   group: "two",
    //   value: "",
    // },
    // {
    //   label: "",
    //   type: "text",
    //   field_name: "",
    //   group: "two",
    //   value: "",
    // },
    {
      id: "impairment_rating",
      label: "Please rate impairment in functioning or deficit in the following areas:",
      type: "heading",
      fontWeight: "500",
      // field_name: "",
      // group: "five",
      // value: "",
    },
    {
      label: "Does client require case management services?",
      type: "select",
      field_name: "management-service",
      group: "one",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "impairment_rating",
    },
    {
      label: "*If you answered yes, please refer the client to a case manager or Director of Rehabilitation Service.*",
      type: "text",
      field_name: "adequate-housing",
      group: "one",
      value: "",
      appears_under: "impairment_rating",
    },
    {
      label: "What type of services does client or family need? please list those needs:",
      type: "textarea",
      field_name: "service-type",
      group: "four",
      value: "",
      appears_under: "impairment_rating",
    },
  ],
};

// const diagnosis_summary: IGenericAppointmentFormCategory = {
//   id: "diagnosis_summary",
//   category: "Diagnosis Summary",
//   forms: [
//     {
//       label: "Summary",
//       type: "textarea",
//       field_name: "summary",
//       group: "one",
//       value: "",
//     },
//     {
//       label: "",
//       type: "line",
//       field_name: "",
//       group: "one",
//       value: "",
//     },
//     {
//       label: "Recommended Level of Care",
//       type: "select",
//       field_name: "recommended-level",
//       group: "two",

//       value: [""],
//       options: [
//         { label: "Select", value: "select" },
//         { label: "None", value: "none" },
//         { label: "Outpatient treatment", value: "outpatient-treatment" },
//         { label: "Inpatient treatment", value: "inpatient-treatment" },
//         { label: "Individual Therapy", value: "individual-therapy" },
//         { label: "Family Therapy", value: "family-therapy" },
//         { label: "Addiction Treatment", value: "addiction-treatment" },
//         { label: "Laboratory work up", value: "laboratory-work-up" },
//         { label: "Psychological Testings", value: "psychological-testings" },
//         { label: "PCP/ other specialist", value: "pcp-other-specialist" },
//         { label: "Medication Evaluation", value: "medication-evaluation" },
//         { label: "Others", value: "others" },
//       ],
//     },
//     {
//       label: "Referral Status",
//       type: "text",
//       field_name: "referral",
//       group: "two",
//       value: "",
//     },

//     {
//       label: "Signature",
//       type: "text",
//       field_name: "signature",
//       group: "one",
//       value: "",
//     },
//     {
//       label: "Date",
//       type: "date",
//       field_name: "date",
//       group: "one",
//       value: "",
//     },
//   ],
// };

const cage: IGenericAppointmentFormCategory = {
  id: "cage",
  category: "Cage Scale",
  forms: [
    {
      id: "out_of_4_a",
      label: "Alcohol Questions",
      type: "heading",
      // description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      fontWeight: "500",
    },

    {
      label: "Do you drink alcohol?",
      type: "select",
      field_name: "drink-alcohol",
      group: "eight",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label: "Have you ever experimented with drugs?",
      type: "select",
      field_name: "experiment-drugs",
      group: "eight",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
    },
    {
      label:
        "If the patient has experimented with drugs, ask the CAGE-AID questions. If the patient only drinks alcohol, use the CAGE questions.",
      type: "text",
      field_name: "",
      group: "four",
      value: "",
      // fontWeight: "500",
    },
    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   group: "four",
    //   value: "",
    // },

    {
      id: "cage_aid",
      label: "CAGE-AID Questions",
      type: "heading",
      // description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      fontWeight: "500",
    },

    {
      label: "In the last three months, have you felt you should cut down or stop drinking or using drugs?",
      type: "select",
      field_name: "cut-down",
      group: "six",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "cage_aid",
    },
    {
      label:
        "In the last 3 months, has anyone annoyed you or gotten on your nerves by telling you to cut down or stop drinking or using drugs?",
      type: "select",
      field_name: "last-annoyed",
      group: "six",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "cage_aid",
    },
    {
      label: "In the last three months, have you felt guilty or bad about how much you drink or use drugs?",
      type: "select",
      field_name: "3-months-use-drug",
      group: "six",

      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "cage_aid",
    },
    {
      label: "In the last three months, have you been waking up wanting to have an alcoholic drink or use drugs?",
      type: "select",
      field_name: "wanting-alcohol",
      group: "six",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "cage_aid",
    },
    {
      label: "Patient report",
      type: "textarea",
      field_name: "patient-report",
      group: "five",
      value: "",
      appears_under: "cage_aid",
    },
    // {
    //   id: "out_of_4",
    //   label: "out of 4",
    //   type: "heading",
    //   //   fontWeight: "500",
    // },

    {
      id: "impairment",
      label: "Degree of Impairment",
      type: "heading",
      // description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      fontWeight: "500",
    },
    {
      label: "Have you ever tried to cut back?",
      type: "select",
      field_name: "cut-back",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "impairment",
    },
    {
      label: "Has your drinking ever annoyed or angered anyone?",
      type: "select",
      field_name: "drinking-ever",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "impairment",
    },
    {
      label: "Have you ever felt guilty for something you said or did while drinking?",
      type: "select",
      field_name: "felt-guilty",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "impairment",
    },
    {
      label: "Have you ever drank for an eye opener? (Get over a hang over or just start your day)",
      type: "select",
      field_name: "eye-opener",
      group: "two",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
        { label: "N/A", value: "n/a" },
      ],
      appears_under: "impairment",
    },
    {
      label: "Patient reports",
      type: "textarea",
      field_name: "eye-opener",
      group: "one",
      value: "",
      appears_under: "impairment",
    },

    // {
    //   label: "Each affirmative response earns one point. One point indicate a possible problem. Two points indicate a probable problem",
    //   type: "text",
    //   field_name: "",
    //   group: "seven",
    //   value: "",
    //   //   fontWeight: "500",
    //   appears_under: "out_of_4",
    // },
  ],
};

const substance_abuse: IGenericAppointmentFormCategory = {
  id: "substance_abuse",
  category: "Substance Abuse",
  forms: [],
};

/** Appointment assessment static form list */
export const appt_assessment_forms: IGenericAppointmentFormCategory[] = [
  presenting_problem,
  medical_hx,
  developmental_hx,
  education_and_family,
  living,
  mental_status,
  content_thought,
  case_management,
  // diagnosis_summary,
  cage,
  substance_abuse,
];
