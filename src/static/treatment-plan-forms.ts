import { IGenericAppointmentFormCategory } from "@/interfaces";

export const problem: IGenericAppointmentFormCategory = {
  id: "problem",
  category: "Problem",
  forms: [
    {
      label: "Problem",
      type: "text",
      field_name: "problem",
      group: "one",
      value: "",
    },
    {
      label: "Problem Area",
      type: "select",
      field_name: "problem-area",
      group: "two",
      value: [""],
      options: [
        { label: "Chemical Use", value: "chemical use" },
        { label: "Custody/Placement", value: "custody/placement" },
        { label: "School/Work", value: "school/work" },
        { label: "Family Relations", value: "family relations" },
        { label: "Financial Difficulties", value: "financial difficulties" },
        { label: "Legal Issues", value: "legal issues" },
        { label: "Living Situation", value: "living situation" },
        { label: "Mental Health", value: "mental health" },
        { label: "Physical Health", value: "physical health" },
        { label: "Friendship/Social Relations", value: "friendship/social relations" },
      ],
    },
    {
      label: "Degree of Impairment",
      type: "select",
      field_name: "Impairment",
      group: "two",
      value: [""],
      options: [
        { label: "None/NA", value: "none na" },
        { label: "Mild", value: "mild" },
        { label: "Moderate", value: "moderate" },
        { label: "Severe", value: "severe" },
      ],
    },

    {
      label: "Problem Evidenced By",
      type: "textarea",
      field_name: "problem-evidenced",
      group: "six",
      value: "",
      textareaHeight: "105px",
    },
    {
      label: "Goal Statement (in person served own words)",
      type: "textarea",
      field_name: "goal-statement",
      group: "six",
      value: "",
      textareaHeight: "105px",
    },
    {
      label: "Family/Caregiver/Guardian Role in Treatment Planning and Interventions:",
      type: "select",
      field_name: "caregiver-role",
      group: "six",
      value: [""],
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
      ],
    },
    {
      label: "Service Code",
      type: "text",
      field_name: "service-code",
      group: "six",
      value: "",
    },
    {
      label: "Expectations of Service",
      type: "textarea",
      field_name: "expectations-service",
      group: "seven",
      value: "",
      textareaHeight: "118px",
    },

    {
      id: "clinical_summary",
      label: "Clinical Summary",
      type: "heading",
      // description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      fontWeight: "500",
    },

    {
      label: "Describe Client's Strengths & Skills",
      type: "textarea",
      textareaHeight: "95px",
      field_name: "client-strength",
      group: "one",
      value: "",
      appears_under: "clinical_summary",
    },

    {
      label: "Plan Start Date",
      type: "date",
      field_name: "start-date",
      group: "two",
      value: "",
      appears_under: "clinical_summary",
    },
    // {
    //   label: "",
    //   type: "text",
    //   field_name: "",
    //   group: "two",
    //   value: "",
    //   appears_under: "clinical_summary",
    // },
    {
      label: "Level of Care Indicated",
      type: "text",
      field_name: "level-care",
      group: "two",
      value: "",
      appears_under: "clinical_summary",
    },
    {
      label: "Primary Counselor",
      type: "text",
      field_name: "primary-counselor",
      group: "two",
      value: "",
      appears_under: "clinical_summary",
    },
  ],
};

export const appt_treatment_plan_forms: IGenericAppointmentFormCategory[] = [problem];
