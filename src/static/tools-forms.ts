import { IGenericAppointmentFormCategory } from "@/interfaces";

const ANXIETY_OPTIONS = [
  { label: "Not at all", value: "0", is_shaded: false },
  { label: "Several days", value: "1", is_shaded: false },
  { label: "More than half", value: "2", is_shaded: false },
  { label: "Nearly every day", value: "3", is_shaded: false },
];

const YES_NO_OPTIONS = [
  { label: "Yes", value: "1", is_shaded: false },
  { label: "No", value: "0", is_shaded: false },
];

const PLAIN_YES_NO_OPTIONS = [
  { label: "Yes", value: "yes" },
  { label: "No", value: "no" },
  // { label: "N/A", value: "n/a" },
];

const CESDC_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "A little",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Somewhat",
    value: "2",
    is_shaded: false,
  },
  {
    label: "A lot",
    value: "3",
    is_shaded: false,
  },
];

const CESDC_OPTIONS_TWO = [
  {
    label: "Not at all",
    value: "3",
    is_shaded: false,
  },
  {
    label: "A little",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Somewhat",
    value: "1",
    is_shaded: false,
  },
  {
    label: "A lot",
    value: "0",
    is_shaded: false,
  },
];

// const CLIENT_HEALTH_OPTIONS = [
//   {
//     label: "Not at all",
//     value: "0",
//     is_shaded: false,
//   },
//   {
//     label: "Serveral days",
//     value: "1",
//     is_shaded: false,
//   },
//   {
//     label: "More than half the day",
//     value: "2",
//     is_shaded: false,
//   },
//   {
//     label: "Nearly everyday",
//     value: "3",
//     is_shaded: false,
//   },
// ];

// const CLIENT_HEALTH_OPTION_2 = [
//   {
//     label: "Not difficult at all",
//     value: "0",
//     is_shaded: false,
//   },
//   {
//     label: "Somewhat difficult",
//     value: "1",
//     is_shaded: false,
//   },
//   {
//     label: "Very difficult",
//     value: "2",
//     is_shaded: false,
//   },
//   {
//     label: "Extremely difficult",
//     value: "3",
//     is_shaded: false,
//   },
// ];

const MAST_OPTIONS = (yes_val?: number, no_val?: number) => [
  {
    label: "Yes",
    value: (yes_val ?? "1").toString(),
    is_shaded: false,
  },
  {
    label: "No",
    value: (no_val ?? "0").toString(),
    is_shaded: false,
  },
  // {
  //   label: "N/A",
  //   value: "n/a",
  //   is_shaded: false,
  // },
];

const SASSI_OPTIONS = MAST_OPTIONS();
// const SASSI_OPTIONS = [
//   {
//     label: "Never",
//     value: "0",
//     is_shaded: false,
//   },
//   {
//     label: "Once or Twice",
//     value: "1",
//     is_shaded: false,
//   },
//   {
//     label: "Serveral Times",
//     value: "2",
//     is_shaded: false,
//   },
//   {
//     label: "Repeated",
//     value: "3",
//     is_shaded: false,
//   },
// ];

const ASRS_OPTIONS_TWO = [
  {
    label: "Never",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Rarely",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Sometimes",
    value: "2",
    is_shaded: true,
  },
  {
    label: "Often",
    value: "3",
    is_shaded: true,
  },
  {
    label: "Very Often",
    value: "4",
    is_shaded: true,
  },
];

const CHILD_ANXIETY_OPTIONS = [
  {
    label: "Never",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Occasionally",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Half of  the time",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Most of the time",
    value: "3",
    is_shaded: true,
  },
  {
    label: "All of the time",
    value: "4",
    is_shaded: true,
  },
];

const BGQ_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "A little",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Somewhat",
    value: "2",
    is_shaded: false,
  },
  {
    label: "A lot",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Extremely",
    value: "4",
    is_shaded: false,
  },
];

const TPRS_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Just a little",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Quite a bit",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Very much",
    value: "3",
    is_shaded: false,
  },
];

const DAST_1O_OPTIONS = MAST_OPTIONS;

const CHILD_PTSD_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Once in a while",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Half the time",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Almost always",
    value: "3",
    is_shaded: false,
  },
];

const CHILD_PTSD_BOOLEAN_OPTIONS = [
  {
    label: "No",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Yes",
    value: "1",
    is_shaded: false,
  },
];

const SADQ_OPTIONS = [
  {
    label: "Almost never",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Sometimes",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Often",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Nearly always",
    value: "3",
    is_shaded: false,
  },
];

const LDQ_Options = [
  {
    label: "Never",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Sometimes",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Often",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Nearly always",
    value: "3",
    is_shaded: false,
  },
];

const PHQ_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Several days",
    value: "1",
    is_shaded: false,
  },
  {
    label: "More than half the days",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Nearly every day",
    value: "3",
    is_shaded: false,
  },
];

const NIDA_ASSIST_OPTIONS = [
  {
    label: "Never",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Once or Twice",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Monthly",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Weekly",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Daily or Almost Daily",
    value: "4",
    is_shaded: false,
  },
];

const KESSLER_OPTIONS = [
  {
    label: "All of the time",
    value: "5",
    is_shaded: false,
  },
  {
    label: "Most of the time",
    value: "4",
    is_shaded: false,
  },
  {
    label: "Some of the time",
    value: "3",
    is_shaded: false,
  },
  {
    label: "A little of the time",
    value: "2",
    is_shaded: false,
  },
  {
    label: "None of the time",
    value: "1",
    is_shaded: false,
  },
];

const OCIR_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "A little",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Moderately",
    value: "2",
    is_shaded: false,
  },
  {
    label: "A lot",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Extremely",
    value: "4",
    is_shaded: false,
  },
];

const PARENTING_HASSLES_OPTIONS = [
  {
    label: "Rarely",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Sometimes",
    value: "2",
    is_shaded: false,
  },
  {
    label: "A lot",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Constantly",
    value: "4",
    is_shaded: false,
  },
];

// const PARENTING_HASSLES_INTENSITY_OPTIONS = [
//   {
//     label: "Not a hassle",
//     value: "1",
//     is_shaded: false,
//   },
//   {
//     label: "A little hassle",
//     value: "2",
//     is_shaded: false,
//   },
//   {
//     label: "Quite a hassle",
//     value: "3",
//     is_shaded: false,
//   },
//   {
//     label: "A big hassle",
//     value: "4",
//     is_shaded: false,
//   },
// ];

const DSM_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "A little bit",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Moderately",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Quite a bit",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Extremely",
    value: "4",
    is_shaded: false,
  },
];

const SOCIAL_OPTIONS = [
  {
    label: "Very Strongly Disagree",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Strongly Disagree",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Mildly Disagree",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Neutral",
    value: "4",
    is_shaded: false,
  },
  {
    label: "Mildly Agree",
    value: "5",
    is_shaded: false,
  },
  {
    label: "Strongly Agree",
    value: "6",
    is_shaded: false,
  },
  {
    label: "Very Strongly Agree",
    value: "7",
    is_shaded: false,
  },
];

const READINESS_OPTIONS = [
  {
    label: "No, Strongly Disagree",
    value: "1",
    is_shaded: false,
  },
  {
    label: "No, Disagree",
    value: "2",
    is_shaded: false,
  },
  {
    label: "?, Undecided or Unsure",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Yes, agree",
    value: "4",
    is_shaded: false,
  },
  {
    label: "Yes, Strongly Agree",
    value: "5",
    is_shaded: false,
  },
];

const RELATIONSHIP_OPTIONS = [
  {
    label: "Not at all",
    value: "1",
    is_shaded: false,
  },
  {
    label: "A little",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Somewhat",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Quite a bit",
    value: "4",
    is_shaded: false,
  },
  {
    label: "Very much",
    value: "4",
    is_shaded: false,
  },
];

const RELATIONSHIP_OPTIONS_REVERSED = [
  {
    label: "Not at all",
    value: "4",
    is_shaded: false,
  },
  {
    label: "A little",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Somewhat",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Quite a bit",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Very much",
    value: "0",
    is_shaded: false,
  },
];

const ROSENBERG_SCALE_OPTIONS = [
  {
    label: "Strongly Agree",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Agree",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Disagree",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Strongly Disagree",
    value: "0",
    is_shaded: false,
  },
];

const ROSENBERG_SCALE_OPTIONS_REVERSED = [
  {
    label: "Strongly Agree",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Agree",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Disagree",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Strongly Disagree",
    value: "3",
    is_shaded: false,
  },
];

const SOCIAL_PHOBIA_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "A little bit",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Somewhat",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Very much",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Extremely",
    value: "4",
    is_shaded: false,
  },
];

const SOMATIC_OPTIONS = [
  {
    label: "Not bothered at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Bothered a little",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Bothered a lot",
    value: "2",
    is_shaded: false,
  },
];

const TSQ_OPTIONS = [
  {
    label: "YES, at least twice in the past week",
    value: "1",
    is_shaded: false,
  },
  {
    label: "NO",
    value: "0",
    is_shaded: false,
  },
];

const WSAS_OPTIONS = [
  {
    label: "Not at all impaired",
    value: "0",
    is_shaded: false,
  },
  {
    label: "Very slightly impaired",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Slightly impaired",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Definitely impaired",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Markedly impaired",
    value: "4",
    is_shaded: false,
  },
  {
    label: "Very severely impaired",
    value: "5",
    is_shaded: false,
  },
  {
    label: "Severely impaired",
    value: "6",
    is_shaded: false,
  },
  {
    label: "Very severely impaired",
    value: "7",
    is_shaded: false,
  },
  {
    label: "Extremely impaired",
    value: "8",
    is_shaded: false,
  },
];

const PAIN_OPTIONS = [
  {
    label: "0",
    value: "0",
    is_shaded: false,
  },
  {
    label: "1",
    value: "1",
    is_shaded: false,
  },
  {
    label: "2",
    value: "2",
    is_shaded: false,
  },
  {
    label: "3",
    value: "3",
    is_shaded: false,
  },
  {
    label: "4",
    value: "4",
    is_shaded: false,
  },
  {
    label: "5",
    value: "5",
    is_shaded: false,
  },
  {
    label: "6",
    value: "6",
    is_shaded: false,
  },
  {
    label: "7",
    value: "7",
    is_shaded: false,
  },
  {
    label: "8",
    value: "8",
    is_shaded: false,
  },
  {
    label: "9",
    value: "9",
    is_shaded: false,
  },
  {
    label: "10",
    value: "10",
    is_shaded: false,
  },
];

const WHOQOL_OPTION_ONE = [
  {
    label: "Not at all",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Not much",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Moderately",
    value: "3",
    is_shaded: false,
  },
  {
    label: "A great deal",
    value: "4",
    is_shaded: false,
  },
  {
    label: "Completely",
    value: "5",
    is_shaded: false,
  },
];

const WHOQOL_OPTION_TWO = [
  {
    label: "Very poor",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Poor",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Neither poor nor good",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Good",
    value: "4",
    is_shaded: false,
  },
  {
    label: "Very good",
    value: "5",
    is_shaded: false,
  },
];

const WHOQOL_OPTION_THREE = [
  {
    label: "Very dissatisfied",
    value: "1",
    is_shaded: false,
  },
  {
    label: "Dissatisfied",
    value: "2",
    is_shaded: false,
  },
  {
    label: "Neither satisfied nor dissatisfied",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Satisfied",
    value: "4",
    is_shaded: false,
  },
  {
    label: "Very satisfied",
    value: "5",
    is_shaded: false,
  },
];
const WHOQOL_OPTION_FOUR = [
  {
    label: "Not at all",
    value: "1",
    is_shaded: false,
  },
  {
    label: "A little",
    value: "2",
    is_shaded: false,
  },
  {
    label: "A moderate amount",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Very much",
    value: "4",
    is_shaded: false,
  },
  {
    label: "An extreme amount",
    value: "5",
    is_shaded: false,
  },
];
const WHOQOL_OPTION_FOUR_REVERSED = [
  {
    label: "Not at all",
    value: "5",
    is_shaded: false,
  },
  {
    label: "A little",
    value: "4",
    is_shaded: false,
  },
  {
    label: "A moderate amount",
    value: "3",
    is_shaded: false,
  },
  {
    label: "Very much",
    value: "2",
    is_shaded: false,
  },
  {
    label: "An extreme amount",
    value: "1",
    is_shaded: false,
  },
];

const CHILD_CPSS_OPTIONS = [
  {
    label: "Not at all",
    value: "0",
    is_shaded: false,
  },
  {
    label: "1 time",
    value: "1",
    is_shaded: false,
  },
  {
    label: "2–3 times",
    value: "2",
    is_shaded: false,
  },
  {
    label: "4 or more times",
    value: "3",
    is_shaded: false,
  },
  // {
  //   label: "6 or more times a week/almost always",
  //   value: "4",
  //   is_shaded: false,
  // },
];

/////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////// TOOLS FORMS ////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////

const general_anxiety_disorder: IGenericAppointmentFormCategory = {
  id: "general_anxiety_disorder",
  category: "General Anxiety Disorder",
  forms: [
    {
      id: "choose",
      label: "Choose answers from the menu",
      description: "Over the last 2 weeks, how often have you been bothered by any of the following problems?",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "Feeling nervous, anxious, or on edge",
      type: "select",
      field_name: "feeling-nervous",
      group: "two",
      value: [""],
      options: ANXIETY_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "Not being able to stop or control worrying",
      type: "select",
      field_name: "control-worrying",
      group: "two",
      value: [""],
      options: ANXIETY_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "Worrying to much about different things",
      type: "select",
      field_name: "worrying-things",
      group: "two",
      value: [""],
      options: ANXIETY_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "Trouble Relaxing",
      type: "select",
      field_name: "trouble-relaxing",
      group: "two",
      value: [""],
      options: ANXIETY_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "Being so restless that its hard to sit still",
      type: "select",
      field_name: "restless",
      group: "two",
      value: [""],
      options: ANXIETY_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "Becoming easily annoyed or irritable",
      type: "select",
      field_name: "irritable",
      group: "two",
      value: [""],
      options: ANXIETY_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "If you checked off any problems, how difficult have this problems made it for you to do your, take care of things at home, or get along with other people.",
      type: "select",
      field_name: "checked-problems",
      group: "one",
      value: [""],
      options: ANXIETY_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "Feeling afraid as if something awful might happen",
      type: "select",
      field_name: "feeling-afraid-awful",
      group: "one",
      value: [""],
      options: ANXIETY_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "eight",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "eight",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 4, value: "Minimal anxiety" },
        { min: 5, max: 9, value: "Mild anxiety" },
        { min: 10, max: 14, value: "Moderate anxiety" },
        { min: 15, max: 21, value: "Severe anxiety" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-a",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-b",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-b",
      value: "",
    },
    {
      id: "info",
      type: "heading",
      label: "Scoring Range",
      description:
        "A cut-off score of 10 or more is typically used to identify cases that may require further evaluation or clinical intervention.",

      bullets: [
        {
          title: "Scoring range: 0 – 21",
          bullets: ["0-4: Minimal anxiety", "5-9: Mild anxiety", "10-14: Moderate anxiety", "15-21: Severe anxiety"],
        },
      ],
    },
  ],
};

// OLD SUICIDE SCREENING FORM
// const suicide_screening: IGenericAppointmentFormCategory = {
//   id: "suicide_screening",
//   category: "Suicide Screening",
//   forms: [
//     {
//       id: "directions",
//       label: "Directions",
//       type: "heading",
//       fontWeight: "500",
//       description: "Assess each key factor and select the descriptor that BEST describes patient",
//       subDescription: "Note Clinician's subjective risk appraisal.",
//     },
//     {
//       label: "Suicide attempt immediately prior to HFY admission? (within 30 days prior to admit)",
//       type: "select",
//       field_name: "suicide-attempt",
//       group: "two",
//       value: [],
//       options: YES_NO_OPTIONS,
//       options_is_summable: true,
//     },
//     {
//       label: "Suicide Attempt History",
//       type: "select",
//       field_name: "attempt-history",
//       group: "two",
//       value: [],
//       options: [
//         {
//           label: "Past attempt of high lethality",
//           value: "2",
//           is_shaded: false,
//         },
//         {
//           label: "Past attempts of lethality",
//           value: "1",
//           is_shaded: false,
//         },
//         {
//           label: "No Previous attempt",
//           value: "0",
//           is_shaded: false,
//         },
//       ],
//       options_is_summable: true,
//     },
//     {
//       label: "Suicidal Ideation",
//       type: "select",
//       field_name: "suicidal-ideation",
//       group: "two",
//       value: [],
//       options: [
//         {
//           label: "Constant suicidal thoughts",
//           value: "2",
//           is_shaded: false,
//         },
//         {
//           label: "Intermittent or fleeing suicidal thoughts",
//           value: "1",
//           is_shaded: false,
//         },
//         {
//           label: "Denies current suicidal thoughts",
//           value: "0",
//           is_shaded: false,
//         },
//       ],
//       options_is_summable: true,
//     },
//     {
//       label: "Suicidal Plan",
//       type: "select",
//       field_name: "suicidal-plan",
//       group: "two",
//       value: [],
//       options: [
//         {
//           label: "Has plan with actual OR potential access to planned method",
//           value: "2",
//           is_shaded: false,
//         },
//         {
//           label: "Has plan without access to planned method",
//           value: "1",
//           is_shaded: false,
//         },
//         {
//           label: "No plan",
//           value: "0",
//           is_shaded: false,
//         },
//       ],
//       options_is_summable: true,
//     },
//     {
//       label: "Plan lethality",
//       type: "select",
//       field_name: "plan-lethality",
//       group: "two",
//       value: [],
//       options: [
//         {
//           label: "Highly lethal plan (i.e., gun, hanging, jumping)",
//           value: "2",
//           is_shaded: false,
//         },
//         {
//           label: "Moderate lethality of plan",
//           value: "1",
//           is_shaded: false,
//         },
//         {
//           label: "Low lethality of plan (i.e., biting, holding breath)",
//           value: "0",
//           is_shaded: false,
//         },
//       ],
//       options_is_summable: true,
//     },
//     {
//       label: "Safety Plan Agreement",
//       type: "select",
//       field_name: "safety-plan-agreement",
//       group: "two",
//       value: [],
//       options: [
//         {
//           label: "Unwilling OR unable to agree due to impaired reality testing",
//           value: "2",
//           is_shaded: false,
//         },
//         {
//           label: "Patient is ambivalent and/or guarded",
//           value: "1",
//           is_shaded: false,
//         },
//         {
//           label: "Reliable agrees",
//           value: "0",
//           is_shaded: false,
//         },
//       ],
//       options_is_summable: true,
//     },

//     {
//       label: "Current Morbid Thoughts (i.e. reunion fantasies, preoccupation with death)",
//       type: "select",
//       field_name: "safety-Morbid-Thoughts",
//       group: "two",
//       value: [],
//       options: [
//         {
//           label: "Constantly",
//           value: "2",
//           is_shaded: false,
//         },
//         {
//           label: "Frequency",
//           value: "1",
//           is_shaded: false,
//         },
//         {
//           label: "Rarely",
//           value: "0",
//           is_shaded: false,
//         },
//       ],
//       options_is_summable: true,
//     },
//     {
//       label: "Elopement Risk",
//       type: "select",
//       field_name: "elopement-risk",
//       group: "two",
//       value: [],
//       options: [
//         {
//           label: "High risk",
//           value: "2",
//           is_shaded: false,
//         },
//         {
//           label: "Moderate risk",
//           value: "1",
//           is_shaded: false,
//         },
//         {
//           label: "Low risk",
//           value: "0",
//           is_shaded: false,
//         },
//       ],
//       options_is_summable: true,
//     },

//     {
//       label: "Symptoms - Choose all that apply:",
//       type: "select",
//       field_name: "symptoms-apply",
//       group: "two",
//       value: [],
//       multiple: true,
//       options: [
//         {
//           label: "Hopelessness",
//           value: "hopelessness",
//         },
//         {
//           label: "Helplessness",
//           value: "helplessness",
//         },
//         {
//           label: "Anhedonia",
//           value: "anhedonia",
//         },
//         {
//           label: "Guilt/Shame",
//           value: "guilt/shame",
//         },
//         {
//           label: "Anxiety",
//           value: "anxiety",
//         },
//         {
//           label: "Anger/Rage",
//           value: "anger/rage",
//         },
//         {
//           label: "Insomnia",
//           value: "insomnia",
//         },
//         {
//           label: "Agitation",
//           value: "agitation",
//         },
//         {
//           label: "Impulsivity",
//           value: "impulsivity",
//         },
//       ],
//     },
//     {
//       label: "Symptoms - If other, please specify:",
//       type: "textarea",
//       field_name: "symptoms-apply-answer",
//       group: "two",
//       value: "",
//       textareaHeight: "100px",
//       hideCharCount: true,
//     },

//     {
//       label: "Total Symptoms present:",
//       type: "select",
//       field_name: "total-symptoms-present",
//       group: "two",
//       value: [],
//       options: [
//         {
//           label: "5+ symptoms present",
//           value: "2",
//           is_shaded: false,
//         },
//         {
//           label: "3 - 4 symptoms present",
//           value: "1",
//           is_shaded: false,
//         },
//         {
//           label: "0 - 2 symptoms present",
//           value: "0",
//           is_shaded: false,
//         },
//       ],
//       options_is_summable: true,
//     },
//     {
//       label: "Total",
//       type: "options-total",
//       field_name: "total-count",
//       group: "two",
//       value: "",
//       disabled: true,
//     },

//     {
//       label: "",
//       type: "line",
//       field_name: "",
//       group: "seven",
//       value: "",
//     },
//     {
//       label: "Signature",
//       type: "text",
//       field_name: "asrs-signature",
//       group: "eight",
//       value: "",
//     },
//     {
//       label: "Comments",
//       type: "textarea",
//       field_name: "comments",
//       group: "eight",
//       value: "",
//       textareaHeight: "80px",
//       hideCharCount: true,
//     },

//     {
//       label: "Date",
//       type: "date",
//       field_name: "asrs-date",
//       group: "eight",
//       value: "",
//     },
//   ],
// };

const suicide_screening: IGenericAppointmentFormCategory = {
  id: "suicide_screening",
  category: "Suicide Screening",
  forms: [
    // {
    //   id: "directions",
    //   label: "Directions",
    //   type: "heading",
    //   fontWeight: "500",
    //   description: "Assess each key factor and select the descriptor that BEST describes patient",
    //   subDescription: "Note Clinician's subjective risk appraisal.",
    // },
    {
      id: "section_1",
      type: "heading",
      label: "Section 1: Suicidal Ideation (Past Month)",
      bullets: ["If NO to Q1 and Q2, skip to Section 2.", "If YES to Q1 or Q2, continue with questions 3 to 5."],
    },
    {
      label: "1. Have you wished you were dead or wished you could go to sleep and not wake up?",
      type: "select",
      field_name: "suicide-attempt",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "2. Have you actually had any thoughts about killing yourself?",
      type: "select",
      field_name: "attempt-history",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "3. Have you been thinking about how you might kill yourself?",
      type: "select",
      field_name: "suicidal-ideation",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "4. Have you had these thoughts and had some intention of acting on them?",
      type: "select",
      field_name: "suicidal-plan",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label:
        "5. Have you started to work out or actually worked out the details of how to kill yourself? Do you intend to carry out this plan?",
      type: "select",
      field_name: "plan-lethality",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    ////////////////

    {
      id: "section_2",
      type: "heading",
      label: "Section 2: Suicidal Behavior",
    },
    {
      label: "6a. Have you ever made a suicide attempt?",
      type: "select",
      field_name: "made-suicide-attempt",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "6b. If YES, Was the most recent attempt within the past 3 months?",
      type: "select",
      field_name: "if-yes-within-3-months",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: false,
      appears_under: "section_2",
    },

    {
      label: "6c. if YES, How many total attempts?",
      type: "number",
      field_name: "if-yes-how-many-attempts",
      group: "two",
      value: "",
      min: 0,
      max: 100,
      step: 1,
      appears_under: "section_2",
    },
    {
      label: "6d. if YES, Method(s) used:",
      type: "text",
      field_name: "if-yes-methods-used",
      group: "two",
      value: "",
      appears_under: "section_2",
      // hideCharCount: true,
    },
    {
      label: "7a. Has there been an interrupted suicide attempt?",
      type: "select",
      field_name: "interrupted-attempt",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "7b. If YES, Was this within the past 3 months?",
      type: "select",
      field_name: "7-if-yes-within-3-months-",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: false,
      appears_under: "section_2",
    },

    {
      label: "8a. Has there been an aborted suicide attempt?",
      type: "select",
      field_name: "aborted-attempt",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "8b. If YES, Was this within the past 3 months?",
      type: "select",
      field_name: "8-if-yes-within-3-months-",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: false,
      appears_under: "section_2",
    },
    {
      label:
        "9a. Have you ever done or prepared anything to end your life without actually attempting it? (Examples: writing a suicide note, giving away possessions, collecting pills, purchasing a weapon.)",
      type: "select",
      field_name: "prepared-to-end-life",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "9b. If YES, Was this within the past 3 months?",
      type: "select",
      field_name: "9-if-yes-within-3-months",
      group: "two",
      value: [],
      options: YES_NO_OPTIONS,
      options_is_summable: false,
      appears_under: "section_2",
    },

    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: -1, max: 0, value: "No apparent risk" },
        { min: 1, max: 2, value: "Low risk" },
        { min: 3, max: 5, value: "Moderate risk" },
        { min: 6, max: 9, value: "High risk" },
      ],
    },

    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-a",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },

    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "guideline",
      type: "heading",
      label: "Risk Level Guidelines (Clinician Use or Automated Logic)",
      description: "Use the following to determine level of suicide risk:",
      bullets: [
        { title: "No Apparent Risk", bullets: ["No to all questions (1–9)"] },
        { title: "Low Risk", bullets: ["Yes to Q1 or Q2 only, and", "No plan, no intent, and no behavior (Q3–Q9 all “No”)"] },
        {
          title: "Moderate Risk",
          bullets: [
            "Yes to Q3 or Q4 (method or intent), but",
            "No specific plan (Q5 = No), and",
            "No recent suicidal behavior",
            "OR Yes to Q2 and any lifetime behavior (Q6–Q9), but no recent attempt",
          ],
        },
        {
          title: "High Risk",
          bullets: [
            "Yes to Q5 (specific plan and intent)",
            "OR Yes to any suicidal behavior within past 3 months (Q6–Q9)",
            "OR Yes to Q4 and any recent suicidal behavior",
          ],
        },
      ],
    },

    {
      id: "immediate_action",
      type: "heading",
      label: "Immediate Action Required",
      description: "These are the actions to be taken when the result is not 'No apparent risk'",
      bullets: ["Develop safety plan", "Refer to mental health professional", "Initiate emergency intervention"],
    },
  ],
};

const mood_disorder_form: IGenericAppointmentFormCategory = {
  id: "mood_disorder_form",
  category: "Mood Disorder Questionnaire",
  short_code: "MDQ9",
  forms: [
    {
      id: "instructions",
      label: "Instructions: Select the answer that best applies to you. Please answer each question as best you can.",
      type: "heading",
      fontWeight: "500",
      description: "",
    },

    {
      id: "q_1",
      type: "heading",
      label: "1. Has there ever been a period of time when you were not your usual self and...",
    },

    {
      label:
        "...you felt so good or so hyper that other people thought you were not your normal self or you were so hyper that you got into trouble?",
      type: "select",
      field_name: "felt-good",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...you were so irritable that you shouted at people or started fights or arguments?",
      type: "select",
      field_name: "irritable-fights",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...you felt much more self-confident than usual?",
      type: "select",
      field_name: "self-confident",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...you got much less sleep than usual and found you really didn't miss it?",
      type: "select",
      field_name: "less-sleep",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "... you were much more talkative or spoke faster than usual?",
      type: "select",
      field_name: "spoke-faster",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...thoughts raced through your head or you couldn't slow your mind down?",
      type: "select",
      field_name: "thoughts-raced",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...you were so easily distracted by things around you that you had trouble concentrating or staying on task?",
      type: "select",
      field_name: "easily-distracted",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...you had much more energy than usual?",
      type: "select",
      field_name: "more-energy",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...you were much more active or did many more things than usual?",
      type: "select",
      field_name: "more-active",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "... you were much more social or outgoing than usual, for example, you telephoned friends in the middle of the night?",
      type: "select",
      field_name: "social-outgoing",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...you were much more interested in sex than usual?",
      type: "select",
      field_name: "sex-interested",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...you did things that were unusual for you or that other people might have thought were excessive, foolish, or risky?",
      type: "select",
      field_name: "unusual-things",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },
    {
      label: "...spending money got you or your family in trouble?",
      type: "select",
      field_name: "spending-family-money",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_1",
    },

    {
      label: "2. If you checked YES to more than one of the above, have several of these ever happened during the same period of time?.",
      type: "select",
      field_name: "checked-yes",
      group: "two",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: true,
      appears_under: "q_2",
    },
    {
      label:
        "3. How much of a problem did any of these cause you -- like being able to work; having family, money, or legal troubles; getting into arguments or fights? Please check one response only.",
      type: "select",
      field_name: "legal-troubles",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      appears_under: "q_2",
    },

    {
      label:
        "4. Have any of your blood relatives (ie, children, siblings, parents, grandparents, aunts, uncles,) has manic-depressive illness or bipolar disorder?",
      type: "select",
      field_name: "blood-relatives",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      appears_under: "q_2",
    },
    {
      label: "5. Has a health professional ever told you that you have manic-depressive illness or bipolar disorder?",
      type: "select",
      field_name: "bipolar-disorder",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      appears_under: "q_2",
    },

    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   group: "four",
    //   value: "",
    // },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring_range",
      group: "two-e",
      value: "",
      range_options: [
        { min: 0, max: 6, value: "Negative" },
        { min: 7, max: 20, value: "Positive" },
      ],
    },
    {
      label: "Signature",
      type: "text",
      field_name: "signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring_system",
      type: "heading",
      label: "Scoring System",
      description: "Scoring system used is either a POSITIVE outcome or a NEGATIVE outcome",
      bullets: [
        {
          title: "Interpretation",
          bullets: ["<7 Negative", ">=7 Positive"],
        },

        "Q3, 4, 5 are not part of scoring",
      ],
    },
  ],
};

// const client_health_form: IGenericAppointmentFormCategory = {
//   id: "client_health_form",
//   category: "Client Health Questionnaire - (CHQ)",
//   forms: [
//     {
//       id: "instructions",
//       label: "Over the last two weeks, how often have you been bothered by any of the following problems?",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "1. Little interest or pleasure in doing things:",
//       type: "select",
//       options_is_summable: true,
//       field_name: "little-interest",
//       group: "two",
//       value: [],
//       options: CLIENT_HEALTH_OPTIONS,
//     },
//     {
//       label: "2. Feeling down, depressed or hopeless:",
//       type: "select",
//       options_is_summable: true,
//       field_name: "feeling-depressed",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTIONS,
//     },
//     {
//       label: "3. Trouble falling or staying asleep, or sleeping too much?",
//       type: "select",
//       options_is_summable: true,
//       field_name: "trouble-sleeping",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTIONS,
//     },
//     {
//       label: "4. Feeling tired or having little energy:",
//       type: "select",
//       options_is_summable: true,
//       field_name: "feeling-tired",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTIONS,
//     },
//     {
//       label: "5. Poor appetite or overeating:",
//       type: "select",
//       options_is_summable: true,
//       field_name: "poor-appetite",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTIONS,
//     },
//     {
//       label: "6. Feeling bad about yourself-or that you are a failure or have let yourself or your family down:",
//       type: "select",
//       options_is_summable: true,
//       field_name: "about-yourself",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTIONS,
//     },
//     {
//       label: "7. Trouble concentrating on things, such as reading the newspaper or watching television:",
//       type: "select",
//       options_is_summable: true,
//       field_name: "concentrating",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTIONS,
//     },
//     {
//       label:
//         "8. Moving or speaking slowly that other people noticed. Or the opposite-being so fidgety or restless that you've moved around a lot more than usual:",
//       type: "select",
//       options_is_summable: true,
//       field_name: "speaking-slowly",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTIONS,
//     },
//     {
//       label: "9. Thoughts that you would be better off dead, or hurting yourself in some way:",
//       type: "select",
//       options_is_summable: true,
//       field_name: "thoughts-better",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTIONS,
//     },

//     {
//       label:
//         "10. If you checked off any problems, how difficult have these problems made it for you to do your work, take care of things at home, or get along with other people?",
//       type: "select",
//       options_is_summable: true,
//       field_name: "difficult-problems",
//       group: "two",
//       value: [""],
//       options: CLIENT_HEALTH_OPTION_2,
//     },
//     {
//       label: "",
//       type: "line",
//       field_name: "",
//       group: "seven",
//       value: "",
//     },
//     {
//       label: "Total",
//       type: "options-total",
//       field_name: "total-count",
//       group: "eight",
//       value: "",
//     },
//     {
//       label: "Comments",
//       type: "textarea",
//       field_name: "comments",
//       group: "eight",
//       value: "",
//       textareaHeight: "80px",
//     },
//     {
//       label: "Signature",
//       type: "text",
//       field_name: "asrs-signature",
//       group: "eight",
//       value: "",
//     },
//     {
//       label: "Date",
//       type: "date",
//       field_name: "asrs-date",
//       group: "eight",
//       value: "",
//     },
//   ],
// };

const substance_abuse_crafft: IGenericAppointmentFormCategory = {
  id: "substance_abuse_crafft",
  category: "Substance Abuse Assessment Tool - The CRAFFT Screening Tool",
  name: "The CRAFFT Screening Tool",
  forms: [
    {
      id: "part_a",
      label: "Part A",
      type: "heading",
      description: "During the past 12 months, did you:",
      fontWeight: "500",
      fontSize: "16px",
    },
    {
      label: "1. Drink any alcohol (more than a few sips)?",
      type: "select",
      field_name: "any-alcohol",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      appears_under: "part_a",
    },
    {
      label: "2. Smoke any marijuana or hashish?",
      type: "select",
      field_name: "smoke-marijuana",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      appears_under: "part_a",
    },
    {
      label: "3. Use anything else** to get high?",
      type: "select",
      field_name: "anything-else",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      appears_under: "part_a",
    },
    {
      label: "**”anything else” includes illegal drugs, over the counter and prescription drugs, and things that you “sniff” or “huff”",
      type: "text",
      field_name: "",
      group: "one",
      value: "",
      appears_under: "part_a",
    },
    // {
    //   label: "",
    //   type: "text",
    //   field_name: "",
    //   group: "one",
    //   value: "",
    //   appears_under: "part_a",
    // },
    {
      label:
        "If you answered NO to all (A1, A2, and A3), answer only B1 below, then stop. If you answered YES to any (A1, A2, or A3), answer B1 through B6 below",
      type: "text",
      field_name: "",
      group: "one",
      value: "",
      appears_under: "part_a",
    },

    // {
    //   label: "",
    //   type: "text",
    //   field_name: "",
    //   group: "two",
    //   value: "",
    //   appears_under: "part_a",
    // },
    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   group: "one",
    //   value: "",
    //   appears_under: "part_a",
    // },
    {
      id: "part_b",
      label: "Part B",
      type: "heading",
      description: "During the past 12 months, did you:",
      fontWeight: "500",
      fontSize: "16px",
    },
    {
      label: "1. Have you ever ridden in a CAR driven by someone (including yourself) who was high or been using drugs/alcohol?",
      type: "select",
      field_name: "car",
      group: "six",
      value: [""],
      options: YES_NO_OPTIONS,
      appears_under: "part_b",
      options_is_summable: true,
    },
    {
      label: "2. Do you ever use alcohol or drugs to RELAX, feel better about yourself, or fit in?",
      type: "select",
      field_name: "fit-in",
      group: "six",
      value: [""],
      options: YES_NO_OPTIONS,
      appears_under: "part_b",
      options_is_summable: true,
    },
    {
      label: "3. Do you ever use alcohol or drugs while you are by yourself, or ALONE?",
      type: "select",
      field_name: "alcohol-alone",
      group: "six",
      value: [""],
      options: YES_NO_OPTIONS,
      appears_under: "part_b",
      options_is_summable: true,
    },
    {
      label: "4. Do you ever FORGET things you did while using alcohol or drugs?",
      type: "select",
      field_name: "forget-using",
      group: "six",
      value: [""],
      options: YES_NO_OPTIONS,
      appears_under: "part_b",
    },
    {
      label: "5. Do your FAMILY or FRIENDS ever tell you that you should cut down on your drinking or drug use?",
      type: "select",
      field_name: "family-friends",
      group: "six",
      value: [""],
      options: YES_NO_OPTIONS,
      appears_under: "part_b",
      options_is_summable: true,
    },
    {
      label: "6. Have you ever gotten into TROUBLE while you were using alcohol or drugs?",
      type: "select",
      field_name: "trouble-drugs",
      group: "six",
      value: [""],
      options: YES_NO_OPTIONS,
      appears_under: "part_b",
      options_is_summable: true,
    },

    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "part_b",
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "part_b",
      range_options: [
        { min: -1, max: 0, value: "No significant risk – reinforce healthy choices" },
        { min: 1, max: 2, value: "Moderate risk – brief intervention recommended" },
        { min: 3, max: 1000, value: "High risk – further assessment or referral recommended" },
      ],
    },
    {
      label: "Clinician Signature",
      type: "text",
      field_name: "clinician-signature",
      group: "two-f",
      value: "",
      appears_under: "part_b",
    },
    {
      label: "Date",
      type: "date",
      field_name: "signature-date",
      group: "two-f",
      value: "",
      appears_under: "part_b",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0 No significant risk – reinforce healthy choices",
        "1-2 Moderate risk – brief intervention recommended",
        ">=3 High risk – further assessment or referral recommended",
      ],
    },
  ],
};

const substance_abuse_mast: IGenericAppointmentFormCategory = {
  id: "substance_abuse_mast",
  category: "Substance Abuse Assessment Tool - (MAST)",
  name: "Michigan Alcohol Screen Test",
  short_code: "MAST",
  forms: [
    {
      label: "1. Do you feel you are a normal drinker? (“Normal” means drinking about as much or less than most other people.)",
      type: "select",
      field_name: "feel-normal-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(0, 2),
      options_is_summable: true,
    },

    {
      label:
        "2. Have you ever awakened the morning after drinking the night before and found that you could not remember a part of the evening?",
      type: "select",
      field_name: "after-drinking-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "3. Does any near relative or close friend ever worry or complain about your drinking?",
      type: "select",
      field_name: "relative-friend-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "4. Can you stop drinking without difficulty after one or two drinks?",
      type: "select",
      field_name: "stop-drinking-difficulty",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(0, 2),
      options_is_summable: true,
    },
    {
      label: "5. Do you ever feel guilty about your drinking?",
      type: "select",
      field_name: "guilty-drinking-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "6. Have you ever attended a meeting of Alcoholics Anonymous (AA)?",
      type: "select",
      field_name: "alcoholics-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "7. Have you ever gotten into physical fights while drinking?",
      type: "select",
      field_name: "physical-fights-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "8. Has drinking ever created problems between you and a close relative or close friend?",
      type: "select",
      field_name: "created-problems-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "9. Has any family member or close friend gone to anyone for help about your drinking problem?",
      type: "select",
      field_name: "family-drinking-problems-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "10. Have you ever lost friends because of your drinking?",
      type: "select",
      field_name: "lost-friends-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "11. Have you ever gotten into trouble at work because of drinking?",
      type: "select",
      field_name: "work-trouble-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "12. Have you ever lost a job because of drinking?",
      type: "select",
      field_name: "lost-job-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "13. Have you ever neglected your obligations, family, or work for two or more days in a row because you were drinking?",
      type: "select",
      field_name: "neglected-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "14. Do you drink before noon fairly often?",
      type: "select",
      field_name: "before-noon-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },

    {
      label: "15. Have you ever been told you have liver trouble, such as cirrhosis?",
      type: "select",
      field_name: "liver-trouble-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label:
        "16. After heavy drinking, have you ever had delirium tremens (DT's), severe shaking, visual or auditory (hearing) hallucinations?",
      type: "select",
      field_name: "heavy-drinking-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "17. Have you ever gone to anyone for help about your drinking?",
      type: "select",
      field_name: "help-drinking-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "18. Have you ever been hospitalized because of drinking?",
      type: "select",
      field_name: "hospitalized-drinking-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "19. Has your drinking ever resulted in your being hospitalized in a psychiatric ward?",
      type: "select",
      field_name: "psychiatric-ward-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label:
        "20. Have you ever gone to any doctor, social worker, clergyman, or mental health clinic for help with any emotional problem in which drinking was part of the problem?",
      type: "select",
      field_name: "clergyman-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },
    {
      label: "21. Have you been arrested more than once for driving under the influence of alcohol?",
      type: "select",
      field_name: "arrested-select",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(2, 0),
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 3, value: "No apparent alcohol problem" },
        { min: 4, max: 5, value: "Early or mild alcohol problem" },
        { min: 6, max: 9, value: "Moderate alcohol problem" },
        { min: 10, max: 1000, value: "Severe alcohol problem / likely Alcohol Use Disorder (AUD)" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0–3 points: No apparent alcohol problem",
        "4–5 points: Early or mild alcohol problem",
        "6–9 points: Moderate alcohol problem",
        "10+ points: Severe alcohol problem / likely Alcohol Use Disorder (AUD)",
      ],
    },
  ],
};

const gambling_screening: IGenericAppointmentFormCategory = {
  id: "gambling_screening",
  // category: "Gambling Screening",
  category: "South Oaks Gambling Screening",
  name: "Gambling Risk Assessment",
  short_code: "SOGS",
  forms: [
    {
      label: "1. Have you ever felt you had a problem with gambling?",
      type: "select",
      field_name: "problem-with-gambling",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },

    {
      label: "2. Have you ever gambled more than you intended to?",
      type: "select",
      field_name: "more-than-intended-to",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label:
        "3. Have people criticized your betting or told you that you had a gambling problem, regardless of whether you thought it was true?",
      type: "select",
      field_name: "criticized-your-betting",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "4. Have you ever felt guilty about the way you gamble or what happens when you gamble?",
      type: "select",
      field_name: "felt-guilty-about-gambling",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "5. Have you ever felt like you would like to stop betting money, but didn't think you could?",
      type: "select",
      field_name: "felt-like-stop-betting",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label:
        "6. Have you ever hidden betting slips, lottery tickets, gambling money, or other signs of betting from your spouse, children, or other important people in your life?",
      type: "select",
      field_name: "hidden-betting-slips",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "7. Have you ever argued with people you live with over how you handle money?",
      type: "select",
      field_name: "argued-with-people",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "8. Have you ever missed time from work or school due to gambling?",
      type: "select",
      field_name: "missed-time-work",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "9. Have you ever borrowed from someone and not paid them back as a result of gambling?",
      type: "select",
      field_name: "borrowed-money",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "10. Have you ever borrowed money or sold anything to finance your gambling?",
      type: "select",
      field_name: "borrowed-money-financing",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },

    {
      id: "fin_gam_section",
      type: "heading",
      label: "Financial/Gambling Behavior",
    },
    {
      label: "11. Have you ever borrowed money to gamble from:",
      type: "select",
      field_name: "borrowed-money-gamble-from",
      group: "two",
      value: [""],
      options: [
        { label: "Credit cards", value: "1" },
        { label: "Loan company", value: "01" },
        { label: "Pawn shop", value: "001" },
        { label: "Check cashing service", value: "0001" },
        { label: "Family/friends", value: "00001" },
        { label: "Other", value: "000001" },
      ],
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "12. Have you ever written a bad check or taken money that didn’t belong to you to pay for gambling?",
      type: "select",
      field_name: "written-bad-check",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "13. Has your gambling ever caused serious or repeated problems in your relationships or home life?",
      type: "select",
      field_name: "gambling-serious-problems",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "14. Have you ever gambled to get money to pay bills or solve financial difficulties?",
      type: "select",
      field_name: "gambling-money-pay-bills",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },

    {
      label: "Have you ever considered suicide as a result of your gambling?",
      type: "select",
      field_name: "considered-suicide",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "16. Have you ever been arrested or had legal trouble related to your gambling?",
      type: "select",
      field_name: "legal-trouble-gambling",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "17. Has your gambling ever affected your reputation?",
      type: "select",
      field_name: "affected-reputation",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "18. Have you ever gambled longer than you planned?",
      type: "select",
      field_name: "gamble-longer-planned",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "19. Have you ever used gambling to escape worry, trouble, or loneliness?",
      type: "select",
      field_name: "gamble-to-escape-worry",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "20. Have you ever tried to win back money you lost (chasing losses)?",
      type: "select",
      field_name: "chasing-losses",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
      appears_under: "fin_gam_section",
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
      appears_under: "fin_gam_section",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 2, value: "No apparent gambling problem (recreational gambler)" },
        { min: 3, max: 4, value: "Problem gambling – At-risk gambler; may be experiencing early consequences" },
        {
          min: 5,
          max: 1000,
          value: "Probable pathological gambling – Strong likelihood of gambling disorder; full diagnostic assessment recommended",
        },
      ],
      appears_under: "fin_gam_section",
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
      appears_under: "fin_gam_section",
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
      appears_under: "fin_gam_section",
    },
    {
      label: "Date",
      type: "date",
      field_name: "date",
      group: "two-f",
      value: "",
      appears_under: "fin_gam_section",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0 - 2: No apparent gambling problem (recreational gambler)",
        "3 - 4: Problem gambling – At-risk gambler; may be experiencing early consequences",
        "5 or more: Probable pathological gambling – Strong likelihood of gambling disorder; full diagnostic assessment recommended",
      ],
    },
  ],
};

// OLD GAMBLING SCREENING FORM
// const gambling_screening: IGenericAppointmentFormCategory = {
//   id: "gambling_screening",
//   // category: "Gambling Screening",
//   category: "South Oaks Gambling Screening",
//   name: "Gambling Risk Assessment",
//   short_code: "SOGS",
//   forms: [
//     {
//       id: "when_did_behavior",
//       label: "When you did the behavior",
//       type: "heading",
//       description:
//         "1. Please indicate which of the following types of gambling you have done. For each type, mark one answer which describes the last time you performed each listed behavior and how often you did the behaviour. If you check “not at all” simply go on to the next item as you will not need to report “how often”.",
//       fontWeight: "500",
//     },

//     /// DID GAMBLING 1
//     {
//       id: "did_gambling_1",
//       label: "Did any kind of gambling",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__gambling_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "did_gambling_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__gambling_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "did_gambling_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__gambling_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "did_gambling_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__gambling_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "did_gambling_1",
//     },

//     /// PLAYED CARDS 1

//     {
//       id: "played_cards_1",
//       label: "Played cards for money (such as whot, poker, or other card games",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__played_cards_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_cards_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__played_cards_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_cards_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__played_cards_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_cards_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__played_cards_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_cards_1",
//     },

//     /// BET ON HORSES 1
//     {
//       id: "bet_on_horses_1",
//       label: "Bet on horses, dogs, or other animals (at OTB, the track, or with a bookie) for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__bet_on_horses_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_horses_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__bet_on_horses_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_horses_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__bet_on_horses_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_horses_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__bet_on_horses_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_horses_1",
//     },

//     /// BET ON SPORTS 1
//     {
//       id: "bet_on_sports_1",
//       label: "Bet on sports for money (including basketball, football, parlay cards, Jai Alai, or other sports) with friends, bookie, etc.",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__bet_on_sports_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_sports_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__bet_on_sports_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_sports_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__bet_on_sports_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_sports_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__bet_on_sports_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_sports_1",
//     },

//     /// PLAYED DICE GAMES 1
//     {
//       id: "played_dice_games_1",
//       label: "Played dice games (including craps, over and under, or other dice games) for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__played_dice_games_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_dice_games_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__played_dice_games_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_dice_games_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__played_dice_games_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_dice_games_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__played_dice_games_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_dice_games_1",
//     },

//     /// GAMBLING IN CASINO 1
//     {
//       id: "gambled_in_casino_1",
//       label: "Gambled in a casino or on a casino boat (legal or otherwise) for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__gambled_in_casino_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_in_casino_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__gambled_in_casino_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_in_casino_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__gambled_in_casino_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_in_casino_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__gambled_in_casino_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_in_casino_1",
//     },

//     /// PLAYED BET LOTTERIES 1
//     {
//       id: "played_bet_lotteries_1",
//       label: "Played the numbers or bet on lotteries, Kino, or Quick Draw, played bingo for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__played_bet_lotteries_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_bet_lotteries_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__played_bet_lotteries_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_bet_lotteries_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__played_bet_lotteries_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_bet_lotteries_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__played_bet_lotteries_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_bet_lotteries_1",
//     },

//     /// PLAYED THE STOCK MARKET 1
//     {
//       id: "played_the_stock_market_1",
//       label: "Played the stock, options, and/or commodities market",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__played_the_stock_market_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_the_stock_market_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__played_the_stock_market_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_the_stock_market_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__played_the_stock_market_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_the_stock_market_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__played_the_stock_market_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_the_stock_market_1",
//     },

//     /// PLAYED SLOT MACHINES 1
//     {
//       id: "played_slot_machines_1",
//       label: "Played slot machines, poker machines, or other gambling machines",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__played_slot_machines_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_slot_machines_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__played_slot_machines_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_slot_machines_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__played_slot_machines_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_slot_machines_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__played_slot_machines_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_slot_machines_1",
//     },

//     /// BOWLED SHOT POOL 1
//     {
//       id: "bowled_shot_pool_1",
//       label: "Bowled, shot pool, played golf or darts, or some other game of skill for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__bowled_shot_pool_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bowled_shot_pool_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__bowled_shot_pool_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bowled_shot_pool_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__bowled_shot_pool_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bowled_shot_pool_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__bowled_shot_pool_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bowled_shot_pool_1",
//     },

//     /// PULL PAPER GAMES 1
//     {
//       id: "pull_paper_games_1",
//       label: "Pull tabs or “paper” games other than lotteries (such as Lucky 7's)",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__pull_paper_games_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "pull_paper_games_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__pull_paper_games_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "pull_paper_games_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__pull_paper_games_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "pull_paper_games_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__pull_paper_games_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "pull_paper_games_1",
//     },

//     /// GAMBLED AND USED ALCOHOL 1
//     {
//       id: "gambled_and_used_alcohol_1",
//       label: "Gambled and used alcohol or drugs at the same time",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__gambled_and_used_alcohol_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_and_used_alcohol_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__gambled_and_used_alcohol_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_and_used_alcohol_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__gambled_and_used_alcohol_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_and_used_alcohol_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__gambled_and_used_alcohol_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_and_used_alcohol_1",
//     },

//     /// SOME FORM OF GAMBLING 1
//     {
//       id: "some_form_of_gambling_1",
//       label: "Some form of gambling not listed above",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Not at All",
//       type: "select",
//       field_name: "not_at_all__some_form_of_gambling_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "some_form_of_gambling_1",
//     },
//     {
//       label: "More than one year ago ",
//       type: "select",
//       field_name: "more_than_one_year__some_form_of_gambling_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "some_form_of_gambling_1",
//     },
//     {
//       label: "Less than one year ago",
//       type: "select",
//       field_name: "less_than_one_year__some_form_of_gambling_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "some_form_of_gambling_1",
//     },
//     {
//       label: "In the past six months",
//       type: "select",
//       field_name: "past_six_months__some_form_of_gambling_1",
//       group: "r4",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "some_form_of_gambling_1",
//     },

//     {
//       label: "(please specify):",
//       type: "text",
//       field_name: "specify-above",
//       group: "one",
//       value: "",
//       appears_under: "some_form_of_gambling_1",
//       // value: [""],
//       // options: PLAIN_YES_NO_OPTIONS,
//     },

//     /////////////////////////////////////////////////////////////////////////////

//     {
//       id: "how_often",
//       label: "How often?",
//       description:
//         "For each type, mark one answer which describes how often you did the behaviour. If you check “not at all” for when you did the behavior, simply go on to the next item as you will not need to report “how often”.",
//       type: "heading",
//       fontWeight: "500",
//     },

//     /// DID GAMBLING 2
//     {
//       id: "did_gambling_2",
//       label: "Did any kind of gambling",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__gambling_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "did_gambling_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__gambling_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "did_gambling_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__gambling_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "did_gambling_2",
//     },

//     /// PLAYED CARDS 2
//     {
//       id: "played_cards_2",
//       label: "Played cards for money (such as whot, poker, or other card games)",
//       type: "heading",
//       fontWeight: "500",
//     },

//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__played_cards_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_cards_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__played_cards_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_cards_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__played_cards_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_cards_2",
//     },

//     /// BET ON HORSES 2

//     {
//       id: "bet_on_horses_2",
//       label: "Bet on horses, dogs, or other animals (at OTB, the track, or with a bookie) for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__bet_on_horses_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_horses_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__bet_on_horses_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_horses_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__bet_on_horses_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_horses_2",
//     },

//     /// BET ON SPORTS 2
//     {
//       id: "bet_on_sports_2",
//       label: "Bet on sports for money (including basketball, football, parlay cards, Jai Alai, or other sports) with friends, bookie, etc.",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__bet_on_sports_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_sports_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__bet_on_sports_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_sports_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__bet_on_sports_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bet_on_sports_2",
//     },

//     /// PLAYED DICE GAMES 2
//     {
//       id: "played_dice_games_2",
//       label: "Played dice games (including craps, over and under, or other dice games) for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__played_dice_games_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_dice_games_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__played_dice_games_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_dice_games_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__played_dice_games_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_dice_games_2",
//     },

//     /// GAMBLING IN CASINO 2
//     {
//       id: "gambled_in_casino_2",
//       label: "Gambled in a casino or on a casino boat (legal or otherwise) for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__gambled_in_casino_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_in_casino_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__gambled_in_casino_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_in_casino_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__gambled_in_casino_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_in_casino_2",
//     },

//     /// PLAYED BET LOTTERIES 2
//     {
//       id: "played_bet_lotteries_2",
//       label: "Played the numbers or bet on lotteries, Kino, or Quick Draw, played bingo for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__played_bet_lotteries_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_bet_lotteries_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__played_bet_lotteries_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_bet_lotteries_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__played_bet_lotteries_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_bet_lotteries_2",
//     },

//     /// PLAYED THE STOCK MARKET 2
//     {
//       id: "played_the_stock_market_2",
//       label: "Played the stock, options, and/or commodities market",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__played_the_stock_market_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_the_stock_market_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__played_the_stock_market_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_the_stock_market_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__played_the_stock_market_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_the_stock_market_2",
//     },

//     /// PLAYED SLOT MACHINES 2
//     {
//       id: "played_slot_machines_2",
//       label: "Played slot machines, poker machines, or other gambling machines",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__played_slot_machines_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_slot_machines_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__played_slot_machines_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_slot_machines_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__played_slot_machines_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "played_slot_machines_2",
//     },

//     /// BOWLED SHOT POOL 2
//     {
//       id: "bowled_shot_pool_2",
//       label: "Bowled, shot pool, played golf or darts, or some other game of skill for money",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__bowled_shot_pool_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bowled_shot_pool_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__bowled_shot_pool_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bowled_shot_pool_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__bowled_shot_pool_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "bowled_shot_pool_2",
//     },

//     /// PULL PAPER GAMES 2
//     {
//       id: "pull_paper_games_2",
//       label: "Pull tabs or “paper” games other than lotteries (such as Lucky 7's)",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__pull_paper_games_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "pull_paper_games_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__pull_paper_games_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "pull_paper_games_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__pull_paper_games_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "pull_paper_games_2",
//     },

//     /// GAMBLED AND USED ALCOHOL 2
//     {
//       id: "gambled_and_used_alcohol_2",
//       label: "Gambled and used alcohol or drugs at the same time",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__gambled_and_used_alcohol_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_and_used_alcohol_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__gambled_and_used_alcohol_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_and_used_alcohol_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__gambled_and_used_alcohol_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "gambled_and_used_alcohol_2",
//     },

//     /// SOME FORM OF GAMBLING 2
//     {
//       id: "some_form_of_gambling_2",
//       label: "Some form of gambling not listed above",
//       type: "heading",
//       fontWeight: "500",
//     },
//     {
//       label: "Less than one time per week",
//       type: "select",
//       field_name: "less_than_one_time__some_form_of_gambling_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "some_form_of_gambling_2",
//     },
//     {
//       label: "One to two times per week",
//       type: "select",
//       field_name: "one_or_two_times__some_form_of_gambling_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "some_form_of_gambling_2",
//     },
//     {
//       label: "Three or more times per week",
//       type: "select",
//       field_name: "three_or_more_times__some_form_of_gambling_2",
//       group: "three",
//       value: [""],
//       options: PLAIN_YES_NO_OPTIONS,
//       appears_under: "some_form_of_gambling_2",
//     },

//     {
//       label: "(please specify):",
//       type: "text",
//       field_name: "specify-above-2",
//       group: "one",
//       value: "",
//       appears_under: "some_form_of_gambling_2",
//     },
//   ],
// };

const substance_abuse_dast: IGenericAppointmentFormCategory = {
  id: "substance_abuse_dast",
  category: "Substance Abuse Assessment Tool - DAST",
  name: "Drug Abuse Screening Test",
  short_code: "DAST",
  forms: [
    {
      label: "1. Have you used drugs other than those required for medical reasons?",
      type: "select",
      field_name: "have_used_drugs",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },

    {
      label: "2. Have you abused prescription drugs?",
      type: "select",
      field_name: "have_abused_prescription",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "3. Do you abuse more than one drug at a time?",
      type: "select",
      field_name: "abuse_more_than_one_drug",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "4. Can you get through the week without using drugs (other than those required for medical reasons)?",
      type: "select",
      field_name: "get_through_week",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(0, 1),
      options_is_summable: true,
    },
    {
      label: "5. Are you always able to stop using drugs when you want to?",
      type: "select",
      field_name: "able_to_stop_using",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(0, 1),
      options_is_summable: true,
    },
    {
      label: "6. Do you abuse drugs on a continuous basis?",
      type: "select",
      field_name: "abuse_drugs_continuously",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(0, 1),
      options_is_summable: true,
    },
    {
      label: "7. Do you try to limit your drug use to certain situations?",
      type: "select",
      field_name: "limit_drug_use",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(0, 1),
      options_is_summable: true,
    },
    {
      label: "8. Have you had “blackouts” or “flashbacks” as a result of drug use?",
      type: "select",
      field_name: "blackouts_flashbacks",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "9. Do you ever feel bad or guilty about your drug use?",
      type: "select",
      field_name: "feel_bad_about_drug_use",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "10. Does your spouse (or parents) ever complain about your involvement with drugs?",
      type: "select",
      field_name: "spouse_complain",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "11. Do your friends or relatives know or suspect you abuse drugs?",
      type: "select",
      field_name: "friends_relatives_know",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "12. Has drug abuse created problems between you and your spouse or your partner?",
      type: "select",
      field_name: "drug_abuse_problems",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "13. Has a family member ever sought help for problems related to your drug use?",
      type: "select",
      field_name: "family_member_seek_help",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },

    {
      label: "14. Have you ever lost friends because of your use of drugs?",
      type: "select",
      field_name: "lost_friends",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "15. Have you ever neglected your family or missed work because of your use of drugs?",
      type: "select",
      field_name: "neglected_family",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "16. Have you been in trouble at work because of your drug abuse?",
      type: "select",
      field_name: "trouble_at_work",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "17. Have you ever lost a job because of drug abuse?",
      type: "select",
      field_name: "lost_job",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "18. Have you ever gotten into fights when under the influence of drugs?",
      type: "select",
      field_name: "physical-fights",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "19. Have you ever been arrested because of unusual behavior while under the influence of drugs?",
      type: "select",
      field_name: "arrested-unusual",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "20. Have you ever been arrested for driving while under the influence of drugs?",
      type: "select",
      field_name: "arrested-driving",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "21. Have engaged in illegal activities in order to obtain drugs?",
      type: "select",
      field_name: "illegal-activities",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "22. Have you ever been arrested for possession of illegal drugs?",
      type: "select",
      field_name: "arrested-illegal",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "23. Have you ever experienced withdrawal symptoms as a result of your heavy drug intake?",
      type: "select",
      field_name: "withdrawal-symptoms",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "24. Have you had medical problems as a result of your drug use?(e.g. memory loss, hepatitis,convultions,bleeding,etc)",
      type: "select",
      field_name: "medical-problems",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "25. Have you ever gone to anyone for help with a drug problem?",
      type: "select",
      field_name: "help-drug-problem",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "26. Have you even been in a hospital for medical problems related to you drug use?",
      type: "select",
      field_name: "hospital-medical-problems",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "27. Have you ever been involved in a treatment program specifically related to your drug use?",
      type: "select",
      field_name: "treatment-program",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "28. Have you been treated as an outpatient for problems related to drug abuse?",
      type: "select",
      field_name: "outpatient-treatment",
      group: "two",
      value: [""],
      options: MAST_OPTIONS(),
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      // group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: -1, max: 0, value: "No problems reported" },
        { min: 1, max: 2, value: "Low level of problems related to drug use" },
        { min: 3, max: 5, value: "Moderate level; further investigation advised" },
        { min: 6, max: 10, value: "Substantial level; intensive assessment recommended" },
        { min: 11, max: 15, value: "Severe level; probable substance use disorder" },
        { min: 16, max: 28, value: "Very severe; strong indication of substance dependence" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      description: "Note: A score of 6 or higher is typically used as a cutoff point indicating a potential drug problem.",
      bullets: [
        "0: No problems reported",
        "1–2: Low level of problems related to drug use",
        "3–5: Moderate level; further investigation advised",
        "6–10: Substantial level; intensive assessment recommended",
        "11–15: Severe level; probable substance use disorder",
        "16–28: Very severe; strong indication of substance dependence",
      ],
    },
  ],
};

const substance_abuse_sassi: IGenericAppointmentFormCategory = {
  id: "substance_abuse_sassi",
  category: "Substance Abuse Assessment Tool - SASSI",
  forms: [
    {
      id: "alcohol_fva",
      label: "Alcohol (FVA)",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "1. Had drinks with lunch?",
      type: "select",
      field_name: "had-drinks",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. Taken a drink or drinks to help express your feelings or ideas?",
      type: "select",
      field_name: "taken-drinks",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "3. Taken a drink or drinks to relieve a tired feeling or give you energy to keep going?",
      type: "select",
      field_name: "relieve-drinks",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. Had more to drink than you intended to?",
      type: "select",
      field_name: "trouble-relaxing",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. Experienced physical problems after drinking (nausea, seeing/hearing problems, dizziness)?",
      type: "select",
      field_name: "physical-problems",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "6. Gotten into trouble on the job, in school, or at home because of drinking?",
      type: "select",
      field_name: "trouble-job",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "7. Become depressed after having sobered up?",
      type: "select",
      field_name: "become-depressed",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "8. Argued with your family or friends because of your drinking?",
      type: "select",
      field_name: "argued-family",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "9. Had the effects of drinking recur after not drinking awhile (flashbacks, hallucinations)?",
      type: "select",
      field_name: "effects-drinking",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "10. Had problems in relationships because of your drinking (loss of friends, separation, divorce)?",
      type: "select",
      field_name: "relationships-problem",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "11. Became nervous or had the shakes after having sobered up?",
      type: "select",
      field_name: "become-nervous",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "12. Tried to commit suicide while drunk?",
      type: "select",
      field_name: "commit-suicide",
      group: "two",
      value: [""],
      options: SASSI_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 2, value: "Minimal or no indicators of alcohol-related harm" },
        { min: 3, max: 5, value: "Mild risk – education or brief intervention recommended" },
        { min: 6, max: 8, value: "Moderate risk – may indicate problematic drinking" },
        { min: 9, max: 12, value: "High risk – strong indication of alcohol use disorder; further assessment advised" },
      ],
    },

    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0-2 Minimal or no indicators of alcohol-related harm",
        "3-5 Mild risk – education or brief intervention recommended",
        "6-8 Moderate risk – may indicate problematic drinking",
        "9-12 High risk – strong indication of alcohol use disorder; further assessment advised",
      ],
    },
  ],
};

const asrs: IGenericAppointmentFormCategory = {
  id: "asrs",
  category: "ASRS",
  forms: [
    {
      id: "instructions",
      label: "",
      description:
        "Please answer the questions below, rating yourself on each of the criteria shown using the scale on the right side of the page. As you answer each question, place an X in the box that best describes how you have felt and conducted yourself over the past 6 months. Please give this completed checklist to your healthcare professional to discuss during today’s appointment.",
      fontWeight: "500",
      type: "heading",
    },
    {
      id: "part_a",
      label: "PART A",
      fontWeight: "500",
      type: "heading",
      description: "Scoring system",
      subDescription: "Each item is considered “positive” if the person selects the following:",
      bullets: [
        "1. Often (3) or Very Often (4)",
        "2. Often (3) or Very Often (4)",
        "3. Often (3) or Very Often (4)",
        "4. Often (3) or Very Often (4)",
        "5. Sometimes (2), Often (3), or Very Often (4)",
        "6. Sometimes (2), Often (3), or Very Often (4)",
      ],
    },
    {
      id: "part_a_criteria",
      type: "heading",
      label: "Scoring Instruction",
      bullets: [
        "Count how many of the 6 questions are positive (according to the rules above)",
        "If the individual has 4 or more positive responses, the screening is positive",
        "This means that there is a high likelihood of adult ADHD, and the person should be referred for a full diagnostic evaluation using DSM-5 criteria.",
      ],
    },
    {
      label: "1. How often do you have trouble wrapping up the final details of a project, once the challenging parts have been done?",
      type: "select",
      field_name: "troble-wrapping",
      group: "two",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_a",
    },
    {
      label: "2. How often do you have difficulty getting things in order when you have to do a task that requires organization?",
      type: "select",
      field_name: "order-things",
      group: "two",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_a",
    },
    {
      label: "3. How often do you have problems remembering appointments or obligations?",
      type: "select",
      field_name: "problems-remembering",
      group: "two",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_a",
    },

    {
      label: "4. When you have a task that requires a lot of thought, how often do you avoid or delay getting started?",
      type: "select",
      field_name: "task-requires",
      group: "two",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_a",
    },

    {
      label: "5. How often do you fidget or squirm with your hands or feet when you have to sit down for a long time?",
      type: "select",
      field_name: "fidget-squirm",
      group: "two",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_a",
    },
    {
      label: "6. How often do you feel overly active and compelled to do things, like you were driven by a motor?",
      type: "select",
      field_name: "overly-active",
      group: "two",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_a",
    },
    // PART-B
    {
      id: "part_b",
      label: "PART B",
      fontWeight: "500",
      type: "heading",
      description: "Explores the breadth and intensity of ADHD-related symptoms and behaviors.",
      bullets: [
        {
          title: "NOT SCORED FOR SCREENING",
          bullets: ["Inattentiveness (Items 7–11)", "Hyperactivity-impulsivity (Items 12–18)", "Impact on daily functioning"],
        },
      ],
    },
    {
      label: "7. How often do you make careless mistakes when you have to work on a boring or difficult project?",
      type: "select",
      field_name: "careless-mistakes",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "8. How often do you have difficulty keeping your attention when you are doing boring or repetitive work?",
      type: "select",
      field_name: "difficulty-attention",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "9. How often do you have difficulty concentrating on what people say to you, even when they are speaking to you directly?",
      type: "select",
      field_name: "difficulty-concentrating",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "10. How often do you misplace or have difficulty finding things at home or at work?",
      type: "select",
      field_name: "misplace-finding",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "11. How often are you distracted by activity or noise around you?",
      type: "select",
      field_name: "distracted-activity",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "12. How often do you leave your seat in meetings or other situations in which you are expected to remain seated?",
      type: "select",
      field_name: "meetings-seat",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "13. How often do you feel restless or fidgety?",
      type: "select",
      field_name: "feel-restless",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "14. How often do you have difficulty unwinding and relaxing when you have time to yourself?",
      type: "select",
      field_name: "feel-restless",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "15. How often do you find yourself talking too much when you are in social situations?",
      type: "select",
      field_name: "social-situations",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label:
        "16. When you’re in a conversation, how often do you find yourself finishing the sentences of the people you are talking to, before they can finish them themselves?",
      type: "select",
      field_name: "finishing-sentences",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "17. How often do you have difficulty waiting your turn in situations when turn taking is required?",
      type: "select",
      field_name: "difficulty-waiting",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },
    {
      label: "18. How often do you interrupt others when they are busy?",
      type: "select",
      field_name: "interrupt-busy",
      group: "six",
      value: [""],
      options: ASRS_OPTIONS_TWO,
      options_is_summable: true,
      appears_under: "part_b",
    },

    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "eight",
      value: "",
      disabled: true,
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "eight",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "eight",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "eight",
      value: "",
    },
  ],
};

const oasis: IGenericAppointmentFormCategory = {
  id: "oasis",
  category: "Overall Anxiety Severity and Impairment Scale",
  short_code: "OASIS",
  forms: [
    {
      label: "1. In the past week, how often have you felt anxious?",
      type: "select",
      field_name: "often-felt-anxious",
      group: "two",
      value: [""],
      options: [
        {
          label: "No anxiety in the past week.",
          value: "0",
          is_shaded: false,
        },
        {
          label: "Infrequent anxiety. Felt anxious a few times.",
          value: "1",
          is_shaded: false,
        },
        {
          label: "Occasional anxiety. Felt anxious as much of the time as not. It was hard to relax.",
          value: "2",
          is_shaded: false,
        },
        {
          label: "Frequent anxiety. Felt anxious most of the time. It was very diffi cult to relax.",
          value: "3",
          is_shaded: false,
        },
        {
          label: "Constant anxiety. Felt anxious all of the time and never really relaxed.",
          value: "4",
          is_shaded: false,
        },
      ],
      options_is_summable: true,
    },

    {
      label: "2. In the past week, when you have felt anxious, how intense or severe was your anxiety?",
      type: "select",
      field_name: "how-intense-anxious",
      group: "two",
      value: [""],
      options: [
        {
          label: "Little or None: Anxiety was absent or barely noticeable.",
          value: "0",
          is_shaded: false,
        },
        {
          label:
            "Mild: Anxiety was at a low level. It was possible to relax when I tried. Physical symptoms were only slightly uncomfortable.",
          value: "1",
          is_shaded: false,
        },
        {
          label:
            "Moderate: Anxiety was distressing at times. It was hard to relax or concentrate, but I could do it if I tried. Physical symptoms were uncomfortable.",
          value: "2",
          is_shaded: false,
        },
        {
          label:
            "Severe: Anxiety was intense much of the time. It was very diffi cult to relax or focus on anything else. Physical symptoms were extremely uncomfortable.",
          value: "3",
          is_shaded: false,
        },
        {
          label: "Extreme: Anxiety was overwhelming. It was impossible to relax at all. Physical symptoms were unbearable.",
          value: "4",
          is_shaded: false,
        },
      ],
      options_is_summable: true,
    },
    {
      label: "3. In the past week, how often did you avoid situations, places, objects, or activities because of anxiety or fear?",
      type: "select",
      field_name: "situations",
      group: "two",
      value: [""],
      options: [
        {
          label: "None: I do not avoid places, situations, activities, or things because of fear.",
          value: "0",
          is_shaded: false,
        },
        {
          label:
            "Infrequent: I avoid something once in a while, but will usually face the situation or confront the object. My lifestyle is not affected.",
          value: "1",
          is_shaded: false,
        },
        {
          label:
            "Occasional: I have some fear of certain situations, places, or objects, but it is still manageable. My lifestyle has only changed in minor ways. I always or almost always avoid the things I fear when I’m alone, but can handle them if someone comes with me.",
          value: "2",
          is_shaded: false,
        },
        {
          label:
            "Frequent: I have considerable fear and really try to avoid the things that frighten me. I have made signifi cant changes in my lifestyle to avoid the object, situation, activity, or place.",
          value: "3",
          is_shaded: false,
        },
        {
          label:
            "All the Time: Avoiding objects, situations, activities, or places has taken over my life. My lifestyle has been extensively affected and I no longer do things that I used to enjoy.",
          value: "4",
          is_shaded: false,
        },
      ],
      options_is_summable: true,
    },
    {
      label:
        "4. In the past week, how much did your anxiety interfere with your ability to do the things you needed to do at work, at school, or at home?",
      type: "select",
      field_name: "interfere",
      group: "two",
      value: [""],
      options: [
        {
          label: "None: No interference at work/home/<USER>",
          value: "0",
          is_shaded: false,
        },
        {
          label:
            "Mild: My anxiety has caused some interference at work/home/<USER>",
          value: "1",
          is_shaded: false,
        },
        {
          label:
            "Moderate: My anxiety defi nitely interferes with tasks. Most things are still getting done, but few things are being done as well as in the past.",
          value: "2",
          is_shaded: false,
        },
        {
          label:
            "Severe: My anxiety has really changed my ability to get things done. Some tasks are still being done, but many things are not. My performance has defi nitely suffered.",
          value: "3",
          is_shaded: false,
        },
        {
          label:
            "Extreme: My anxiety has become incapacitating. I am unable to complete tasks and have had to leave school, have quit or been fi red from my job, or have been unable to complete tasks at home and have faced consequences like bill collectors, eviction, etc.",
          value: "4",
          is_shaded: false,
        },
      ],
      options_is_summable: true,
    },
    {
      label: "5. In the past week, how much has anxiety interfered with your social life and relationships?",
      type: "select",
      field_name: "relationships",
      group: "two",
      value: [""],
      options: [
        {
          label: "None: My anxiety doesn’t affect my relationships.",
          value: "0",
          is_shaded: false,
        },
        {
          label:
            "Mild: My anxiety slightly interferes with my relationships. Some of my friendships and other relationships have suffered, but, overall, my social life is still fulfi lling.",
          value: "1",
          is_shaded: false,
        },
        {
          label:
            "Moderate: I have experienced some interference with my social life, but I still have a few close relationships. I don’t spend as much time with others as in the past, but I still socialize sometimes.",
          value: "2",
          is_shaded: false,
        },
        {
          label:
            "Severe: My friendships and other relationships have suffered a lot because of anxiety. I do not enjoy social activities. I socialize very little.",
          value: "3",
          is_shaded: false,
        },
        {
          label:
            "Extreme: My anxiety has completely disrupted my social activities. All of my relationships have suffered or ended. My family life is extremely strained.",
          value: "4",
          is_shaded: false,
        },
      ],
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 7, value: "Minimal to mild anxiety — may not require treatment" },
        { min: 8, max: 12, value: "Moderate anxiety — possible clinical significance; consider follow-up" },
        { min: 13, max: 20, value: "Severe anxiety — likely clinical impairment; recommend treatment or referral" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0–7 Minimal to mild anxiety — may not require treatment",
        "8–12 Moderate anxiety — possible clinical significance; consider follow-up",
        "13–20 Severe anxiety — likely clinical impairment; recommend treatment or referral",
      ],
    },
  ],
};

const severity_measure_for_child_anxiety: IGenericAppointmentFormCategory = {
  id: "severity_measure_for_child_anxiety",
  category: "Severity Measure for Generalized Anxiety Disorder---Child Age 11–17",
  forms: [
    {
      id: "instructions",
      label:
        "The following questions ask about thoughts, feelings, and behaviors, often tied to concerns about family, health, finances, school, and work.",
      description: "During the PAST 7 DAYS, I have...",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "1. felt moments of sudden terror, fear, or  fright ",
      type: "select",
      field_name: "sudden-terror",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "2. felt anxious, worried, or nervous",
      type: "select",
      field_name: "sudden-terror",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "3. had thoughts of bad things happening, such  as family tragedy, ill health, loss of a job, or  accidents",
      type: "select",
      field_name: "thoughts-bad-things",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "4. felt a racing heart, sweaty, trouble  breathing, faint, or shaky",
      type: "select",
      field_name: "racing-heart",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "5. felt tense muscles, felt on edge or ",
      type: "select",
      field_name: "felt-tense-muscles",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "6. avoided, or did not approach or enter, situations about which I worry",
      type: "select",
      field_name: "avoided-situations",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "7. left situations early or participated only  minimally due to worries ",
      type: "select",
      field_name: "left-participated",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. spent lots of time making decisions, putting  off making decisions, or preparing for  situations, due to worries",
      type: "select",
      field_name: "time-making-decisions",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "9. sought reassurance from others due to  worries",
      type: "select",
      field_name: "reassurance-others",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "10. needed help to cope with anxiety (e.g.,  alcohol or medication, superstitious  objects, or other people)",
      type: "select",
      field_name: "reassurance-others",
      group: "two",
      value: [""],
      options: CHILD_ANXIETY_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 9, value: "Minimal anxiety" },
        { min: 10, max: 19, value: "Mild to moderate" },
        { min: 20, max: 29, value: "Moderate to severe" },
        { min: 30, max: 40, value: "Severe anxiety (possible GAD)" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["0–9 Minimal anxiety", "10–19 Mild to moderate", "20–29 Moderate to severe", "30–40 Severe anxiety (possible GAD)"],
    },
  ],
};

const brief_grief_questionnaire: IGenericAppointmentFormCategory = {
  id: "pdg__formally(bgq)",
  // category: "Brief Grief Questionnaire - BGQ",
  category: "Prolonged Grief Disorder Screener",
  name: "Prolonged Grief Disorder Screener",
  short_code: "PGD",
  forms: [
    {
      label: "1. How much are you having trouble accepting the death of",
      type: "select",
      field_name: "accepting-death",
      group: "two",
      value: [""],
      options: BGQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. How much does your grief still interfere with your life?",
      type: "select",
      field_name: "grief-interfere",
      group: "two",
      value: [""],
      options: BGQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "3. How much are you having images or thoughts of ________ when he/she died or other thoughts about the death that really bother you?",
      type: "select",
      field_name: "images-thoughts",
      group: "two",
      value: [""],
      options: BGQ_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "4. Are there things you used to do when ________ was alive that you don't feel comfortable doing anymore, that you avoid? Like going somewhere you went with him/her, or doing things you used to enjoy together? Or avoiding looking at pictures or talking about ________ ? How much are you avoiding these things?",
      type: "select",
      field_name: "used-to",
      group: "two",
      value: [""],
      options: BGQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "5. How much are you feeling cut off or distant from other people since ________ died, even people you used to be close to like family or friends?",
      type: "select",
      field_name: "feeling-off",
      group: "two",
      value: [""],
      options: BGQ_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 4, value: "No significant grief" },
        { min: 5, max: 9, value: "Mild grief — monitor" },
        { min: 10, max: 14, value: "10–14 Moderate symptoms — assess for PGD" },
        { min: 15, max: 20, value: "15–20 Severe symptoms — probable Prolonged Grief Disorder" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      description: "Total score range: 0-20",
      bullets: [
        "0–4 No significant grief",
        "5–9 Mild grief — monitor",
        "10-14 Moderate to severe",
        "15–20 Severe symptoms — probable Prolonged Grief Disorder",
      ],
    },
  ],
};

const teacher_parent_rating_scale: IGenericAppointmentFormCategory = {
  id: "tprs",
  category: "Teacher and Parent Rating Scale",
  short_code: "SNAP-IV 26",
  forms: [
    {
      id: "section_1",
      type: "heading",
      label: "Section 1 - (Inattention)",
    },
    {
      label: "1. Often fails to give close attention to details or makes careless mistakes in schoolwork or tasks",
      type: "select",
      field_name: "fails-attention",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "2. Often has difficulty sustaining attention in tasks or play activities",
      type: "select",
      field_name: "difficulty-sustaining",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "3. Often does not seem to listen when spoken to directly",
      type: "select",
      field_name: "listen-directly",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "4. Often does not follow through on instructions and fails to finish schoolwork, chores, or duties",
      type: "select",
      field_name: "instructions-fails",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "5. Often has difficulty organizing tasks and activities",
      type: "select",
      field_name: "difficulty-organizing",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "6. Often avoids, dislikes, or reluctantly engages in tasks requiring sustained mental effort",
      type: "select",
      field_name: "reluctantly-avoids",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "7. Often loses things necessary for activities (e.g., toys, school assignments, pencils, or books)",
      type: "select",
      field_name: "loses-necessary",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "8. Often is distracted by extraneous stimuli",
      type: "select",
      field_name: "distracted-extraneous",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "9. Often is forgetful in daily activities",
      type: "select",
      field_name: "forgetful-activities",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "Subscale Average Score",
      type: "calculation-result",
      field_name: "section_1__result",
      group: "one-a",
      value: "",
      appears_under: "section_1",
      result_of: [
        "fails-attention",
        "difficulty-sustaining",
        "listen-directly",
        "instructions-fails",
        "difficulty-organizing",
        "reluctantly-avoids",
        "loses-necessary",
        "distracted-extraneous",
        "forgetful-activities",
      ],

      calculate_result: (values: number[]) => {
        const sum = values.reduce((acc, curr) => acc + curr, 0);
        return (sum / 9).toFixed(1);
      },
    },

    {
      id: "section_2",
      type: "heading",
      label: "Section 2 - (Hyperactivity/Impulsivity)",
    },

    {
      label: "10. Often fidgets with hands or feet or squirms in seat",
      type: "select",
      field_name: "fidgets-hands",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "11. Often leaves seat in classroom or in other situations in which remaining seated is expected",
      type: "select",
      field_name: "classroom-leaves",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "12. Often runs about or climbs excessively in situations in which it is inappropriate",
      type: "select",
      field_name: "excessively-inappropriate",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "13. Often has difficulty playing or engaging in leisure activities quietly",
      type: "select",
      field_name: "difficulty-leisure",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: `14. Often is "on the go" or often acts as if "driven by a motor"`,
      type: "select",
      field_name: "driven-motor",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "15. Often talks excessively",
      type: "select",
      field_name: "talks-excessively",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "16. Often blurts out answers before questions have been completed",
      type: "select",
      field_name: "blurts-questions",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "17. Often has difficulty awaiting turn",
      type: "select",
      field_name: "difficulty-awaiting",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "18. Often interrupts or intrudes on others (e.g. butts into conversations/ games)",
      type: "select",
      field_name: "interrupts-conversations",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "Subscale Average Score",
      type: "calculation-result",
      field_name: "section_2__result",
      group: "one-a",
      value: "",
      appears_under: "section_2",
      calculate_result: (values: number[]) => {
        const sum = values.reduce((acc, curr) => acc + curr, 0);
        return (sum / values.length).toFixed(1);
      },
    },

    {
      id: "section_3",
      type: "heading",
      label: "Section 3 - (ODD)",
    },

    {
      label: "19. Often loses temper",
      type: "select",
      field_name: "loses-temper",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },
    {
      label: "20. Often argue with adults",
      type: "select",
      field_name: "argue-adults",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "21. Often actively defies or refuses adult requests or rules",
      type: "select",
      field_name: "actively-defies",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "22. Often deliberately does things that annoy other people",
      type: "select",
      field_name: "deliberately-people",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "23. Often blames others for his or her mistakes or misbehavior",
      type: "select",
      field_name: "misbehavior-blames",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "24. Often touchy or easily annoyed by others",
      type: "select",
      field_name: "touchy-annoyed",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "25. Often is angry and resentful",
      type: "select",
      field_name: "angry-resentful",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "26. Often is spiteful or vindictive",
      type: "select",
      field_name: "vindictive-spiteful",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "Subscale Average Score",
      type: "calculation-result",
      field_name: "section_3__result",
      group: "one-a",
      value: "",
      appears_under: "section_3",
      calculate_result: (values: number[]) => {
        const sum = values.reduce((acc, curr) => acc + curr, 0);
        return (sum / values.length).toFixed(1);
      },
    },

    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   group: "seven",
    //   value: "",
    // },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "eight",
      value: "",
      disabled: true,
    },
    // {
    //   label: "Average score per subscale",
    //   type: "options-avg",
    //   field_name: "total-count",
    //   group: "eight",
    //   value: "",
    //   disabled: true,
    // },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "eight",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "eight",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "eight",
      value: "",
    },

    {
      id: "scoring_system",
      type: "heading",
      label: "Scoring System",
      table: {
        th: ["Subscale Avg. Score", "Interpretation"],
        td: [
          ["< 1.0", "Within normal limits"],
          ["1.0–1.5", "Mild symptoms – monitor"],
          ["> 1.5", "Clinically significant — likely ADHD/ODD symptoms"],
        ],
      },
    },
  ],
};

const child_adhd: IGenericAppointmentFormCategory = {
  id: "child_adhd",
  category: "Child ADHD",
  short_code: "SNAP IV 26",
  forms: [
    {
      id: "section_1",
      type: "heading",
      label: "Section 1 - (Inattention)",
    },
    {
      label: "1. Often fails to give close attention to details or makes careless mistakes in schoolwork or tasks",
      type: "select",
      field_name: "fails-attention",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "2. Often has difficulty sustaining attention in tasks or play activities",
      type: "select",
      field_name: "difficulty-sustaining",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "3. Often does not seem to listen when spoken to directly",
      type: "select",
      field_name: "listen-directly",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "4. Often does not follow through on instructions and fails to finish schoolwork, chores, or duties",
      type: "select",
      field_name: "instructions-fails",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "5. Often has difficulty organizing tasks and activities",
      type: "select",
      field_name: "difficulty-organizing",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "6. Often avoids, dislikes, or reluctantly engages in tasks requiring sustained mental effort",
      type: "select",
      field_name: "reluctantly-avoids",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "7. Often loses things necessary for activities (e.g., toys, school assignments, pencils, or books)",
      type: "select",
      field_name: "loses-necessary",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "8. Often is distracted by extraneous stimuli",
      type: "select",
      field_name: "distracted-extraneous",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "9. Often is forgetful in daily activities",
      type: "select",
      field_name: "forgetful-activities",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "Subscale Average Score",
      type: "calculation-result",
      field_name: "section_1__result",
      group: "one-a",
      value: "",
      appears_under: "section_1",
      calculate_result: (values: number[]) => {
        const sum = values.reduce((acc, curr) => acc + curr, 0);
        return (sum / values.length).toFixed(1);
      },
    },

    {
      id: "section_2",
      type: "heading",
      label: "Section 2 - (Hyperactivity/Impulsivity)",
    },

    {
      label: "10. Often fidgets with hands or feet or squirms in seat",
      type: "select",
      field_name: "fidgets-hands",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "11. Often leaves seat in classroom or in other situations in which remaining seated is expected",
      type: "select",
      field_name: "classroom-leaves",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "12. Often runs about or climbs excessively in situations in which it is inappropriate",
      type: "select",
      field_name: "excessively-inappropriate",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "13. Often has difficulty playing or engaging in leisure activities quietly",
      type: "select",
      field_name: "difficulty-leisure",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: `14. Often is "on the go" or often acts as if "driven by a motor"`,
      type: "select",
      field_name: "driven-motor",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "15. Often talks excessively",
      type: "select",
      field_name: "talks-excessively",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "16. Often blurts out answers before questions have been completed",
      type: "select",
      field_name: "blurts-questions",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "17. Often has difficulty awaiting turn",
      type: "select",
      field_name: "difficulty-awaiting",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "18. Often interrupts or intrudes on others (e.g. butts into conversations/ games)",
      type: "select",
      field_name: "interrupts-conversations",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "Subscale Average Score",
      type: "calculation-result",
      field_name: "section_2__result",
      group: "one-a",
      value: "",
      appears_under: "section_2",
      calculate_result: (values: number[]) => {
        const sum = values.reduce((acc, curr) => acc + curr, 0);
        return (sum / values.length).toFixed(1);
      },
    },

    {
      id: "section_3",
      type: "heading",
      label: "Section 3 - (ODD)",
    },

    {
      label: "19. Often loses temper",
      type: "select",
      field_name: "loses-temper",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },
    {
      label: "20. Often argue with adults",
      type: "select",
      field_name: "argue-adults",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "21. Often actively defies or refuses adult requests or rules",
      type: "select",
      field_name: "actively-defies",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "22. Often deliberately does things that annoy other people",
      type: "select",
      field_name: "deliberately-people",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "23. Often blames others for his or her mistakes or misbehavior",
      type: "select",
      field_name: "misbehavior-blames",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "24. Often touchy or easily annoyed by others",
      type: "select",
      field_name: "touchy-annoyed",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "25. Often is angry and resentful",
      type: "select",
      field_name: "angry-resentful",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "26. Often is spiteful or vindictive",
      type: "select",
      field_name: "vindictive-spiteful",
      group: "two",
      value: [""],
      options: TPRS_OPTIONS,
      options_is_summable: true,
      appears_under: "section_3",
    },

    {
      label: "Subscale Average Score",
      type: "calculation-result",
      field_name: "section_3__result",
      group: "one-a",
      value: "",
      appears_under: "section_3",
      calculate_result: (values: number[]) => {
        const sum = values.reduce((acc, curr) => acc + curr, 0);
        return (sum / values.length).toFixed(1);
      },
    },

    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "eight",
      value: "",
      disabled: true,
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "eight",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "eight",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "eight",
      value: "",
    },
    {
      id: "scoring_system",
      type: "heading",
      label: "Scoring System",
      table: {
        th: ["Average Score", "Interpretation"],
        td: [
          ["< 1.0", "Within normal limits"],
          ["1.0–1.5", "Mild symptoms – monitor"],
          ["> 1.5", "Clinically significant symptoms"],
          [">= 1.78", "Cutoff for probable diagnosis (per Swanson, 2001)"],
        ],
      },
    },
  ],
};

const child_ptsd: IGenericAppointmentFormCategory = {
  id: "child_ptsd",
  category: "Child PTSD Symptoms",
  short_code: "CPSS-5",
  forms: [
    {
      id: "section_1",
      label: "",
      type: "heading",
      description:
        "Sometimes scary or upsetting things happen to kids. It might be something like a car accident, getting beaten up, living through an earthquake, being robbed, being touched in a way you didn’t like, having a parent get hurt or killed, or some other very upsetting event. These questions ask how you feel about an upsetting thing that you experienced.",
      subDescription:
        "Read each question carefully. Then select the option that best describes how often that problem has bothered you IN THE LAST MONTH.",
    },
    {
      label: "1. Having upsetting thoughts or pictures about it that came into your head when you didn't want them to",
      type: "select",
      field_name: "thoughts-pictures",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "2. Having bad dreams or nightmares",
      type: "select",
      field_name: "bad-dreams",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "3. Acting or feeling as if it was happening again (seeing or hearing something and feeling as if you are there again)",
      type: "select",
      field_name: "feeling-happening",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "4. Feeling upset when you think about it or hear about the event (for example, feeling scared, angry, sad, guilty, confused)",
      type: "select",
      field_name: "guilty-confused",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label:
        "5. Having feelings in your body when you thinks about or hear about the event (for example, sweating, heart beating fast, stomach or head hurting)",
      type: "select",
      field_name: "head-hurting",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "6. Trying not to think about it or have feelings about it",
      type: "select",
      field_name: "trying-feelings",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label:
        "7. Trying to stay away from anything that reminds you of what happened (for example, people, places, or conversations about it)",
      type: "select",
      field_name: "anything-happened",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "8. Not being able to remember an important part of what happened",
      type: "select",
      field_name: "remember-important",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: `9. Having bad thoughts about yourself, other people, or the world (for example, "1 can't do anything right", "All people are bad", "The world is a scary place")`,
      type: "select",
      field_name: "about-yourself-people",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: `10. Thinking that what happened is your fault (for example, "I should have known better", "I shouldn't have done that", "I deserved it")`,
      type: "select",
      field_name: "what-happened-fault",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "11. Having strong bad feelings (like fear, anger, guilt, or shame)",
      type: "select",
      field_name: "having-strong",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "12. Having much less interest in doing things you used to do",
      type: "select",
      field_name: "less-interest",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "13. Not feeling close to your friends or family or not wanting to be around them",
      type: "select",
      field_name: "close-friends",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "14. Trouble having good feelings (like happiness or love) or trouble having any feelings at all",
      type: "select",
      field_name: "good-feelings",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "15. Getting angry easily (for example, yelling, hitting others, throwing things)",
      type: "select",
      field_name: "angry-easily",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "16. Doing things that might hurt yourself (for example, taking drugs, drinking alcohol, running away, cutting)",
      type: "select",
      field_name: "might-hurt-yourself",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "17. Being very careful or on the lookout for danger (for example, checking to see who is around you and what is around you)",
      type: "select",
      field_name: "very-careful-lookout",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },

    {
      label: "18. Being jumpy or easily scared (for example, when someone walks up behind you, when you hear a loud noise)",
      type: "select",
      field_name: "being-jumpy",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label:
        "19. Having trouble paying attention (for example, losing track of a story on TV, forgetting what you read, unable to pay attention in class)",
      type: "select",
      field_name: "losing-track",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      label: "20. Having trouble falling or staying asleep",
      type: "select",
      field_name: "trouble-staying-asleep",
      group: "two",
      value: [""],
      options: CHILD_PTSD_OPTIONS,
      options_is_summable: true,
      appears_under: "section_1",
    },
    {
      id: "section_2",
      label: "Functional Impairment",
      description: "Have the problems above been getting in the way of the following parts of your life IN THE PAST MONTH?",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "21. Fun things you want to do",
      type: "select",
      field_name: "fun-things-want",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "22. Doing your chores",
      type: "select",
      field_name: "your-chores",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "23. Relationships with friends",
      type: "select",
      field_name: "relationships-friends",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "24. Praying",
      type: "select",
      field_name: "praying",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "25. Schoolwork",
      type: "select",
      field_name: "schoolwork",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "26. Relationships with family",
      type: "select",
      field_name: "family-relationship",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },
    {
      label: "27. Being happy with your life",
      type: "select",
      field_name: "happy-life",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: true,
      appears_under: "section_2",
    },

    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "section_1",
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      appears_under: "section_1",
      use_heading_group: true,

      range_options: [
        { min: 0, max: 10, value: "Minimal or no PTSD symptoms" },
        { min: 11, max: 20, value: "Mild" },
        { min: 21, max: 40, value: "Moderate" },
        { min: 41, max: 60, value: "Severe – likely PTSD diagnosis" },
      ],
    },

    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "section_2",
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      appears_under: "section_2",
      use_heading_group: true,
      range_options: [
        { min: 0, max: 2, value: "Minimal or no functional impact" },
        { min: 3, max: 4, value: "Moderate impact — clinically relevant" },
        { min: 5, max: 7, value: "Severe functional impairment — strong support for PTSD diagnosis" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
      appears_under: "total",
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
      appears_under: "total",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
      appears_under: "total",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["0–10 Minimal or no PTSD symptoms", "11–20 Mild", "21–40 Moderate", "41–60 Severe – likely PTSD diagnosis"],
    },

    {
      id: "impact-scoring-system",
      type: "heading",
      label: "Functional Impact Scoring System",
      bullets: [
        "0–2 Minimal or no functional impact",
        "3–4 Moderate impact — clinically relevant",
        "5–7 Severe functional impairment — strong support for PTSD diagnosis",
      ],
    },
  ],
};

const dast_10: IGenericAppointmentFormCategory = {
  id: "dast_10",
  category: "DAST‐10 Questionnaire",
  forms: [
    {
      id: "instructions",
      label:
        "I’m going to read you a list of questions concerning information about your potential involvement with drugs, excluding alcohol and tobacco, during the past 12 months.",
      description:
        "When the words “drug abuse” are used, they mean the use of prescribed or over‐the‐counter medications/drugs in excess of the directions and any non‐medical use of drugs. The various classes of drugs may include: cannabis (e.g., marijuana, hash), solvents, tranquilizers (e.g., Valium), barbiturates, cocaine, stimulants (e.g., speed), hallucinogens (e.g., LSD) or narcotics (e.g., heroin). Remember that the questions do not include alcohol or tobacco.",
      type: "heading",
      fontWeight: "500",
    },
    {
      id: "past_12_months",
      type: "heading",
      label: "These questions refer to the past 6 months.",
    },
    {
      label: "1. Have you used drugs other than those required for medical reasons?",
      type: "select",
      field_name: "used-drug",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "2. Do you abuse more than one drug at a time?",
      type: "select",
      field_name: "abuse-one-more",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "3. Are you always able to stop using drugs when you want to? (If never use drugs, answer “Yes.”)",
      type: "select",
      field_name: "want-drugs",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(0, 1),
      options_is_summable: true,
    },
    {
      label: `4. Have you had "blackouts" or "flashbacks" as a result of drug use?`,
      type: "select",
      field_name: "abuse-one-more",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "5. Do you ever feel bad or guilty about your drug use? If never use drugs, choose “No.”",
      type: "select",
      field_name: "ever-say-no",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "6. Does your spouse (or parents) ever complain about your involvement with drugs?",
      type: "select",
      field_name: "spouse-parent-complain",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "7. Have you neglected your family because of your use of drugs?",
      type: "select",
      field_name: "neglected-family",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "8. Have you engaged in illegal activities in order to obtain drugs?",
      type: "select",
      field_name: "illegal-activities",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "9. Have you ever experienced withdrawal symptoms (felt sick) when you stopped taking drugs?",
      type: "select",
      field_name: "withdrawal-symptoms",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },
    {
      label: "10. Have you had medical problems as a result of your drug use (e.g., memory loss, hepatitis, convulsions, bleeding, etc.)?",
      type: "select",
      field_name: "medical-problems",
      group: "two",
      value: [""],
      options: DAST_1O_OPTIONS(),
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: -1, max: 0, value: "No problems reported" },
        { min: 1, max: 2, value: "Low level - monitor, brief education recommended" },
        { min: 3, max: 5, value: "Moderate level — further assessment recommended" },
        { min: 6, max: 8, value: "Substantial level — intensive assessment recommended" },
        { min: 9, max: 10, value: "Severe level — probable substance use disorder" },
      ],
    },

    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0   No problems reported",
        "1-2 Low level — monitor, brief education recommended",
        "3-5 Moderate level — further assessment recommended",
        "6-8 Substantial level — intensive assessment recommended",
        "9-10 Severe level — probable substance use disorder",
      ],
    },
  ],
};

const sadq: IGenericAppointmentFormCategory = {
  id: "sadq",
  category: "Severity of Alcohol Dependence Questionnaire",
  short_code: "SADQ-C",
  forms: [
    {
      id: "when_was",
      label: "Please recall a typical period of heavy drinking in the last 6 months.",
      description: "When was this?",
      type: "heading",
    },
    {
      label: "Month",
      type: "text",
      field_name: "month",
      group: "two",
      value: "",
      appears_under: "when_was",
    },
    {
      label: "Year",
      type: "text",
      field_name: "year",
      group: "two",
      value: "",
      appears_under: "when_was",
    },
    {
      label: "1. The day after drinking alcohol, I woke up feeling sweaty.",
      type: "select",
      field_name: "day-drinking-alcohol",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. The day after drinking alcohol, my hands shook first thing in the morning.",
      type: "select",
      field_name: "first-thing",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "3. The day after drinking alcohol, my whole body shook violently first thing in the morning if I didn't have a drink.",
      type: "select",
      field_name: "body-shook",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. The day after drinking alcohol, I woke up absolutely drenched in sweat.",
      type: "select",
      field_name: "absolutely-drenched",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. The day after drinking alcohol, I dread waking up in the morning.",
      type: "select",
      field_name: "dread-waking",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "6. The day after drinking alcohol, I was frightened of meeting people first thing in the morning",
      type: "select",
      field_name: "frightened-meeting",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "7. The day after drinking alcohol, I felt at the edge of despair when I awoke.",
      type: "select",
      field_name: "despair-edge",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. The day after drinking alcohol, I felt very frightened when I awoke.",
      type: "select",
      field_name: "despair-edge",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "9. The day after drinking alcohol, I liked to have an alcoholic drink in the morning.",
      type: "select",
      field_name: "alcoholic-morning",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "10. The day after drinking alcohol, I always gulped my first few alcoholic drinks down as quickly as possible.",
      type: "select",
      field_name: "gulped-first",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "11. The day after drinking alcohol, I drank more alcohol to get rid of the shakes.",
      type: "select",
      field_name: "more-shakes",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "12. The day after drinking alcohol, I had a very strong craving for a drink when I awoke.",
      type: "select",
      field_name: "strong-craving",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "13. I drank more than a quarter of a bottle of spirits in a day (OR 1 bottle of wine OR 7 beers)",
      type: "select",
      field_name: "spirits-beers",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "14. I drank more than half a bottle of spirits per day (OR 2 bottles of wine OR 15 beers).",
      type: "select",
      field_name: "bottles-beers",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "15. I drank more than one bottle of spirits per day (OR 4 bottles of wine OR 30 beers).",
      type: "select",
      field_name: "bottles-beers-30",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "16. I drank more than two bottles of spirits per day (OR 8 bottles of wine OR 60 beers)",
      type: "select",
      field_name: "bottles-beers-60",
      group: "two",
      value: [""],
      options: SADQ_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 12, value: "Low dependence" },
        { min: 13, max: 24, value: "Moderate dependence" },
        { min: 25, max: 36, value: "Severe dependence" },
        { min: 37, max: 48, value: "Very severe dependence" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "notes",
      label: "NOTES ON THE USE OF THE SADQ",
      type: "heading",
      description:
        "The Severity of Alcohol Dependence Questionnaire was developed by the Addiction Research Unit at the Maudsley Hospital. It is a measure of the severity of dependence. The AUDIT questionnaire, by contrast, is used to assess whether or not there is a problem with dependence.",
    },
    {
      id: "covers",
      label: "The SADQ questions cover the following aspects of dependency syndrome:",
      type: "heading",
      bullets: [
        "physical withdrawal symptoms",
        "affective withdrawal symptoms",
        "relief drinking",
        "frequency of alcohol consumption",
        "speed of onset of withdrawal symptoms.",
      ],
    },
    // {
    //   id: "scoring",
    //   label: "Scoring",
    //   type: "heading",
    //   description: "Answers to each question are rated on a four-point scale:",
    //   bullets: ["Almost never - 0", "Sometimes - 1", "relief drinking", "Often 2", "Nearly always 3"],
    // },
    {
      id: "scoring_more",
      label: "Scoring System",
      type: "heading",
      description: "",
      bullets: ["0-12 Low dependence", "13-24 Moderate dependence'", "25-36 Severe dependence.", "37-48 Very severe dependence"],
    },
  ],
};

const dependence_ldq: IGenericAppointmentFormCategory = {
  id: "ldq",
  category: "Dependence - Drugs & Alcohol | Leeds Dependence Questionnaire",
  short_code: "LDQ",
  forms: [
    {
      label: "1. Do you find yourself thinking about when you will next be able to have another drink or take more drugs?",
      type: "select",
      field_name: "thinking-about",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "2. Is drinking or taking drugs more important than anything else you might do during the day?",
      type: "select",
      field_name: "taking-drugs",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "3. Do you feel that your need for drink or drugs is too strong to control?",
      type: "select",
      field_name: "drink-drugs-strong",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "4. Do you plan your days around getting and taking drink or drugs?",
      type: "select",
      field_name: "plan-taking",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "5. Do you drink or take drugs in a particular way to increase the effect it gives you?",
      type: "select",
      field_name: "particular-increase",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "6. Do you drink or take drugs morning, afternoon, and evening?",
      type: "select",
      field_name: "drink-take",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "7. Do you feel you have to carry on drinking or taking drugs once you have started?",
      type: "select",
      field_name: "feel-drinking",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "8. Is getting an effect more important than the particular drink or drug that you take?",
      type: "select",
      field_name: "important-drug",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "9. Do you want to take more drink or drugs when the effects start to wear off?",
      type: "select",
      field_name: "more-drink-drugs",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },
    {
      label: "10. Do you find it difficult to cope with life without drink or drugs?",
      type: "select",
      field_name: "difficult-cope",
      group: "two",
      value: [""],
      options: LDQ_Options,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 10, value: "Low to mild" },
        { min: 11, max: 20, value: "Moderate" },
        { min: 21, max: 30, value: "High to severe psychological dependence" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["0–10 Low to mild", "11–20 Moderate", "21-30 High to severe psychological"],
    },
  ],
};

const phq: IGenericAppointmentFormCategory = {
  id: "phq",
  category: "Patient Health Questionnaire 9",
  short_code: "PHQ-9",
  forms: [
    {
      label: "1. Little interest or pleasure in doing things",
      type: "select",
      field_name: "little-interest",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. Feeling down, depressed, or hopeless",
      type: "select",
      field_name: "feeling-down",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "3. Trouble falling or staying asleep, or sleeping too much",
      type: "select",
      field_name: "falling-staying-asleep",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. Feeling tired or having little energy",
      type: "select",
      field_name: "little-energy",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. Poor appetite or overeating",
      type: "select",
      field_name: "poor-appetite",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "6. Feeling bad about yourself — or that you are a failure or have let yourself or your family down",
      type: "select",
      field_name: "bad-about-yoursel",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "7. Trouble concentrating on things, such as reading the newspaper or watching television",
      type: "select",
      field_name: "concentrating-reading",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "8. Moving or speaking so slowly that other people could have noticed? Or the opposite - being so fidgety or restless that you have been moving around a lot more than usual",
      type: "select",
      field_name: "speaking-slowly",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "9. Thoughts that you would be better off dead or of hurting yourself in some way",
      type: "select",
      field_name: "hurting-yourself",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "If you checked off any problems, how difficult have these problems made it for you to do your work, take care of things at home, or get along with other people?",
      type: "select",
      field_name: "how-difficult-to-get-along",
      group: "two",
      value: [""],
      options: PHQ_OPTIONS,
      options_is_summable: false,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 4, value: "Minimal or none" },
        { min: 5, max: 9, value: "Mild" },
        { min: 10, max: 14, value: "Moderate" },
        { min: 15, max: 19, value: "Moderately severe" },
        { min: 20, max: 27, value: "Severe" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["0–4 Minimal or none", "5–9 Mild", "10–14 Moderate", "15–19 Moderately severe", "20–27 Severe"],
    },
    {
      id: "bullets",
      type: "heading",
      label: "Interpretation",
      bullets: [
        "A score of 10 or higher is commonly used as the threshold for possible major depression",
        "Follow-up assessment recommended if ≥10 or if item 9 (suicidal thoughts) is scored >0",
      ],
    },
  ],
};

const center_for_epidemiological_studies: IGenericAppointmentFormCategory = {
  id: "cesdc",
  category: "Center for Epidemiological Studies Depression Scale For Children",
  short_code: "CES-DC",
  forms: [
    {
      label: "1. I was bothered by things that usually don't bother me.",
      type: "select",
      field_name: "bothered-things",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "2. I did not feel like eating, I wasn't very hungry",
      type: "select",
      field_name: "feel-eating",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "3. I wasn't able to feel happy, even when my family or friends tried to help me feel better.",
      type: "select",
      field_name: "feel-happy",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "4. I felt like I was just as good as other kids",
      type: "select",
      field_name: "feel-happy",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS_TWO,
      options_is_summable: true,
    },

    {
      label: "5. I felt like I couldn't pay attention to what I was doing.",
      type: "select",
      field_name: "pay-attention",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "6. I felt down and unhappy.",
      type: "select",
      field_name: "felt-unhappy",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "7. I felt like I was too tired to do things.",
      type: "select",
      field_name: "too-tired",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. I felt like something good was going to happen.",
      type: "select",
      field_name: "something-good",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS_TWO,
      options_is_summable: true,
    },

    {
      label: "9. I felt like things I did before didn't work out right.",
      type: "select",
      field_name: "work-right",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "10. I felt scared",
      type: "select",
      field_name: "felt-scared",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "11. I didn't sleep as well as I usually sleep",
      type: "select",
      field_name: "didnt-sleep-well",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "12. I was happy",
      type: "select",
      field_name: "was-happy",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS_TWO,
      options_is_summable: true,
    },

    {
      label: "13. I was more quiet than usual.",
      type: "select",
      field_name: "more-quiet",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "14. I felt lonely, like I didn't have any friends.",
      type: "select",
      field_name: "felt-lonely",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "15. I felt like kids I know were not friendly or that they didn't want to be with",
      type: "select",
      field_name: "felt-like-kids",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "16. I had a good time",
      type: "select",
      field_name: "good-time",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS_TWO,
      options_is_summable: true,
    },

    {
      label: "17. I felt like crying.",
      type: "select",
      field_name: "felt-crying",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "18. I felt sad.",
      type: "select",
      field_name: "felt-sad",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "19. I felt people didn’t like me.",
      type: "select",
      field_name: "people-like",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "20. It was hard to get started doing things.",
      type: "select",
      field_name: "hard-started",
      group: "two",
      value: [""],
      options: CESDC_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 14, value: "Likely no significant depressive symptoms" },
        { min: 15, max: 100, value: "Positive screen for depressive symptoms" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["<15 Likely no significant depressive symptoms", ">=15 Positive screen for depressive symptoms"],
    },
  ],
};

const nida_m_assist: IGenericAppointmentFormCategory = {
  id: "nida_m_assist",
  category: "Frequency - Drugs | NIDA M-ASSIST",
  forms: [
    {
      id: "duration_1",
      label:
        "During the past TWO (2) WEEKS, about how often did you use any of the following medicines ON YOUR OWN, that is, without a doctor's prescription, in greater amounts or longer than prescribed?",
      type: "heading",
    },
    {
      label: "1. Painkillers (like Vicodin)",
      type: "select",
      field_name: "painkillers-vicodin",
      group: "two",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: false,
      appears_under: "duration_1",
    },
    {
      label: "2. Stimulants (like Ritalin, Adderall)",
      type: "select",
      field_name: "stimulants-ritalin",
      group: "two",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: false,
      appears_under: "duration_1",
    },
    {
      label: "3. Sedatives or tranquilizers (like sleeping pills or Valium)",
      type: "select",
      field_name: "sedatives-tranquilizers",
      group: "two",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: false,
      appears_under: "duration_1",
    },

    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   group: "four",
    //   value: "",
    // },
    {
      id: "duration_2",
      label:
        "Please note: You should only proceed to the next question if your answer to Question 1 is Yes. If your answer is No, skip the next question and continue with the following section.",
      type: "heading",
    },

    {
      label: "1. Have you ever used [substance]?",
      type: "select",
      field_name: "have_you_ever",
      group: "one-a",
      value: [""],
      options: YES_NO_OPTIONS,
      options_is_summable: false,
      appears_under: "duration_2",
    },
    {
      label: "2. In the past 3 months, how often have you used [substance]?",
      type: "select",
      field_name: "in_past_month",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "duration_2",
    },
    {
      label: "3. Has your use of [substance] ever caused health, social, legal, or financial problems?",
      type: "select",
      field_name: "has_your_use",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "duration_2",
    },
    {
      label: "4. Have you ever failed to do what was normally expected of you because of your use of [substance]?",
      type: "select",
      field_name: "ever_failed_to_do",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "duration_2",
    },

    {
      label: "5. Has a friend, relative, or health worker ever expressed concern about your use of [substance]?",
      type: "select",
      field_name: "has_a_friend",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "duration_2",
    },

    {
      label: "5. Heroin",
      type: "select",
      field_name: "heroin",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "duration_2",
    },
    {
      label: "6. Inhalants or solvents (like glue)",
      type: "select",
      field_name: "solvents-glue",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "duration_2",
    },
    {
      label: "7. Methamphetamine (like speed)",
      type: "select",
      field_name: "methamphetamine-speed",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "duration_2",
    },

    {
      id: "failed_to_control",
      type: "heading",
      label: "Have you ever tried and failed to control or cut down your use?",
    },

    {
      label: "1. Tobacco products",
      type: "select",
      field_name: "tobacco-products",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "2. Alcoholic beverages",
      type: "select",
      field_name: "alcoholic_beverages",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "3. Cannabis",
      type: "select",
      field_name: "cannabis",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "4. Cocaine",
      type: "select",
      field_name: "cocaine",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "5. Prescription stimulants (nonmedical use)",
      type: "select",
      field_name: "prescription_stimulants",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "6. Methamphetamine",
      type: "select",
      field_name: "methamphetamine",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "6. Prescription opioids (nonmedical use)",
      type: "select",
      field_name: "prescription_opiods",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "7. Inhalants",
      type: "select",
      field_name: "inhalants",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "8. Sedatives or tranquilizers (e.g., benzodiazepines)",
      type: "select",
      field_name: "sedatives_or_tranquilizers",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "8. Hallucinogens / Ecstasy",
      type: "select",
      field_name: "hallucinogens_ectasy",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },
    {
      label: "9. Other drugs",
      type: "select",
      field_name: "other_drugs",
      group: "six",
      value: [""],
      options: NIDA_ASSIST_OPTIONS,
      options_is_summable: true,
      appears_under: "failed_to_control",
    },

    // {
    //   label: "",
    //   type: "line",
    //   field_name: "",
    //   group: "seven",
    //   value: "",
    // },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "eight",
      value: "",
      disabled: true,
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "eight",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "eight",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "eight",
      value: "",
    },
  ],
};

const kessler: IGenericAppointmentFormCategory = {
  id: "k10",
  category: "Kessler Psychological Distress Scale",
  short_code: "K10",
  forms: [
    {
      id: "instructions",
      label: "In the past 30 days, how often did you feel:...",
      type: "heading",
    },
    {
      label: "1. About how often did you feel tired out for no good reason?",
      type: "select",
      field_name: "feel-tired-often",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. About how often did you feel nervous?",
      type: "select",
      field_name: "feel-nervous",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "3. About how often did you feel so nervous that nothing could calm you down?",
      type: "select",
      field_name: "so-nervous",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. About how often did you feel hopeless?",
      type: "select",
      field_name: "feel-hopeless",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "5. About how often did you feel restless or fidgety?",
      type: "select",
      field_name: "fidgety-restless",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "6. About how often did you feel so restless you could not sit still?",
      type: "select",
      field_name: "restless-still",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "7. About how often did you feel depressed?",
      type: "select",
      field_name: "feel-depressed-often",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. About how often did you feel that everything was an effort?",
      type: "select",
      field_name: "everything-effort",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "9. About how often did you feel so sad that nothing could cheer you up?",
      type: "select",
      field_name: "nothing-cheer",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "10. About how often did you feel worthless?",
      type: "select",
      field_name: "feel-worthless-often",
      group: "two",
      value: [""],
      options: KESSLER_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 15, value: "Low (likely well)" },
        { min: 16, max: 21, value: "Moderate distress" },
        { min: 22, max: 29, value: "High distress — may indicate anxiety or depression" },
        { min: 30, max: 50, value: "Very high distress — likely mental health disorder; referral recommended" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0–15 Low (likely well)",
        "16–21 Moderate distress",
        "22–29 High distress — may indicate anxiety or depression",
        "30–50 Very high distress — likely mental health disorder; referral recommended",
      ],
    },
  ],
};

const ocir: IGenericAppointmentFormCategory = {
  id: "ocir",
  category: "OCI-R",
  forms: [
    {
      id: "instructions",
      label:
        "The following statements refer to experiences that many people have in their everyday lives. Circle the number that best describes HOW MUCH that experience has DISTRESSED or BOTHERED you during the PAST MONTH. The numbers refer to the following verbal labels:",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "1. have saved up so many things that they get in the way.",
      type: "select",
      field_name: "saved-many-things",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. I check things more often than necessary.",
      type: "select",
      field_name: "necessary-check",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "3. I get upset if objects are not arranged properly.",
      type: "select",
      field_name: "upset-objects",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "4. I feel compelled to count while I am doing things.",
      type: "select",
      field_name: "compelled-count",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. I find it difficult to touch an object when I know it has been touched by strangers or certain people.",
      type: "select",
      field_name: "touched-strangers",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "6. I find it difficult to control my own thoughts.",
      type: "select",
      field_name: "difficult-thoughts",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "7. I collect things I don't need.",
      type: "select",
      field_name: "collect-things",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. I repeatedly check doors, windows, drawers, etc.",
      type: "select",
      field_name: "repeatedly-check",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "9. I get upset if others change the way I have arranged things.",
      type: "select",
      field_name: "change-arranged",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "10. I feel I have to repeat certain numbers.",
      type: "select",
      field_name: "repeat-numbers",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "11. I sometimes have to wash or clean myself simply because I feel contaminated.",
      type: "select",
      field_name: "sometimes-wash",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "12. I am upset by unpleasant thoughts that come into my mind against my will.",
      type: "select",
      field_name: "unpleasant-thoughts",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "13. I avoid throwing things away because I am afraid I might need them later.",
      type: "select",
      field_name: "avoid-throwing",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "14. I repeatedly check gas and water taps and light switches after turning them off.",
      type: "select",
      field_name: "gas-check",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "15. I need things to be arranged in a particular way.",
      type: "select",
      field_name: "arranged-particular",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "16. I feel that there are good and bad numbers.",
      type: "select",
      field_name: "good-bad",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "17. I wash my hands more often and longer than necessary.",
      type: "select",
      field_name: "wash-hands-often",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "18. I frequently get nasty thoughts and have difficulty in getting rid of them.",
      type: "select",
      field_name: "nasty-thoughts",
      group: "two",
      value: [""],
      options: OCIR_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 20, value: "Subclinical / mild symptoms" },
        { min: 21, max: 40, value: "Moderate symptoms – may warrant follow-up" },
        { min: 41, max: 72, value: "Severe OCD symptoms – likely clinical OCD" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0–20 Subclinical / mild",
        "21–40 Moderate symptoms – may warrant follow-up",
        "41–72 Severe OCD symptoms – likely clinical OCD",
      ],
    },
  ],
};

const parenting_hassles: IGenericAppointmentFormCategory = {
  id: "parenting_hassles",
  category: "Parenting Daily Hassles",
  forms: [
    // {
    //   id: 'instructions',
    //   type: 'heading',
    //   label: "Parenting Daily Hassles",
    // },
    {
      label: "1. Continually cleaning up messes of toys or food",
      type: "select",
      field_name: "continually-cleaning",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "2. Being nagged, whined at, complained to",
      type: "select",
      field_name: "nagged-whined",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "3. Meal-time difficulties with picky eaters, complaining etc.",
      type: "select",
      field_name: "meal-time",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "4. The kids won't listen or do what they are asked without being nagged",
      type: "select",
      field_name: "kids-listen",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "5. Baby-sitters are hard to find",
      type: "select",
      field_name: "baby-sitters",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "6. The kids schedules (like pre-school or other activities) interfere with meeting your own household needs",
      type: "select",
      field_name: "kids-schedules",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "7. Sibling arguments or fights require a 'referee'",
      type: "select",
      field_name: "sibling-arguments",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. The kids demand that you entertain them or play with them",
      type: "select",
      field_name: "kids-demand",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "9. The kids resist or struggle with you over bed-time",
      type: "select",
      field_name: "kids-resist",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "10. The kids are constantly underfoot, interfering with other chores",
      type: "select",
      field_name: "kids-underfoot",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "11. The need to keep a constant eye on where the kids are and what they are doing",
      type: "select",
      field_name: "constant-eye",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "12. The kids interrupt adult conversations or interactions",
      type: "select",
      field_name: "kids-interrupt",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "13. Having to change your plans because of unprecedented child needs",
      type: "select",
      field_name: "change-plans",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "14. The kids get dirty several times a day requiring changes of clothing",
      type: "select",
      field_name: "kids-dirty",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "15. Difficulties in getting privacy (eg. in the bathroom)",
      type: "select",
      field_name: "difficulties-getting",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "16. The kids are hard to manage in public (grocery store, shopping centre, restaurant)",
      type: "select",
      field_name: "kids-grocery",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "17. Difficulties in getting kids ready for outings and leaving on time",
      type: "select",
      field_name: "kids-ready-outings",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "18. Difficulties in leaving kids for a night out or at school or day care",
      type: "select",
      field_name: "leaving-kids",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "19. The kids have difficulties with friends (eg. fighting, trouble, getting along, or no friends available)",
      type: "select",
      field_name: "kids-friends",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "20. Having to run extra errands to meet the kids needs",
      type: "select",
      field_name: "extra-errands",
      group: "two",
      value: [""],
      options: PARENTING_HASSLES_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "Questionnaire completed by mother/father/adoptive parent/foster carer (please specify)",
      type: "text",
      field_name: "completed-by",
      group: "one",
      value: "",
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "eight",
      value: "",
      disabled: true,
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "eight",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "eight",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "eight",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        {
          title: "Frequency score (how often hassles occur)",
          bullets: ["20–39 Low frequency of hassles", "40-59 Moderate frequency", "60-80 High frequency of daily parenting challenges"],
        },
        {
          title: "Intensity score (how stressful hassles feel)",
          bullets: [
            "20–39 Low perceived stress",
            "40-59 Moderate stress burden",
            "60-80 High perceived stress — may signal burnout, need for support",
          ],
        },
      ],
    },
  ],
};

const dsm_week_version: IGenericAppointmentFormCategory = {
  id: "dsm_week_version",
  category: "PTSD Checklist for DSM-5 - Week Version",
  short_code: "PCL-5",
  forms: [
    {
      id: "instructions_1",
      type: "heading",
      label: "PAST WEEK VERSION",
      description:
        "Below is a list of problems that people sometimes have in response to a very stressful experience. Please read each problem and then select one of the options to indicate how much you have been bothered by that problem in the past week. The options include not at all, a little bit, moderately, quite a bit, and extremely.",
    },

    {
      label: "1. Repeated, disturbing, and unwanted memories of the stressful experience?",
      type: "select",
      field_name: "unwanted-memories",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "2. Repeated, disturbing dreams of the stressful experience?",
      type: "select",
      field_name: "disturbing-dreams",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "3. Suddenly feeling or acting as if the stressful experience were actually happening again (as if you were actually back there reliving it)?",
      type: "select",
      field_name: "feeling-stressful",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "4. Feeling very upset when something reminded you of the stressful experience?",
      type: "select",
      field_name: "feeling-upset",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "5. Having strong physical reactions when something reminded you of the stressful experience (for example, heart pounding, trouble breathing, sweating)?",
      type: "select",
      field_name: "strong-physical",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "6. Avoiding memories, thoughts, or feelings related to the stressful experience?",
      type: "select",
      field_name: "avoiding-memories",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "7. Avoiding external reminders of the stressful experience (for example, people, places, conversations, activities, objects, or situations)?",
      type: "select",
      field_name: "external-reminders",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. Trouble remembering important parts of the stressful experience?",
      type: "select",
      field_name: "trouble-remembering",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "9. Having strong negative beliefs about yourself, other people, or the world (for example, having thoughts such as: I am bad, there is something seriously wrong with me, no one can be trusted, the world is completely dangerous)?",
      type: "select",
      field_name: "strong-negative-yourself",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "10. Blaming yourself or someone else for the stressful experience or what happened after it?",
      type: "select",
      field_name: "blaming-yourself",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "11. Having strong negative feelings such as fear, horror, anger, guilt, or shame?",
      type: "select",
      field_name: "fear-horror",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "12. Loss of interest in activities that you used to enjoy?",
      type: "select",
      field_name: "loss-interest",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "13. Feeling distant or cut off from other people?",
      type: "select",
      field_name: "feeling-distant",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "14. Trouble experiencing positive feelings (for example, being unable to feel happiness or have loving feelings for people close to you)?",
      type: "select",
      field_name: "positive-feelings",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "15. Irritable behavior, angry outbursts, or acting aggressively?",
      type: "select",
      field_name: "irritable-behavior",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "16. Taking too many risks or doing things that could cause you harm?",
      type: "select",
      field_name: "many-risks",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "17. Being “superalert” or watchful or on guard?",
      type: "select",
      field_name: "superalert",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "18. Feeling jumpy or easily startled?",
      type: "select",
      field_name: "feeling-startled",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "19. Having difficulty concentrating?",
      type: "select",
      field_name: "difficulty-concentrating",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "20. Trouble falling or staying asleep?",
      type: "select",
      field_name: "trouble-asleep",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 20, value: "Low distress" },
        { min: 21, max: 32, value: "Moderate PTSD symptoms" },
        { min: 33, max: 39, value: "Clinical concern — likely PTSD" },
        { min: 40, max: 50, value: "Severe — urgent follow-up likely needed" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0–20 Low distress",
        "21–32 Moderate PTSD symptoms",
        "≥33 Clinical concern — likely PTSD",
        "≥40 Severe — urgent follow-up likely needed",
      ],
    },
  ],
};

const dsm_month_version: IGenericAppointmentFormCategory = {
  id: "dsm_month_version",
  category: "PTSD Checklist for DSM-5 - Month Version",
  short_code: "PCL-5",
  forms: [
    {
      id: "",
      type: "heading",
      label: "PAST MONTH VERSION",
      description:
        "Below is a list of problems that people sometimes have in response to a very stressful experience. Please read each problem and then select one of the options to indicate how much you have been bothered by that problem in the past month. The options include not at all, a little bit, moderately, quite a bit, and extremely.",
    },

    {
      label: "1. Repeated, disturbing, and unwanted memories of the stressful experience?",
      type: "select",
      field_name: "unwanted-memories",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "2. Repeated, disturbing dreams of the stressful experience?",
      type: "select",
      field_name: "disturbing-dreams",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "3. Suddenly feeling or acting as if the stressful experience were actually happening again (as if you were actually back there reliving it)?",
      type: "select",
      field_name: "feeling-stressful",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "4. Feeling very upset when something reminded you of the stressful experience?",
      type: "select",
      field_name: "feeling-upset",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "5. Having strong physical reactions when something reminded you of the stressful experience (for example, heart pounding, trouble breathing, sweating)?",
      type: "select",
      field_name: "strong-physical",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "6. Avoiding memories, thoughts, or feelings related to the stressful experience?",
      type: "select",
      field_name: "avoiding-memories",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "7. Avoiding external reminders of the stressful experience (for example, people, places, conversations, activities, objects, or situations)?",
      type: "select",
      field_name: "external-reminders",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. Trouble remembering important parts of the stressful experience?",
      type: "select",
      field_name: "trouble-remembering",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "9. Having strong negative beliefs about yourself, other people, or the world (for example, having thoughts such as: I am bad, there is something seriously wrong with me, no one can be trusted, the world is completely dangerous)?",
      type: "select",
      field_name: "strong-negative-yourself",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "10. Blaming yourself or someone else for the stressful experience or what happened after it?",
      type: "select",
      field_name: "blaming-yourself",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "11. Having strong negative feelings such as fear, horror, anger, guilt, or shame?",
      type: "select",
      field_name: "fear-horror",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "12. Loss of interest in activities that you used to enjoy?",
      type: "select",
      field_name: "loss-interest",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "13. Feeling distant or cut off from other people?",
      type: "select",
      field_name: "feeling-distant",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label:
        "14. Trouble experiencing positive feelings (for example, being unable to feel happiness or have loving feelings for people close to you)?",
      type: "select",
      field_name: "positive-feelings",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "15. Irritable behavior, angry outbursts, or acting aggressively?",
      type: "select",
      field_name: "irritable-behavior",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "16. Taking too many risks or doing things that could cause you harm?",
      type: "select",
      field_name: "many-risks",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "17. Being “superalert” or watchful or on guard?",
      type: "select",
      field_name: "superalert",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "18. Feeling jumpy or easily startled?",
      type: "select",
      field_name: "feeling-startled",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "19. Having difficulty concentrating?",
      type: "select",
      field_name: "difficulty-concentrating",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "20. Trouble falling or staying asleep?",
      type: "select",
      field_name: "trouble-asleep",
      group: "two",
      value: [""],
      options: DSM_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 20, value: "Minimal symptoms" },
        { min: 21, max: 33, value: "Mild to moderate PTSD symptoms" },
        { min: 34, max: 39, value: "Probable PTSD diagnosis — refer for full clinical assessment" },
        { min: 40, max: 80, value: "Likely PTSD — strong clinical concern" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0–20 Minimal symptoms",
        "21-33 Mild to moderate PTSD symptoms",
        "33-39 Probable PTSD diagnosis — refer for full clinical assessment",
        "40–80 Likely PTSD — strong clinical concern",
      ],
    },
  ],
};

const perceived_social_support: IGenericAppointmentFormCategory = {
  id: "perceived_social_support",
  category: "Multidimensional Scale of Perceived Social Support",
  forms: [
    {
      id: "instructions",
      type: "heading",
      label: "",
      description:
        "We are interested in how you feel about the following statements. Read each statement carefully. Indicate how you feel about each statement.",
      subDescription:
        "The items tended to divide into factor groups relating to the source of the social support, namely family (Fam), friends (Fri) or significant other (SO).",
    },
    {
      label: "1. There is a special person who is around when I am in need. (SO)",
      type: "select",
      field_name: "special-need",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "2. There is a special person with whom I can share my joys and sorrows. (SO)",
      type: "select",
      field_name: "special-care",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "3. My family really tries to help me. (Fam)",
      type: "select",
      field_name: "family-help",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "4. I get the emotional help and support I need from my family. (Fam)",
      type: "select",
      field_name: "family-emotional",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "5. I have a special person who is a real source of comfort to me. (SO)",
      type: "select",
      field_name: "special-comfort",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "6. My friends really try to help me. (Fri)",
      type: "select",
      field_name: "friends-help",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "7. I can count on my friends when things go wrong. (Fri)",
      type: "select",
      field_name: "friends-wrong",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "8. I can talk about my problems with my family. (Fam)",
      type: "select",
      field_name: "family-problems",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "9. I have friends with whom I can share my joys and sorrows. (Fri)",
      type: "select",
      field_name: "share-joys",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "10. There is a special person in my life who cares about my feelings. (SO)",
      type: "select",
      field_name: "special-person",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "11. My family is willing to help me make decisions. (Fam)",
      type: "select",
      field_name: "family-willing",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "12. I can talk about my problems with my friends. (Fri)",
      type: "select",
      field_name: "talk-friends",
      group: "two",
      value: [""],
      options: SOCIAL_OPTIONS,
      options_is_summable: true,
    },

    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "eight",
      value: "",
      disabled: true,
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "eight",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "eight",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "eight",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["1.0-2.9 Low support", "3.0-5.0 Moderate support", "5.1-7.0 High support"],
    },
    {
      id: "scoring-system-interpre",
      type: "heading",
      label: "Interpretation",
      bullets: [
        "Total Score Range: 12–84",
        "Each subscale range: 4–28",
        "Subscale scores (average of 4 items each)",
        "Total perceived support score (sum or average of all 12 items)",
      ],
    },
  ],
};

const readiness_alcohol_change: IGenericAppointmentFormCategory = {
  id: "readiness_alcohol_change",
  category: "Readiness to Change - Alcohol",
  short_code: "Socrates-8A",
  forms: [
    {
      id: "instructions",
      type: "heading",
      label: "Stages of Change Readiness and Treatment Eagerness Scale for Alcohol (Socrates-8A)",
      description:
        "Please read the following statements carefully. Each one describes a way that you might (or might not) feel about your drinking. For each statement circle one number from 1 to 5 to indicate how much you agree or disagree with it right now. Please circle one and only one number for every statement.",
    },

    {
      id: "recognition",
      type: "heading",
      label: "Recognition",
    },

    {
      label: "1. Sometimes I wonder if I am an alcoholic.",
      type: "select",
      field_name: "wonder-alcoholic",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "2. If I don't change my drinking soon, my problems are going to get worse.",
      type: "select",
      field_name: "change-drinking",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "3. Sometimes I wonder if my drinking is hurting other people.",
      type: "select",
      field_name: "wonder-hurting-people",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "4. I am a problem drinker.",
      type: "select",
      field_name: "problem-drinker",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "5. I have serious problems with drinking.",
      type: "select",
      field_name: "serious-drinking",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "6. My drinking is causing a lot of harm.",
      type: "select",
      field_name: "drinking-harm",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "7. I know I have a drinking problem.",
      type: "select",
      field_name: "recognized-drinking",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "8. There are times when I wonder if I am drinking too much.",
      type: "select",
      field_name: "wonder-drinking",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "9. I am alcoholic",
      type: "select",
      field_name: "alcoholic",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "Total Score",
      type: "options-total",
      field_name: "total-count",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "recognition",
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "recognition",
      range_options: [
        { min: 0, max: 15, value: "Low Recognition" },
        { min: 16, max: 23, value: "Moderate Recognition" },
        { min: 24, max: 45, value: "High Recognition" },
      ],
    },

    ///////////////////////
    {
      id: "ambivalence",
      type: "heading",
      label: "Ambivalence",
    },

    {
      label: "1. Sometimes I wonder if I am an alcoholic.",
      type: "select",
      field_name: "wonder-alcoholic__ambivalence",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "ambivalence",
    },
    {
      label: "2. Sometimes I wonder if my drinking is hurting other people.",
      type: "select",
      field_name: "wonder-hurting-people__ambivalence",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "ambivalence",
    },
    {
      label: "3. Sometimes I wonder if I am in control of my drinking.",
      type: "select",
      field_name: "control-drinking__ambivalence",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "ambivalence",
    },
    {
      label: "4. There are times when I wonder if I am drinking too much.",
      type: "select",
      field_name: "wonder-drinking__ambivalence",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "ambivalence",
    },

    {
      label: "Total Score",
      type: "options-total",
      field_name: "total-count__ambivalence",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "ambivalence",
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range__ambivalence",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "ambivalence",
      range_options: [
        { min: 0, max: 6, value: "Low Ambivalence" },
        { min: 7, max: 12, value: "Moderate Ambivalence" },
        { min: 13, max: 20, value: "High Ambivalence" },
      ],
    },

    ///////////////////////

    {
      id: "taking_steps",
      type: "heading",
      label: "Taking Steps",
    },

    {
      label: "1. I really want to make changes in my drinking.",
      type: "select",
      field_name: "really-changes-need__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "2. I have already started making changes in my drinking.",
      type: "select",
      field_name: "already-drinking__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "3. I was drinking too much at one time, but l've managed to change my drinking.",
      type: "select",
      field_name: "was-drinking__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "4. I'm not just thinking about changing my drinking, I'm already doing something about it.",
      type: "select",
      field_name: "thinking-changing__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "5. I have already changed my drinking, and I am looking for ways to keep from slipping back to my old pattern.",
      type: "select",
      field_name: "already-looking__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "6. I am actively doing things now to cut down or stop drinking.",
      type: "select",
      field_name: "stop-drinking__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "7. I want help to keep from going back to the drinking problems that I had before.",
      type: "select",
      field_name: "drinking-help__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "8. I am working hard to change my drinking.",
      type: "select",
      field_name: "hard-change__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "9. I have made some changes in my drinking, and I want some help to keep me from going back to the way I used to drink.",
      type: "select",
      field_name: "some-help__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "Total Score",
      type: "options-total",
      field_name: "total-count__taking_steps",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "taking_steps",
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range__taking_steps",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "taking_steps",
      range_options: [
        { min: 0, max: 15, value: "Low Taking Steps" },
        { min: 16, max: 23, value: "Moderate Taking Steps" },
        { min: 24, max: 45, value: "High Taking Steps" },
      ],
    },

    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-a",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      description: "The interpretation of the scores:",
      bullets: [
        "High Recognition + High Taking Steps → likely in Action stage",
        "High Recognition + High Ambivalence, Low Taking Steps → Contemplation",
        "Low scores across all → likely in Precontemplation",
      ],
    },
  ],
};

const readiness_drug_change: IGenericAppointmentFormCategory = {
  id: "readiness_drug_change",
  category: "Readiness to Change - Drugs",
  short_code: "Socrates-8D",
  forms: [
    {
      id: "instructions",
      type: "heading",
      label: "Stages of Change Readiness and Treatment Eagerness Scale for Drugs (Socrates-8D)",
      description:
        "Please read the following statements carefully. Each one describes a way that you might (or might not) feel about your drinking. For each statement circle one number from 1 to 5 to indicate how much you agree or disagree with it right now. Please circle one and only one number for every statement.",
    },
    {
      id: "recognition",
      type: "heading",
      label: "Recognition",
    },

    {
      label: "1. Sometimes I wonder if I am an addict.",
      type: "select",
      field_name: "wonder-addict__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "2. If I don't change my drug use soon, my problems are going to get worse.",
      type: "select",
      field_name: "change-drug-worse__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "3. Sometimes I wonder if my drug use is hurting other people.",
      type: "select",
      field_name: "wonder-hurting-people__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "4. I am a drug problem",
      type: "select",
      field_name: "drug-problem__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "5. I have serious problems with drugs.",
      type: "select",
      field_name: "serious-drugs__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "6. My drug use is causing a lot of harm.",
      type: "select",
      field_name: "drug-harm__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "7. I know I have a drug problem.",
      type: "select",
      field_name: "recognized-drug-problem__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "8. There are times when I wonder if I use drugs too much.",
      type: "select",
      field_name: "wonder-drug-intake__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },
    {
      label: "9. I am a drug addict",
      type: "select",
      field_name: "drug-addict__recognition",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "recognition",
    },

    {
      label: "Total Score",
      type: "options-total",
      field_name: "total-count__recognition",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "recognition",
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range__recognition",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "recognition",
      range_options: [
        { min: 0, max: 15, value: "Low Recognition" },
        { min: 16, max: 23, value: "Moderate Recognition" },
        { min: 24, max: 45, value: "High Recognition" },
      ],
    },

    ////////////////

    {
      id: "ambivalence",
      type: "heading",
      label: "Ambivalence",
    },

    {
      label: "1. Sometimes I wonder if I am an addict.",
      type: "select",
      field_name: "wonder-addict__ambivalence",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "ambivalence",
    },
    {
      label: "2. Sometimes I wonder if my drug use is hurting other people.",
      type: "select",
      field_name: "wonder-hurting-people__ambivalence",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "ambivalence",
    },
    {
      label: "3. Sometimes I wonder if I am in control of my drug use.",
      type: "select",
      field_name: "control-drugs__ambivalence",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "ambivalence",
    },
    {
      label: "4. There are times when I wonder if I use drugs too much.",
      type: "select",
      field_name: "wonder-drug-intake__ambivalence",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "ambivalence",
    },

    {
      label: "Total Score",
      type: "options-total",
      field_name: "total-count__ambivalence",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "ambivalence",
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range__ambivalence",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "ambivalence",
      range_options: [
        { min: 0, max: 6, value: "Low Ambivalence" },
        { min: 7, max: 12, value: "Moderate Ambivalence" },
        { min: 13, max: 20, value: "High Ambivalence" },
      ],
    },

    ////////////////

    {
      id: "taking_steps",
      type: "heading",
      label: "Taking Steps",
    },

    {
      label: "1. I really want to make changes in my use of drugs.",
      type: "select",
      field_name: "changes-drug__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "2. I have already started making changes in my use of drugs.",
      type: "select",
      field_name: "making-changes__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },
    {
      label: "3. I was using drugs too much at one time, but l've managed to change that.",
      type: "select",
      field_name: "was-much-drug__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "4. I'm not just thinking about changing my drug use, I'm already doing something about it.",
      type: "select",
      field_name: "changing-druguse__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },
    {
      label: "5. I have already changed my drug use, and I am looking for ways to keep from slipping back to my old pattern.",
      type: "select",
      field_name: "slipping-pattern__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "6. I am actively doing things now to cut down or stop use of drugs.",
      type: "select",
      field_name: "drug-cutdown__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },
    {
      label: "7. I want help to keep from going back to the drug problems that I had before.",
      type: "select",
      field_name: "drug-help-problems__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "8. I am working hard to change my drug use.",
      type: "select",
      field_name: "hard-chang-druguse__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },
    {
      label: "9. I have made some changes in my drug use, and I want some help to keep me from going back to the way I used before.",
      type: "select",
      field_name: "changes-druguse-before__taking_steps",
      group: "two",
      value: [""],
      options: READINESS_OPTIONS,
      options_is_summable: true,
      appears_under: "taking_steps",
    },

    {
      label: "Total Score",
      type: "options-total",
      field_name: "total-count__taking_steps",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "taking_steps",
    },

    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range__taking_steps",
      group: "two-a",
      value: "",
      disabled: true,
      use_heading_group: true,
      appears_under: "taking_steps",
      range_options: [
        { min: 0, max: 15, value: "Low Taking Steps" },
        { min: 16, max: 23, value: "Moderate Taking Steps" },
        { min: 24, max: 45, value: "High Taking Steps" },
      ],
    },

    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-a",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },

    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      description: "The interpretation of the scores:",
      bullets: [
        "High Recognition + High Taking Steps → likely in Action stage",
        "High Recognition + High Ambivalence, Low Taking Steps → Contemplation",
        "Low scores across all → likely in Precontemplation",
      ],
    },
  ],
};

const relationship: IGenericAppointmentFormCategory = {
  id: "relationship_assessment_scale",
  category: "Relationship Assessment Scale",
  forms: [
    {
      id: "instructions",
      type: "heading",
      label: "Scoring is kept continuous. The higher the score, the more satisfied the respondent is with his/her relationship.",
    },
    {
      label: "1. How well does your partner meet your needs?",
      type: "select",
      field_name: "partner-needs",
      group: "two",
      value: [""],
      options_is_summable: true,
      options: RELATIONSHIP_OPTIONS,
    },
    {
      label: "2. In general, how satisfied are you with your relationship?",
      type: "select",
      field_name: "satisfied-relationship",
      group: "two",
      value: [""],
      options_is_summable: true,
      options: RELATIONSHIP_OPTIONS,
    },
    {
      label: "3. How good is your relationship compared to most?",
      type: "select",
      field_name: "compared-relationship",
      group: "two",
      value: [""],
      options_is_summable: true,
      options: RELATIONSHIP_OPTIONS,
    },
    {
      label: "4. How often do you wish you hadn’t gotten into this relationship?",
      type: "select",
      field_name: "how-often-hadnt",
      group: "two",
      value: [""],
      options_is_summable: true,
      options: RELATIONSHIP_OPTIONS_REVERSED,
    },
    {
      label: "5. To what extent has your relationship met your original expectations?",
      type: "select",
      field_name: "original-expectations",
      group: "two",
      value: [""],
      options_is_summable: true,
      options: RELATIONSHIP_OPTIONS,
    },
    {
      label: "6. How much do you love your partner?",
      type: "select",
      field_name: "love-partner",
      group: "two",
      value: [""],
      options_is_summable: true,
      options: RELATIONSHIP_OPTIONS,
    },
    {
      label: "7. How many problems are there in your relationship?",
      type: "select",
      field_name: "problems-relationship",
      group: "two",
      value: [""],
      options_is_summable: true,
      options: RELATIONSHIP_OPTIONS,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 17, value: "Low satisfaction — possible relationship distress" },
        { min: 18, max: 23, value: "Moderate concerns" },
        { min: 24, max: 29, value: "Moderate to high satisfaction" },
        { min: 30, max: 35, value: "Very high satisfaction" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0–17 Low satisfaction — possible relationship distress",
        "18-23 Moderate concerns",
        "24-29 Moderate to high satisfaction",
        "30–35 Very high satisfaction",
      ],
    },
  ],
};

const rosenberg_scale: IGenericAppointmentFormCategory = {
  id: "rosenberg_self_esteem_scale",
  category: "Rosenberg Self-Esteem Scale",
  forms: [
    {
      id: "instructions",
      label:
        "A 10-item scale that measures global self-worth by measuring both positive and negative feelings about the self. The scale is believed to be uni-dimensional. All items are answered using a 4-point Likert scale format ranging from strongly agree to strongly disagree.",
      description:
        "Below is a list of statements dealing with your general feelings about yourself. Please indicate how strongly you agree or disagree with each statement.",
      type: "heading",
    },
    {
      label: "1. On the whole, I am satisfied with myself.",
      type: "select",
      field_name: "satisfied-myself",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. At times I think I am no good at all.",
      type: "select",
      field_name: "think-good",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS_REVERSED,
      options_is_summable: true,
    },
    {
      label: "3. I feel that I have a number of good qualities.",
      type: "select",
      field_name: "good-qualities",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. I am able to do things as well as most other people.",
      type: "select",
      field_name: "other-people",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. I feel I do not have much to be proud of.",
      type: "select",
      field_name: "much-proud",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS_REVERSED,
      options_is_summable: true,
    },
    {
      label: "6. I certainly feel useless at times.",
      type: "select",
      field_name: "certainly-useless",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS_REVERSED,
      options_is_summable: true,
    },
    {
      label: "7. I feel that I'm a person of worth, at least on an equal plane with others.",
      type: "select",
      field_name: "person-worth",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "8. I wish I could have more respect for myself.",
      type: "select",
      field_name: "respect-myself",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS_REVERSED,
      options_is_summable: true,
    },
    {
      label: "9. All in all, I am inclined to feel that I am a failure.",
      type: "select",
      field_name: "all-in-all",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS_REVERSED,
      options_is_summable: true,
    },
    {
      label: "10. I take a positive attitude toward myself.",
      type: "select",
      field_name: "positive-attitude",
      group: "two",
      value: [""],
      options: ROSENBERG_SCALE_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 14, value: "Low self-esteem" },
        { min: 15, max: 25, value: "Normal (average) self-esteem" },
        { min: 26, max: 40, value: "High self-esteem" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["0-14 Low self-esteem", "15-25 Normal (average) self-esteem", ">25 High self-esteem"],
    },
  ],
};

const social_phobia: IGenericAppointmentFormCategory = {
  id: "spin",
  category: "Social Phobia Inventory",
  short_code: "SPIN",
  forms: [
    {
      id: "instructions",
      label:
        "Please indicate how much the following problems have bothered you during the past week. Mark only one box for each problem, and be sure to answer all items.",
      type: "heading",
      fontWeight: "500",
    },
    {
      label: "1. I am afraid of people in authority.",
      type: "select",
      field_name: "afraid-authority",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. I am bothered by blushing in front of people.",
      type: "select",
      field_name: "bothered-blushing",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "3. Parties and social events scare me.",
      type: "select",
      field_name: "parties-social",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. I avoid talking to people I don't know.",
      type: "select",
      field_name: "avoid-talking",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. Being criticized scares me a lot.",
      type: "select",
      field_name: "criticized-scares",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "6. Fear of embarrassment causes me to avoid doing things or speaking to people.",
      type: "select",
      field_name: "criticized-scares",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "7. Sweating in front of people causes me distress.",
      type: "select",
      field_name: "criticized-scares",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "8. I avoid going to parties.",
      type: "select",
      field_name: "avoid-parties",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "9. I avoid activities in which I am the center of attention.",
      type: "select",
      field_name: "activities-attention",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "10. Talking to strangers scares me.",
      type: "select",
      field_name: "talking-strangers",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "11. I avoid having to give speeches.",
      type: "select",
      field_name: "avoid-speeches",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "12. I would do anything to avoid being criticized.",
      type: "select",
      field_name: "anything-criticized",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "13. Heart palpitations bother me when I am around people.",
      type: "select",
      field_name: "palpitations-bother",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "14. I am afraid of doing things when people might be watching.",
      type: "select",
      field_name: "afraid-watching",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "15. Being embarrassed or looking stupid is among my worst fears.",
      type: "select",
      field_name: "embarrassed-stupid",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "16. I avoid speaking to anyone in authority.",
      type: "select",
      field_name: "speaking-authority",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "17. Trembling or shaking in front of others is distressing to me.",
      type: "select",
      field_name: "trembling-shaking",
      group: "two",
      value: [""],
      options: SOCIAL_PHOBIA_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 20, value: "Mild or no social anxiety" },
        { min: 21, max: 30, value: "Moderate social anxiety" },
        { min: 31, max: 40, value: "Marked social anxiety" },
        { min: 41, max: 50, value: "Severe social anxiety" },
        { min: 51, max: 68, value: "Very severe social anxiety" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "0–20 Mild or no social anxiety",
        "21–30 Moderate social anxiety",
        "31–40 Marked social anxiety",
        "41–50 Severe social anxiety",
        "51–68 Very severe – high clinical concern",
      ],
    },
  ],
};

const somatic: IGenericAppointmentFormCategory = {
  id: "somatic",
  category: "Somatic / Physical Symptoms",
  short_code: "PHQ 15",
  forms: [
    {
      id: "instructions",
      type: "heading",
      label: "Adapted Patient Health Questionnaire 15 (PHQ 15)",
      description: "Over the last 2 weeks, how often have you been bothered by any of the following problems?",
    },
    {
      label: "1. Stomach pain",
      type: "select",
      field_name: "stomach-pain",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. Back pain",
      type: "select",
      field_name: "back-pain",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "3. Pain in your arms, legs, or joints (knees, hips, etc.)",
      type: "select",
      field_name: "pain-arms",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. Headaches",
      type: "select",
      field_name: "headaches",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. Chest pain",
      type: "select",
      field_name: "chest-pain",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "6. Dizziness",
      type: "select",
      field_name: "dizziness",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "7. Fainting spells",
      type: "select",
      field_name: "fainting-spells",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "8. Feeling heart pound or race",
      type: "select",
      field_name: "feeling-pound",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "9. Shortness of breath",
      type: "select",
      field_name: "shortness-breath",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "10. Constipation, loose bowels, or diarrhea",
      type: "select",
      field_name: "constipation",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "11. Nausea, gas, or indigestion",
      type: "select",
      field_name: "nausea-gas",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "12. Feeling tired or having low",
      type: "select",
      field_name: "feeling-tired",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "13. Trouble sleeping",
      type: "select",
      field_name: "trouble-sleeping",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "14. Menstrual cramps or other problems with your periods (This is only applicable if the respondent menstruates)",
      type: "select",
      field_name: "menstrual-cramps",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "15. Problems during sexual intercourse",
      type: "select",
      field_name: "sexual-problems_1",
      group: "two",
      value: [""],
      options: SOMATIC_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 4, value: "Minimal" },
        { min: 5, max: 9, value: "Low" },
        { min: 10, max: 14, value: "Moderate" },
        { min: 15, max: 30, value: "High (suggests somatization / clinical concern)" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["0–4 Minimal", "5–9 Low", "10–14 Moderate", "15–30 High (suggests somatization / clinical concern)"],
    },
  ],
};

const trauma_screening: IGenericAppointmentFormCategory = {
  id: "tsq",
  category: "Trauma Screening Questionnaire",
  short_code: "TSQ",
  forms: [
    {
      id: "instructions",
      type: "heading",
      label: "",
      description:
        "If you have recently been exposed to a potentially traumatic event (a PTE), here is a tool that may help you to identify whether or not you should seek additional help in recovering from its effects. Have you recently experienced any of the following:",
    },
    {
      label: "1. Upsetting thoughts or memories about the event that have come into your mind against your will",
      type: "select",
      field_name: "upsetting-thoughts",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. Upsetting dreams about the event",
      type: "select",
      field_name: "upsetting-dreams",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "3. Acting or feeling as though the event were happening again",
      type: "select",
      field_name: "happening-again",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. Feeling upset by reminders of the event",
      type: "select",
      field_name: "feeling-reminders",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. Bodily reactions (such as fast heartbeat, stomach churning)",
      type: "select",
      field_name: "bodily-reactions",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "6. Difficulty falling or staying asleep",
      type: "select",
      field_name: "difficulty-asleep",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "7. Irritability or outbursts of anger",
      type: "select",
      field_name: "irritability-outbursts",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "8. Difficulty concentrating",
      type: "select",
      field_name: "difficulty-concentrating",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "9. Heightened awareness of potential dangers to yourself and others",
      type: "select",
      field_name: "awareness-heightened",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "10. Feeling jumpy or being startled by something unexpected",
      type: "select",
      field_name: "feeling-jumpy",
      group: "two",
      value: [""],
      options: TSQ_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    // {
    //   label: "Total",
    //   type: "options-total",
    //   field_name: "total-count",
    //   group: "eight",
    //   value: "",
    //   disabled: true
    // },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "one",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 5, value: "Low risk (but monitor if symptoms persist)" },
        { min: 6, max: 1000, value: "Positive screen → Possible PTSD → Refer for further clinical assessment" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: [
        "<6 Low risk (but monitor if symptoms persist)",
        ">6 Positive screen → Possible PTSD → Refer for further clinical assessment",
      ],
    },
  ],
};

const wsas: IGenericAppointmentFormCategory = {
  id: "wsas",
  category: "Work and Social Adjustment Scale (WSAS)",
  forms: [
    {
      id: "instructions",
      type: "heading",
      label: "",
      description:
        "People'sproblems sometimes affect their ability to do certain day-to-day tasks in their lives. To rate your problems look at each section and determine on the scale provided how much your problem impairs your ability to carry out the activity. This assessment is not intended to be a diagnosis. If you are concerned about your results in any way, please speak with a qualified health professional.",
    },
    {
      label:
        "1. Because of my [problem] my ability to work is impaired. '0' means 'not at all impaired' and '8' means very severely impaired to the point I can't work.",
      type: "select",
      field_name: "because-impaired",
      group: "two",
      value: [""],
      options: WSAS_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "2. Because of my [problem] my home management (cleaning, tidying, shopping, cooking, looking after home or children, paying bills) is impaired.",
      type: "select",
      field_name: "shopping-cooking",
      group: "two",
      value: [""],
      options: WSAS_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "3. Because of my [problem] my social leisure activities (with other people e.g. parties, bars, clubs, outings, visits, dating, home entertaining) are impaired.",
      type: "select",
      field_name: "leisure-activities",
      group: "two",
      value: [""],
      options: WSAS_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "4. Because of my [problem], my private leisure activities (done alone, such as reading, gardening, collecting, sewing, walking alone) are impaired.",
      type: "select",
      field_name: "private-activities",
      group: "two",
      value: [""],
      options: WSAS_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "5. Because of my [problem], my ability to form and maintain close relationships with others, including those I live with, is impaired.",
      type: "select",
      field_name: "close-relationships",
      group: "two",
      value: [""],
      options: WSAS_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 10, value: "Mild or no impairment" },
        { min: 11, max: 20, value: "Moderate impairment" },
        { min: 21, max: 30, value: "Significant impairment" },
        { min: 31, max: 40, value: "Severe impairment" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["0–10 Mild or no impairment", "11–20 Moderate impairment", "21–30 Significant impairment", "31–40 Severe impairment"],
    },
  ],
};

const pain_index: IGenericAppointmentFormCategory = {
  id: "pain_index",
  category: "Pain Disability Index",
  forms: [
    {
      label:
        "1. Family/Home Responsibilities: This category refers to activities of the home or family. It includes chores or duties performed around the house (e.g. yard work) and errands or favors for other family members (e.g. driving the children to school).",
      type: "select",
      field_name: "family-responsibilities",
      group: "two",
      value: [""],
      options: PAIN_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. Recreation: This disability includes hobbies, sports, and other similar leisure time activities.",
      type: "select",
      field_name: "recreation",
      group: "two",
      value: [""],
      options: PAIN_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "3. Social Activity: This category refers to activities, which involve participation with friends and acquaintances other than family members. It includes parties, theater, concerts, dining out, and other social functions.",
      type: "select",
      field_name: "social-activity",
      group: "two",
      value: [""],
      options: PAIN_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "4. Occupation: This category refers to activities that are part of or directly related to one's job. This includes non-paying jobs as well, such as that of a housewife or volunteer.",
      type: "select",
      field_name: "occupation",
      group: "two",
      value: [""],
      options: PAIN_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. Sexual Behavior: This category refers to the frequency and quality of one's sex life.",
      type: "select",
      field_name: "sexual-behavior",
      group: "two",
      value: [""],
      options: PAIN_OPTIONS,
      options_is_summable: true,
    },
    {
      label:
        "6. Self Care: This category includes activities, which involve personal maintenance and independent daily living (e.g. taking a shower, driving, getting dressed, etc.)",
      type: "select",
      field_name: "self-care",
      group: "two",
      value: [""],
      options: PAIN_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "7. Life-Support Activities: This category refers to basic life supporting behaviors such as eating, sleeping and breathing.",
      type: "select",
      field_name: "life-support",
      group: "two",
      value: [""],
      options: PAIN_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 20, value: "Mild interference" },
        { min: 21, max: 40, value: "Moderate interference" },
        { min: 41, max: 70, value: "Severe interference" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      bullets: ["0-20 Mild interference", "21-40 Moderate interference", "41-70 Severe interference"],
    },
  ],
};

const whoqol_bref: IGenericAppointmentFormCategory = {
  id: "whoqol_bref",
  category: "WHOQOL-BREF",
  name: "Quality of life",
  short_code: "WHOQOL-BREF",
  forms: [
    {
      label: "What is the highest education you received?",
      type: "select",
      field_name: "highest-education",
      group: "two",
      value: [""],
      // options: ["Select", "Single", "Married", "Living as married", "Separated", "Divorced", "Widowed"],
      options: [
        { label: "Single", value: "single" },
        { label: "Married", value: "married" },
        { label: "Living as married", value: "living as married" },
        { label: "Separated", value: "separated" },
        { label: "Divorced", value: "divorced" },
        { label: "Widowed", value: "widowed" },
      ],
    },
    {
      label: "Are you currently ill?",
      type: "select",
      field_name: "currently-ill",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
    },
    {
      label: "Do you get the kind of support from others that you need?",
      type: "select",
      field_name: "kind-support",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_ONE,
      options_is_summable: true,
    },
    {
      label: "1. How would you rate your quality of life?",
      type: "select",
      field_name: "quality-life",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_TWO,
      options_is_summable: true,
    },
    {
      label: "2. How satisfied are you with your health?",
      type: "select",
      field_name: "satisfied-health",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "3. To what extent do you feel that physical pain prevents you from doing what you need to do?",
      type: "select",
      field_name: "what-extent",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_FOUR_REVERSED,
      options_is_summable: true,
    },
    {
      label: "4. How much do you need any medical treatment to function in your daily life?",
      type: "select",
      field_name: "medical-treatment",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_FOUR_REVERSED,
      options_is_summable: true,
    },
    {
      label: "5. How much do you enjoy life?",
      type: "select",
      field_name: "enjoy-life",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_FOUR,
      options_is_summable: true,
    },
    {
      label: "6. To what extent do you feel your life to be meaningful?",
      type: "select",
      field_name: "meaningful-life",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_FOUR,
      options_is_summable: true,
    },
    {
      label: "7. How well are you able to concentrate?",
      type: "select",
      field_name: "concentrate-well",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_FOUR,
      options_is_summable: true,
    },
    {
      label: "8. How safe do you feel in your daily life?",
      type: "select",
      field_name: "daily-life",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_FOUR,
      options_is_summable: true,
    },
    {
      label: "9. How healthy is your physical environment?",
      type: "select",
      field_name: "physical-environment",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_FOUR,
      options_is_summable: true,
    },
    {
      label: "10. Do you have enough energy for everyday life?",
      type: "select",
      field_name: "enough-energy",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_ONE,
      options_is_summable: true,
    },
    {
      label: "11. Are you able to accept your bodily appearance?",
      type: "select",
      field_name: "bodily-appearance",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_ONE,
      options_is_summable: true,
    },
    {
      label: "12. Have you enough money to meet your needs?",
      type: "select",
      field_name: "money-needs",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_ONE,
      options_is_summable: true,
    },
    {
      label: "13. How available to you is the information that you need in your day-to-day life?",
      type: "select",
      field_name: "available-information",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_ONE,
      options_is_summable: true,
    },
    {
      label: "14. To what extent do you have the opportunity for leisure activities?",
      type: "select",
      field_name: "leisure-activities",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_ONE,
      options_is_summable: true,
    },
    {
      label: "15. How well are you able to get around?",
      type: "select",
      field_name: "able-around",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_TWO,
      options_is_summable: true,
    },
    {
      label: "16. How satisfied are you with your sleep?",
      type: "select",
      field_name: "your-sleep",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "17. How satisfied are you with your ability to perform your daily living activities?",
      type: "select",
      field_name: "living-activities",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "18. How satisfied are you with your capacity for work?",
      type: "select",
      field_name: "capacity-work",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "19. How satisfied are you with yourself?",
      type: "select",
      field_name: "satisfied-yourself",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "20. How satisfied are you with your personal relationships?",
      type: "select",
      field_name: "satisfied-personal-relationships",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "21. How satisfied are you with your sex life?",
      type: "select",
      field_name: "satisfied-sex-life",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "22. How satisfied are you with the support you get from your friends?",
      type: "select",
      field_name: "satisfied-support-friends",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "23. How satisfied are you with the conditions of your living place?",
      type: "select",
      field_name: "conditions-living",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "24. How satisfied are you with your access to health services?",
      type: "select",
      field_name: "access-services",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "25. How satisfied are you with your transport?",
      type: "select",
      field_name: "transport-satisfied",
      group: "two",
      value: [""],
      options: WHOQOL_OPTION_THREE,
      options_is_summable: true,
    },
    {
      label: "26. How often do you have negative feelings such as blue mood, despair, anxiety, depression?",
      type: "select",
      field_name: "transport-satisfied",
      group: "two",
      value: [""],
      options: [
        {
          label: "Never",
          value: "5",
          is_shaded: false,
        },
        {
          label: "Seldom",
          value: "4",
          is_shaded: false,
        },
        {
          label: "Quite often",
          value: "3",
          is_shaded: false,
        },
        {
          label: "Very often",
          value: "2",
          is_shaded: false,
        },
        {
          label: "Always",
          value: "1",
          is_shaded: false,
        },
      ],
      options_is_summable: true,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 20, value: "Very low quality of life" },
        { min: 21, max: 40, value: "Low quality of life" },
        { min: 41, max: 60, value: "Moderate / average quality of life" },
        { min: 61, max: 80, value: "High quality of life" },
        { min: 81, max: 100, value: "Very high quality of life" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      description: "Scored in 4 domains: physical health, psychological, social relationships, environment",
      subDescription: "WHO recommends converting raw domain scores to a 0–100 scale for easier interpretation.",
      bullets: ["0-20 Very low quality of life", "21–40 Low", "41-60 Moderate / average", "61–80 High", "81-100 Very high quality of life"],
    },
  ],
};

const child_trauma: IGenericAppointmentFormCategory = {
  id: "child_trauma",
  category: "The Child PTSD Symptom Scale For DSM-V - Trauma Screen",
  short_code: "CPSS-V SR",
  forms: [
    {
      id: "intructions",
      label: "TRAUMA SCREEN (OPTIONAL – IF NEEDED)",
      type: "heading",
      description:
        "Many children go through frightening or stressful events. Below is a listed of frightening or stressful events that can happen. Mark YES if you have experienced any of these events. Mark NO if you have not experienced these events.",
    },
    {
      label: "1. A severe natural disaster such as a flood, tornado, hurricane, earthquake, or fire",
      type: "select",
      field_name: "severe-disaster",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "2. Serious accident or injury caused by a car or bike crash, being bitten by a dog, or caused by playing sports",
      type: "select",
      field_name: "car-accident-injury",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "3. Being robbed by threat, force, or weapon",
      type: "select",
      field_name: "robbed-weapon",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "4. Being slapped, punished, or beaten by a relative",
      type: "select",
      field_name: "slapped-relative",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "5. Being slapped, knifed, or beaten by a stranger",
      type: "select",
      field_name: "slapped-stranger",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "6. Seeing a relative get slapped, punished, or beaten",
      type: "select",
      field_name: "seeing-relative-slapped",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "7. Seeing somebody in your community being slapped, punished, or beaten",
      type: "select",
      field_name: "somebody-community",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "8. Being touched in your sexual/private parts by an adult/someone older who should not be touching you there",
      type: "select",
      field_name: "touched-sexual",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "9. Being forced/pressured to have sex at a time when you could not say no",
      type: "select",
      field_name: "forced-sex",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "10. A family member or somebody close dying suddenly or in a violent way",
      type: "select",
      field_name: "somebody-violent",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "11. Being attacked, shot, stabbed, or seriously injured",
      type: "select",
      field_name: "attacked-seriously",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "12. Seeing someone be attacked, shot, stabbed, or seriously injured or killed",
      type: "select",
      field_name: "someone-shot",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "13. Having a stressful or frightening medical procedure",
      type: "select",
      field_name: "stressful-procedure",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "14. Being around a war",
      type: "select",
      field_name: "war",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "15. Any other stressful or frightening event",
      type: "select",
      field_name: "war",
      group: "two",
      value: [""],
      options: PLAIN_YES_NO_OPTIONS,
      options_is_summable: true,
    },
    {
      label: "If Yes, Describe",
      type: "text",
      field_name: "describe-event",
      group: "two",
      value: "",
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    // {
    //   label: "Total",
    //   type: "options-total",
    //   field_name: "total-count",
    //   group: "eight",
    //   value: "",
    //   disabled: true,
    // },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "instruction_2",
      type: "heading",
      label: "If child answers “Yes” to any item →",
      description: "Clinician proceeds with PTSD symptom screening (e.g., CPSS-V SR) based on that event",
    },
  ],
};

const child_cpss: IGenericAppointmentFormCategory = {
  id: "child_cpss",
  category: "The Child PTSD Symptom Scale For DSM-V",
  short_code: "CPSS-V SR",
  forms: [
    {
      label:
        "Please write down the scary or upsetting thing that bothers you the most when you think about it (this should be the event you listed in the Trauma Screen, if the Trauma Screen was used):",
      type: "textarea",
      field_name: "scary-upsetting",
      group: "one",
      value: "",
      textareaHeight: "120px",
      hideCharCount: true,
    },
    {
      label: "When did it happen?",
      type: "text",
      field_name: "happen-when",
      group: "one",
      value: "",
    },
    {
      label:
        "These questions ask about how you feel about the upsetting thing you wrote down. Read each question carefully. Then circle the number (0-4) that best describes how often that problem has bothered you IN THE LAST MONTH.",
      type: "text",
      field_name: "",
      group: "one",
      value: "",
    },
    {
      label: "1. Having upsetting thoughts or pictures about it that came into your head when you didn't want them to",
      type: "select",
      field_name: "fails-attention",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: "2. Having bad dreams or nightmares",
      type: "select",
      field_name: "bad-dreams",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: "3. Acting or feeling as if it was happening again (seeing or hearing something and feeling as if you are there again)",
      type: "select",
      field_name: "acting-hearing",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: "4. Feeling upset when you remember what happened (for example, feeling scared, angry, sad, guilty, confused)",
      type: "select",
      field_name: "feeling-scared",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label:
        "5. Having feelings in your body when you remember what happened (for example, sweating, heart beating fast, stomach or head hurting)",
      type: "select",
      field_name: "remember-happened",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: "6. Trying not to think about it or have feelings about it",
      type: "select",
      field_name: "feelings-about",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label:
        "7. Trying to stay away from anything that reminds you of what happened (for example, people, places, or conversations about it)",
      type: "select",
      field_name: "anything-reminds",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: "8. Not being able to remember an important part of what happened",
      type: "select",
      field_name: "remember-able",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `9. Having bad thoughts about yourself, other people, or the world (for example, "I can't do anything right", "All people are bad", "The world is a scary place")`,
      type: "select",
      field_name: "cant-anything",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `10. Thinking that what happened is your fault (for example, "I should have known better", "I shouldn't have
        done that", "I deserved it")`,
      type: "select",
      field_name: "known-better",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `11. Having strong bad feelings (like fear, anger, guilt, or shame)`,
      type: "select",
      field_name: "strong-bad-anger",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `12. Having much less interest in doing things you used to do`,
      type: "select",
      field_name: "less-interest",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `13. Not feeling close to your friends or family or not wanting to be around them`,
      type: "select",
      field_name: "feeling-close",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `14. Trouble having good feelings (like happiness or love) or trouble having any feelings at all`,
      type: "select",
      field_name: "feelings-happiness",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `15. Getting angry easily (for example, yelling, hitting others, throwing things)`,
      type: "select",
      field_name: "angry-easily",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `16. Doing things that might hurt yourself (for example, taking drugs, drinking alcohol, running away, cutting
        yourself)`,
      type: "select",
      field_name: "hurt-drugs",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `17. Being very careful or on the lookout for danger (for example, checking to see who is around you and what is
        around you)`,
      type: "select",
      field_name: "very-careful-danger",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `18. Being jumpy or easily scared (for example, when someone walks up behind you, when you hear a loud
        noise)`,
      type: "select",
      field_name: "noise",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `19. Having trouble paying attention (for example, losing track of a story on TV, forgetting what you read,
        unable to pay attention in class)`,
      type: "select",
      field_name: "attention-losing",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `20. Having trouble falling or staying asleep`,
      type: "select",
      field_name: "trouble-falling",
      group: "two",
      value: [""],
      options: CHILD_CPSS_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `Have the problems above been getting in the way of these parts of your life IN THE PAST MONTH?`,
      type: "text",
      field_name: "",
      group: "four",
      value: "",
    },
    {
      label: `21. Fun things you want to do`,
      type: "select",
      field_name: "fun-things",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `22. Doing your chores`,
      type: "select",
      field_name: "doing-chores",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `23. Relationships with your friends`,
      type: "select",
      field_name: "your-friends",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `24. Praying`,
      type: "select",
      field_name: "praying",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `25. Schoolwork`,
      type: "select",
      field_name: "schoolwork_",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `26. Relationships with your family`,
      type: "select",
      field_name: "your",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: false,
    },
    {
      label: `27. Being happy with your life`,
      type: "select",
      field_name: "your",
      group: "six",
      value: [""],
      options: CHILD_PTSD_BOOLEAN_OPTIONS,
      options_is_summable: false,
    },
    {
      label: "",
      type: "line",
      field_name: "",
      group: "seven",
      value: "",
    },
    {
      label: "Total",
      type: "options-total",
      field_name: "total-count",
      group: "two-e",
      value: "",
      disabled: true,
    },
    {
      label: "Scoring Range",
      type: "scoring-range",
      field_name: "scoring-range",
      group: "two-e",
      value: "",
      disabled: true,
      range_options: [
        { min: 0, max: 10, value: "Minimal" },
        { min: 11, max: 20, value: "Mild" },
        { min: 21, max: 40, value: "Moderate" },
        { min: 41, max: 60, value: "Severe" },
      ],
    },
    {
      label: "Comments",
      type: "textarea",
      field_name: "comments",
      group: "one-e",
      value: "",
      textareaHeight: "80px",
      hideCharCount: true,
    },
    {
      label: "Signature",
      type: "text",
      field_name: "asrs-signature",
      group: "two-f",
      value: "",
    },
    {
      label: "Date",
      type: "date",
      field_name: "asrs-date",
      group: "two-f",
      value: "",
    },
    {
      id: "scoring-system",
      type: "heading",
      label: "Scoring System",
      description:
        "A score of ≥31 often aligns with clinical levels of PTSD in research, but functional impairment (Items 21–27) must also be considered for diagnosis.",
      subDescription:
        "The functional impairment section does not count toward the symptom score (Items 1–20), but it's crucial for clinical diagnosis.",
      bullets: [
        "0–10 Minimal Below clinical concern, mild or no symptoms",
        "11–20 Mild Subthreshold symptoms; monitor or re-screen later",
        "21–40 Moderate Likely PTSD; clinical assessment recommended",
        "41–60 Severe Strong indication of PTSD; clinical referral warranted",
      ],
    },
  ],
};

export const appt_tools_forms: IGenericAppointmentFormCategory[] = [
  general_anxiety_disorder,
  suicide_screening,
  mood_disorder_form,
  // client_health_form,
  substance_abuse_crafft,
  substance_abuse_mast,
  gambling_screening,
  substance_abuse_dast,
  substance_abuse_sassi,
  asrs,
  oasis,
  severity_measure_for_child_anxiety,
  brief_grief_questionnaire,
  teacher_parent_rating_scale,
  child_adhd,
  child_ptsd,
  dast_10,
  sadq,
  dependence_ldq,
  phq,
  center_for_epidemiological_studies,
  nida_m_assist,
  kessler,
  ocir,
  parenting_hassles,
  dsm_week_version,
  dsm_month_version,
  perceived_social_support,
  readiness_alcohol_change,
  readiness_drug_change,
  relationship,
  rosenberg_scale,
  social_phobia,
  somatic,
  trauma_screening,
  wsas,
  pain_index,
  whoqol_bref,
  child_trauma,
  child_cpss,
];
