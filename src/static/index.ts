export * from "./assessment-forms";
export * from "./treatment-plan-forms";
export * from "./tools-forms";

export * from "./progress-note-forms";

// r4 = repeat 4

export const form_builder_grid_groups = {
  one: "1fr",
  two: { sm: "1fr", "3sm": "1fr 1fr" },
  three: { sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)" },
  four: "1fr",
  r4: { sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)", "4sm": "repeat(4, 1fr)" },
  five: "1fr",
  six: { sm: "1fr", "3sm": "1fr 1fr" },
  seven: "1fr",
  eight: { sm: "1fr", "3sm": "1fr 1fr" },
  nine: { sm: "1fr", "3sm": "1fr 1fr" },
  ten: { sm: "1fr", "3sm": "1fr 1fr" },
  eleven: "1fr",
  twelve: { sm: "1fr", "3sm": "1fr 1fr" },
  thirteen: "1fr",
  fourtenth: { sm: "1fr", "3sm": "1fr 1fr" },
  fifteenth: { sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)", "4sm": "repeat(4, 1fr)" },
  sixteenth: { sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)" },
  seventeenth: { sm: "1fr", "2sm": "repeat(2, 1fr)", "3sm": "repeat(3, 1fr)", "4sm": "repeat(4, 1fr)" },
  "321": "3fr 2fr 1fr",
  "5fr": "1.25fr 0.4975fr 1fr 1fr 1fr",
  "***": "1.25fr 3fr",
  "****": "1fr 3fr",
  "*****": "2fr 1fr 0.5fr",
  "": "1fr",
} as const;

export type FormBuilderGridGroupKeys = keyof typeof form_builder_grid_groups;

// For the task you would be doing, Don't change the original title: "..." text in the form objects.

// 1. merge the objects with title: "1. ..." into the next object with type: "latest-options"

// 2. Change title: to label:
// 3. update name: to field_name:
// 4. update answer: to value: "" or [""] if the object type is latest-options
// 5. Update latest-options to select
// 6. Add options_is_summable: true to objects with type: "latest-options"
// 7. Change latestOptions: to options:
// 8. Change type: "textarea-comment" to type: "textarea"
// 9. Change type: "input" to type: "text"
// 10. Add hideCharCount: true to objects with type: "textarea"
// 11. change type: 'total-count' to type: "options-total"
// 12. if the object has type: "options-total", add disabled: true
// 13. if the object has type: "options-total", and the label is "", change it to "Total"
