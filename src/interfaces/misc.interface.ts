import { ApiResponse } from "./base.interface";

export interface IndustryDataRo {
  industry_id: string;
  name: string;
}

export interface CountryListDataRo {
  capital: string;
  iso2: string;
  iso3: string;
  name: string;
  numeric_code: string;
  phone_code: string;

  states: {
    id: number;
    latitude: string;
    longitude: string;
    name: string;
    state_code: string;
    type: null;
  }[];
}

export interface ServiceOfferringListDataRo {
  name: string;
  description: string;
  service_offer_id: string;
}

export interface BankListDataRo {
  code: string;
  name: string;
  slug: string;
}

export interface ProviderScheduleDateDataRo {
  /** available day of the week, 0 = sunday */
  av_day: number;
}

export interface ProviderScheduleTimeDataRo {
  end_time: string;
  start_time: string;

  /**
   * - 0 = Available
   * - 1 = Booked
   */
  status: number;
}

export interface ProviderSpecialTrainingDataRo {
  name: string;
  description: string;
  training_id: string;
}

export interface ServiceCategoryDataRo {
  createdAt: string;
  description: string;
  name: string;
  service_data: {
    description: string;
    goal: string;
    name: string;
    service_category_id: string[];
    service_offer_id: string;
    status: number;
    updatedAt: string;
    _id: string;
  }[];
  status: number;
  // _id: string;
  service_cat_id: string;
}

export interface BookingPricesDataRo {
  name: string;
  value: string;
}

export interface ExclusiveServiceDataRo {
  focus: string;
  format: string;
  service_exclusive_id: string;
  session: string;
  title: string;
}

export type BankListRo = ApiResponse<BankListDataRo[]>;
export type CountryListRo = ApiResponse<CountryListDataRo[]>;
export type IndustryListDataRo = ApiResponse<IndustryDataRo[]>;
export type ServiceOfferringListRo = ApiResponse<ServiceOfferringListDataRo[]>;
export type ProviderScheduleDateRo = ApiResponse<ProviderScheduleDateDataRo[]>;
export type ProviderScheduleTimeRo = ApiResponse<ProviderScheduleTimeDataRo[]>;
export type ProviderSpecialTrainingRo = ApiResponse<ProviderSpecialTrainingDataRo[]>;
export type ServiceCategoryRo = ApiResponse<ServiceCategoryDataRo[]>;
export type BookingPricesRo = ApiResponse<BookingPricesDataRo[]>;
export type ExclusiveServiceListRo = ApiResponse<ExclusiveServiceDataRo[]>;

export type EducationLevelListRo = ApiResponse<string[]>;
export type ReligionListRo = ApiResponse<string[]>;
export type PreferredLanguageListRo = ApiResponse<string[]>;
export type MaritalStatusListRo = ApiResponse<string[]>;
export type ProviderDocumentListRo = ApiResponse<string[]>;
export type ProviderCertificationListRo = ApiResponse<string[]>;
