import { ApiResponse } from "./base.interface";

export interface NotificationListDataRo {
  id: number;
  datetime: string;
  read: boolean;
  message: string;

  body: string;
  createdAt: string;
  data: unknown;
  notification_id: string;
  status: number;
  subject: string;
  updatedAt: string;
  user_id: string;
  user_type: string;
}

export type NotificationListRo = ApiResponse<NotificationListDataRo[]>;
