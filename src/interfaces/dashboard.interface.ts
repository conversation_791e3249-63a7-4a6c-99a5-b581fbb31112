import { ApiResponse } from "./base.interface";

export interface StatDataRo {
  count_stat: {
    total_appt_count: number;
    total_appt_upcoming: number;
    total_earning: number;
    wallet_balance: number;
  };

  demo_stat: {
    total_completed: number;
    total_female: number;
    total_male: number;
  };
}

export interface GraphDataRo {
  month: string;
  total_canceled: number;
  total_completed: number;
  total_upcoming: number;
}

export interface RatingDataRo {
  rating_stat: {
    percent_fivestar: number;
    percent_fourstar: number;
    percent_onestar: number;
    percent_threestar: number;
    percent_twostar: number;
    total_count: number;
    total_fivestar: number;
    total_fourstar: number;
    total_onestar: number;
    total_threestar: number;
    total_twostar: number;
  };

  service_stat: {
    service_offer_name: string;
    total_count: number;
  }[];
}

export interface MostBookedDaysDataRo {
  day: string;
  total_count: number;
}

export type StatRo = ApiResponse<StatDataRo>;
export type GraphRo = ApiResponse<GraphDataRo[]>;
export type RatingRo = ApiResponse<RatingDataRo>;

export type TopBookedServicesRo = ApiResponse<RatingDataRo["service_stat"]>;
export type MostBookedDaysRo = ApiResponse<MostBookedDaysDataRo[]>;
