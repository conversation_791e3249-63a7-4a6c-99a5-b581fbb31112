import { ApiResponse } from "./base.interface";

export type AccountUnionType = "individual";

export interface UserDataRo {
  account_type: AccountUnionType;
  avatar: string;
  industry_name: string;
  gender: string;

  bank_data: {
    account_number: string;
    bank_code: string;
    bank_name: string;
    account_name: string;
  };
  charge_currency: "NGN" | "USD";
  biz_description: string;
  business_data: {
    account_mgt: {
      /**
       * - 0 - Self Managed
       * - 1 - Third Party
       */
      mgt_type: number;

      /**
       * - 0 - Pending (needs approval)
       * - 1 - Approved
       */
      status: number;
    };
    payment_pref: { payment_option: number; status: number; doc_link: string };
    reg_number: string;
    regdoc_link: string;
    tin_number: string;
  };
  comm_mode: string;
  contact_person: {
    email: string;
    job_title: string;
    name: string;
    phone_number: string;
    phone_prefix: string;
  };
  createdAt: string;
  deletion_data: { status: number };
  dob: string;
  email: string;

  /**
   * used for calculating wallet unit
   */
  funding_unitrate: number;

  /** 0 | 1
   * - 0 Not verified
   * - 1 Verified
   */
  email_status: number;
  industry_id: string;
  name: string;
  phone_prefix: string;
  phone_number: string;
  preferred_lan: string[];
  residence_address: string;
  residence_country: string;
  residence_state: string;

  /**
   * - 0 - Awaiting Admin Approval
   * - 1 - Active
   * - 2 - Account Suspended
   */
  status: number;
  updatedAt: string;
  user_id: string;
  user_type: "provider";
  specialty: string;
  service_cat_id: string;
  religion: string;
  service_start_year: number;

  signup_stage: {
    /**
     * This field is meant to be used to check if the user has completed the provider setup
     *  - 0 - Nothing
     *  - 1 - Personal info added (Not completed)
     *  - 2 - Education added (Not completed)
     *  - 3 - Certification added (Not completed)
     *  - 4 - Professional training added (Not completed)
     *  - 5 - Documents added (Not completed)
     *  - 6 - Service offer added (Not completed)
     *  - 7 - Availability added (Not completed)
     *  - 8 - Association added (completed)
     * */

    provider: number;

    /**
     * This field is meant to be used to check if the user has completed the organisation setup
     *  - 0 - Nothing
     *  - 1 - Service added (Not completed)
     *  - 2 - Tier added (Completed)
     * */
    organisation: number;
  };
  __v: number;
}

export interface SigninDataRo extends UserDataRo {
  token: string;
}

export interface SpecialTrainingDataRo {
  name: string;
  description: string;
  training_id: string;
}

export interface EducationLevelDataRo {
  createdAt: string;
  degree: string;
  education_dataid: string;
  level: string;
  location: string;
  name: string;
  updatedAt: string;
  user_id: string;
  year: number;
}

export interface PublicationDataRo {
  createdAt: string;
  publication_id: string;
  name: string;
  updatedAt: string;
  user_id: string;
  year: number;
}

export interface CertificationDataRo {
  createdAt: string;
  certification_id: string;
  cert_name: string;
  country: string;
  cert_number: string;
  state: string;
  updatedAt: string;
  user_id: string;
  cert_year: number;
}
export interface AssociationDataRo {
  createdAt: string;
  association_id: string;
  name: string;
  updatedAt: string;
  user_id: string;
  country: string;
  state: string;
  member_id: string;
}

export interface ProviderDocumentDataRo {
  document_type: string;
  // updatedAt: string;
  user_id: string;
  document_id: string;
  createdAt: string;

  /**
   * - 0 - Pending
   * - 1 - Approved
   * - 2 - Rejected
   */
  status: number;
}

export type SigninRo = ApiResponse<SigninDataRo>;
export type UserRo = ApiResponse<UserDataRo>;
export type SpecialTrainingRo = ApiResponse<SpecialTrainingDataRo[]>;
export type EducationLevelRo = ApiResponse<EducationLevelDataRo[]>;
export type PublicationRo = ApiResponse<PublicationDataRo[]>;
export type CertificationRo = ApiResponse<CertificationDataRo[]>;
export type AssociationRo = ApiResponse<AssociationDataRo[]>;
export type ProviderDocumentRo = ApiResponse<ProviderDocumentDataRo[]>;
