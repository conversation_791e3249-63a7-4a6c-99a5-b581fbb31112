import { ApiResponse } from "./base.interface";

export interface ServiceRateDataRo {
  amount: number;
  description: string;
  service_offer_id: string;
  service_offer_name: string;
}

export interface AvailabilityDataRo {
  av_day: number;

  /** 0 - Not available,
   *  1 - Available */
  is_active: number;
  time_data: {
    start_time: number;
    end_time: number;
    schedule_id: string;

    /** 0 - Not available,
     *  1 - Available */
    is_active: number;
  }[];
}

export interface ExclusiveServiceDbDataRo {
  name: string;
  amount: number;
  service_exclusive_id: string;
}

export interface ResolveBankAccountNumberDataRo {
  account_name: string;
  account_number: string;
  bank_id: number;
}

export type ServiceRateListRo = ApiResponse<ServiceRateDataRo[]>;
export type AvailabilityListRo = ApiResponse<AvailabilityDataRo[]>;
export type ResolveBankAccountNumberRo = ApiResponse<ResolveBankAccountNumberDataRo>;
export type ExclusiveServiceDbListRo = ApiResponse<ExclusiveServiceDbDataRo[]>;
