import { ApiResponse } from "./base.interface";

export interface WalletListDataRo {
  id: number;
  datetime: string;
  description: string;
  unit: number;
  type: string;

  actual_amount: number;
  amount: number;
  createdAt: string;
  data_mode: "live";
  payment_method: string;
  payment_type: string;
  save_card: false;
  sponsor_id: string;

  /**
   * - 1 - Successful
   * - 2 - Failed
   * - 3 - Abandoned
   */
  status: string | number;
  transaction_id: string;

  /**
   * - 1 - Credit
   * - 2 - Debit
   */
  transaction_type: number;
  updatedAt: string;
  user_id: string;
  user_type: "org";
}

export interface InitFundWalletDataRo {
  amount: number;
  paystack_key: string;
  transaction_id: string;
}

export interface WalletStatDataRo {
  total_balance: number;
  total_credit: number;
  total_debit: number;
}

export type WalletStatRo = ApiResponse<WalletStatDataRo>;
export type InitFundWalletRo = ApiResponse<InitFundWalletDataRo>;
export type WalletListRo = ApiResponse<WalletListDataRo[]>;
