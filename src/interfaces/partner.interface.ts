import { ApiResponse } from "./base.interface";
import { WalletListDataRo } from "./wallet.interface";

export interface PartnerListDataRo {
  max_amount: number;
  min_amount: number;
  org_data: {
    account_type: string;
    avatar: string;
    biz_description: string;
    industry_name: string;
    name: string;
  };
  org_id: string;
  provider_id: string;
  status: string | number;

  service_data: {
    max_amount: number;
    min_amount: number;
    name: string;
    service_offer_id: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface PartnerEarningDataRo {
  actual_amount: number;
  amount: number;
  createdAt: string;
  data_mode: string;
  description: string;
  payment_type: string;
  sponsor_id: string;
  status: number;
  transaction_id: string;
  transaction_type: number;
  updatedAt: string;
  user_id: string;
}

export type PartnerListRo = ApiResponse<PartnerListDataRo[]>;
export type PartnerByIdRo = ApiResponse<PartnerListDataRo[]>;
export type PartnerEarningRo = ApiResponse<WalletListDataRo[]>;
