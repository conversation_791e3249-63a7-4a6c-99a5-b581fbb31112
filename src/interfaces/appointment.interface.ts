import { UseQueryOptions } from "@tanstack/react-query";
import { ApiResponse } from "./base.interface";
import { SelectItem } from "@/components";
import { FormBuilderGridGroupKeys } from "@/static";

export interface AppointmentListDataRo {
  id: number;
  datetime: string;
  consultant: string;
  member: string;
  bookedby: string;
  // status: string;
  amount?: number;

  appointment_id: string;
  appt_date: string;
  appt_day: number;
  appt_schedule: string;
  appt_time: number;
  booking_ref: string;
  charge_status: number;
  comm_mode: string;
  createdAt: string;
  meeting_id: string;
  member_gender: string;

  // TODO: Verify this fields exists
  org_payer_id?: string;
  org_provider_id?: string;
  ////////

  member_data: {
    email: string;
    first_name: string;
    last_name: string;
    avatar: string;
    _id: string;

    gender: string;
    marital_status: string;
    nickname: string;
    religion: string;
  };

  member_id: string;
  member_questionnaire: {
    question: string;
    answer: string | string[];
    sub_question: string;
  }[];

  member_rated: unknown;
  member_token: string;
  notify_count: number;
  payment_option: number;

  provider_data: {
    auth_id: string;
    avatar: string;
    email: string;
    name: string;
    service_cat_id: string;
    specialty: string;
    rating: number;
  };

  rating_data: {
    comment: string;
    rating: number;
    receiver_usertype: string;
    sender_id: string;
  }[];

  provider_id: string;
  provider_rated: unknown;
  provider_token: string;
  refunded_amount: number;
  service_offer_id: string;
  service_offer_name: string;

  /**
   * - 0 - Pending
   * - 1 - Upcoming
   * - 2 - Live
   * - 3 - Completed
   * - 4 - Canceled
   */
  status: string | number;
  status_cancel: number;

  systemdata_charge: unknown;
  tier_id: string;
  updatedAt: string;
  tier_name: string;

  service_cat_id: string;
  reschedule_amount: number;
  reschedule_data: never[];

  payment_by: number;
  payment_distribution: { org_provider: number; individual_provider: number; msmt_platform: number };
  booking_link: string;

  referral_indata: {
    status: number;
    comment: string;
  };

  referral_outdata: unknown;
}

export interface AppointmentStatsDataRo {
  total_cancel: number;
  total_completed: number;
  total_count: number;
  total_spent: number;
  total_upcoming: number;
  _id: null;
}

export interface AppointmentDiagnosisDatasetDataRo {
  id: string;
  icd_9_code: string;
  icd_10_code: string;
  description: string;
}

export interface AppointmentAssessmentDataRo {
  appointment_id: string;
  createdAt: string;
  data: {
    field_name: string;
    label: string;
    type: string;
    value: string | string[];
  }[];
  data_id: string;
  data_name: string;
  data_tab: string;
  member_id: string;
  provider_id: string;
  updatedAt: string;
}

export interface AppointmentTreatmentPlanDataRo {
  appointment_id: string;
  createdAt: string;
  data: {
    field_name: string;
    label: string;
    type: string;
    value: string | string[];
  }[];
  data_id: string;
  data_name: string;
  data_tab: string;
  member_id: string;
  provider_id: string;
  updatedAt: string;
}

export interface AppointmentDiagnosisDataRo {
  appointment_id: string;
  code9: string;
  code10: string;
  createdAt: string;
  data_id: string;
  description: string;
  member_id: string;
  provider_id: string;
  updatedAt: string;
}

export interface AppointmentProgressNoteDataRo {
  appointment_id: string;
  createdAt: string;
  data: {
    field_name: string;
    label: string;
    type: string;
    value: string | string[];
  }[];
  data_id: string;
  member_id: string;
  provider_id: string;
  updatedAt: string;
}

export interface AppointmentMedicalPrescriptionDataRo {
  appointment_id: string;
  createdAt: string;
  data_id: string;
  end_date: string;
  med_dosage: string;
  med_freq: string;
  med_name: string;
  med_type: string;
  member_id: string;
  provider_id: string;
  start_date: string;
  updatedAt: string;
}

export interface MedicalRecordDataRo<T = GeneralFormDataRo> {
  provider_name: string;
  provider_specialty: string;
  createdAt: string;

  data: T[];
}

export interface GeneralFormDataRo {
  data: {
    label: string;
    value: string;
    value_text: string;
  }[];
  data_name: string;
}

export interface ProviderListDataRo {
  id: number;
  datetime: string;
  name: string;
  specialty: string;
  tier: string | number;

  rating: number;
  account_type: string;
  avatar: string;
  cat_data: { name: string; _id: string };
  charge_from: number;
  comm_mode: string[];

  user_id: string;
  service_cat_id: string;
  createdAt: string;
  updatedAt: string;

  provider_id: string;
  provider_org_id: string;

  /**
   * - 0 - Pending
   * - 1 - Active
   * - 2 - Suspended
   * - 3 - Declined
   */
  status: string | number;
  tier_id: string;
  tier_name: string;

  provider_data: {
    avatar: string;
    name: string;
    rating: number;
    service_cat_id: string;
    specialty: string;

    comm_mode: string[];
    service_start_year: number;
    special_training_data: { name: string; year: number }[];

    total_certification: number;
    total_publication: number;

    service_data: {
      name: string;
      service_offer_id: string;
      _id: string;
    }[];
  };

  service_data: {
    name: string;
    service_offer_id: string;
    amount: number;
  }[];

  tier_data: {
    name: string;
    _id: string;
  }[];

  total_appointment?: 0;
  total_earning?: 0;
  total_service?: 1;
}

export interface ServiceProviderDataRo {
  charge_from: number;
  provider_data: {
    account_service_type: string;
    account_type: string;
    avatar: string;
    name: string;
    specialty: string;
    user_id: string;
    user_type: string;
  };
}

export type DiagnosisMedicalRecordDataRo = MedicalRecordDataRo<AppointmentDiagnosisDataRo>;
export type MedicationMedicalRecordDataRo = MedicalRecordDataRo<AppointmentMedicalPrescriptionDataRo>;

export type AppointmentListRo = ApiResponse<AppointmentListDataRo[]>;
export type AppointmentByIdRo = ApiResponse<AppointmentListDataRo>;
export type AppointmentStatsRo = ApiResponse<AppointmentStatsDataRo>;
export type AppointmentDiagnosisDatasetRo = ApiResponse<AppointmentDiagnosisDatasetDataRo[]>;
export type AppointmentAssessmentRo = ApiResponse<AppointmentAssessmentDataRo | AppointmentAssessmentDataRo[]>;
export type AppointmentTreatmentPlanRo = ApiResponse<AppointmentTreatmentPlanDataRo | AppointmentTreatmentPlanDataRo[]>;
export type AppointmentDiagnosisRo = ApiResponse<AppointmentDiagnosisDataRo[]>;
export type AppointmentProgressNoteRo = ApiResponse<AppointmentProgressNoteDataRo[]>;
export type AppointmentMedicalPrescriptionRo = ApiResponse<AppointmentMedicalPrescriptionDataRo[]>;
export type MedicalRecordRo = ApiResponse<MedicalRecordDataRo[]>;
export type DiagnosisMedicalRecordRo = ApiResponse<DiagnosisMedicalRecordDataRo[]>;
export type MedicationMedicalRecordRo = ApiResponse<MedicationMedicalRecordDataRo[]>;
export type ProviderListRo = ApiResponse<ProviderListDataRo[]>;
export type ServiceProviderListRo = ApiResponse<ServiceProviderDataRo[]>;

type IGenericAppointmentFormGroupType = FormBuilderGridGroupKeys | `${FormBuilderGridGroupKeys}-${string}`;

export interface IGenericAppointmentTextForm {
  type: "text"; // | "textarea" | "select" | "checkbox" | "radio" | "number" | "date" | "time";
  label: string;
  field_name: string;
  default_value?: string;
  value?: string;
  placeholder?: string;

  /** Points to the heading id this form appears under */
  appears_under?: string;
  group?: IGenericAppointmentFormGroupType; // Optional group name for categorization

  // placeholder?: string;
  // required?: boolean;
  // options?: { label: string; value: string }[];
  // default_value?: string | string[];
  // value?: string | string[];
  // min?: number;
  // max?: number;
  // step?: number;
  // min_length?: number;
  // max_length?: number;
  // pattern?: string;
}

export interface IGenericAppointmentTextareaForm {
  type: "textarea";
  label: string;
  field_name: string;
  default_value?: string;
  value?: string;
  placeholder?: string;

  /** Points to the heading id this form appears under */
  appears_under?: string;
  group?: IGenericAppointmentFormGroupType; // Optional group name for categorization
  textareaHeight?: string;
  hideCharCount?: boolean;
}

export interface IGenericAppointmentNumberForm {
  type: "number";
  label: string;
  field_name: string;
  default_value?: string;
  value?: string;
  placeholder?: string;

  /** Points to the heading id this form appears under */
  appears_under?: string;
  group?: IGenericAppointmentFormGroupType; // Optional group name for categorization

  min?: number;
  max?: number;
  step?: number;
}

interface IGenericAppointmentLineForm {
  type: "line";
  label: string;
  field_name: string;
  default_value?: string;
  value: string;

  /** Points to the heading id this form appears under */
  appears_under?: string;
  group?: IGenericAppointmentFormGroupType; // Optional group name for categorization
}
export interface IGenericAppointmentDateForm {
  type: "date";
  label: string;
  field_name: string;
  default_value?: string;
  placeholder?: string; // Optional placeholder for the select field

  /** Format: YYYY-MM-DD */
  value?: string;

  /** Points to the heading id this form appears under */
  appears_under?: string;
  group?: IGenericAppointmentFormGroupType; // Optional group name for categorization
}
export interface IGenericAppointmentSelectForm {
  type: "select";
  label: string;
  field_name: string;
  default_value?: string[];
  value?: string[];
  value_text?: string | string[];
  options: (SelectItem & { is_shaded?: boolean })[];
  placeholder?: string; // Optional placeholder for the select field

  /** Points to the heading id this form appears under */
  appears_under?: string;
  group?: IGenericAppointmentFormGroupType; // Optional group name for categorization
  multiple?: boolean; // Whether multiple selections are allowed
  options_is_summable?: boolean;
  can_filter_list?: boolean;
}

export interface IGenericUnresolvedAppointmentSelectForm extends Omit<IGenericAppointmentSelectForm, "options" | "type"> {
  type: "unresolved-select";
  options: (...args: string[]) => UseQueryOptions<unknown>;
  transform: <T = unknown>(data: T) => SelectItem[];
}
export interface IGenericAppointmentFormHeading {
  id: string;
  type: "heading";
  label: string;
  hidden?: boolean; // Whether the heading is hidden by default
  description?: string; // Optional description for the heading
  subDescription?: string; // Optional sub description for the heading
  fontWeight?: string; // Optional font weight for the heading
  fontSize?: string; // Optional font size for the heading
  bullets?: (string | { title: string; bullets: string[] })[];

  /** Optional table data for the heading */
  table?: {
    th: string[];
    td: string[][];
  };
}
export interface IGenericAppointmentFormOptionTotal {
  type: "options-total";
  label: string;
  field_name: string;
  group?: IGenericAppointmentFormGroupType;
  hidden?: boolean; // Whether the heading is hidden by default
  disabled?: boolean;
  value: string;

  /**
   * If true, the forms under the heading will be used to calculate the total
   * If false, all select fields will be used to calculate the total
   *
   * Points to the heading id this form appears under
   */
  use_heading_group?: boolean;
  appears_under?: string;
}

export interface IGenericAppointmentFormScoringRange {
  type: "scoring-range";
  label: string;
  field_name: string;
  group?: IGenericAppointmentFormGroupType;
  hidden?: boolean; // Whether the heading is hidden by default
  disabled?: boolean;
  value: string;
  range_options: { min: number; max: number; value: string }[];

  /**
   * If true, the forms under the heading will be used to calculate the total
   * If false, all select fields will be used to calculate the total
   *
   * Points to the heading id this form appears under
   */
  use_heading_group?: boolean;
  appears_under?: string;
}
export interface IGenericAppointmentFormCalculationResult {
  type: "calculation-result";
  label: string;
  field_name: string;
  group?: IGenericAppointmentFormGroupType;
  hidden?: boolean; // Whether the heading is hidden by default
  disabled?: boolean;
  value: string;

  /**
   * Array of field_names to calculate the result from
   * if the field_name is not found, the value will be set to 0
   * if the field_name is found but the value is not a number, the value will be set to 0
   * if `result_of` has an empty array, the value of all select fields will be used in the calculate_result function param
   */
  result_of?: string[];

  /**
   * Function to calculate the result
   * @param values - Array of values to calculate the result from
   */
  calculate_result: (values: number[]) => string;

  /**
   * Points to the heading id this form appears under
   * If not provided, the form will appear under the last heading
   * If result_of is empty or undefined, the value of all select fields attached to the heading will be used in the calculate_result function param
   */
  appears_under?: string;
}

/** Generic discriminated union of appointment form */
export type IGenericAppointmentForm =
  | IGenericAppointmentTextForm
  | IGenericAppointmentTextareaForm
  | IGenericAppointmentDateForm
  | IGenericAppointmentLineForm
  | IGenericAppointmentSelectForm
  | IGenericUnresolvedAppointmentSelectForm
  | IGenericAppointmentFormHeading
  | IGenericAppointmentFormOptionTotal
  | IGenericAppointmentFormScoringRange
  | IGenericAppointmentFormCalculationResult
  | IGenericAppointmentNumberForm;

export interface IGenericAppointmentFormCategory {
  id: string;
  category: string;
  name?: string;
  short_code?: string;
  forms: IGenericAppointmentForm[]; /// | IGenericAppointmentFormCategorySubForm[];
}

export type IGenericAppointmentFormCategorySubForm = { id: string; forms: IGenericAppointmentForm[] };
