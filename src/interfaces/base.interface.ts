/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ApiResponse<D> {
  status?: string;
  data?: D;
  total?: number;
}

export interface IListFilter {
  page?: number;
  item_per_page?: number;
  q?: string;
  sort?: string;
  order?: string;
  range?: Partial<{
    /**
     * today | this_month | all_time | custom
     */
    value: string;
    /** Format -  */
    start_date: string;
    end_date: string;
  }>;

  custom?: Partial<{
    start_date: string;
    end_date: Partial<{ e: boolean }>;
  }>;
  [key: string]: any;
}

export type ListCountRo = ApiResponse<{ total: number }>;
