import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import omit from "lodash.omit";
import { ForgotPasswordDto, LoginDto, RegisterAccountDto, ResendOtpDto, SetPasswordDto, VerifyEmailDto } from "@/schemas";
import configs from "@/config";
import { toaster } from "@/components";
import { setContextError } from "./error.store";
import { post, tryCatch, zustandSecureStoragePrototype } from "@/utils";
import { networkAwareGet, networkAwarePost } from "@/utils/networkAwareRequest";
import { SigninRo, UserDataRo } from "@/interfaces";

type Auth = {
  user_id?: string;
  token?: string;
  is_signed_in: boolean;
  email_verified: boolean;
  email?: string;
};

export type AuthState = {
  auth: Auth | null;
  loading: boolean;
  temp_signup_data?: RegisterAccountDto;
  temp_forgotPassword_data?: ForgotPasswordDto;

  user?: UserDataRo;

  timer: Partial<{
    [key in ResendOtpDto["request_type"]]: {
      start?: number;
      loading?: boolean;
    };
  }>;
};

type AuthActions = {
  signin: (data: LoginDto) => ReturnType<typeof tryCatch<SigninRo>>;
  signup: (data: RegisterAccountDto) => ReturnType<typeof tryCatch>;
  verifyEmail: (data: VerifyEmailDto) => ReturnType<typeof tryCatch>;
  resendOtp: (data: ResendOtpDto) => ReturnType<typeof tryCatch>;
  completeSignup: (data: VerifyEmailDto) => ReturnType<typeof tryCatch<SigninRo>>;

  clearTimer(type?: ResendOtpDto["request_type"]): void;
  setTimer(type: ResendOtpDto["request_type"], data: { start?: number; loading?: boolean }): void;
  forgotPassword: (email: string) => ReturnType<typeof tryCatch>;
  setPassword: (data: SetPasswordDto) => ReturnType<typeof tryCatch>;
  logout: () => void;
  getUser: () => ReturnType<typeof tryCatch>;
};

export type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      auth: {
        is_signed_in: false,
        email_verified: false,
      },
      loading: false,

      timer: {},

      setTimer(type, data) {
        set((state) => ({
          timer: {
            ...state.timer,
            [type]: {
              ...data,
            },
          },
        }));
      },

      clearTimer(type) {
        set((state) => {
          if (type) {
            return {
              timer: {
                ...state.timer,
                [type]: {
                  start: 0,
                  loading: false,
                },
              },
            };
          } else {
            return {
              timer: {},
            };
          }
        });
      },

      async signin(data) {
        set({ loading: true });
        // console.log("Submit signin dto", data);

        const toastId = toaster.create({
          type: "loading",
          title: "Signing in...",
          description: "Please wait while we sign you in",
        });

        const promise = post<SigninRo, typeof data>(`/providers/auths/login`, data);
        const result = await tryCatch(promise);

        if (!result.ok) {
          const error = result.error;
          console.log("Signin Error", error);
          setContextError("auth/signin", error, { payload: data });
          toaster.update(toastId!, {
            type: "error",
            title: "Failed to signin",
            description: error?.msg || "Please try again later",
          });
        }

        if (result.ok) {
          const { token, ...rest } = result?.data?.data || {};
          const user = rest as UserDataRo;

          set({
            user,
            auth: {
              token,
              is_signed_in: true,
              email_verified: Boolean(user?.email_status),
            },
          });

          toaster.update(toastId!, {
            type: "success",
            title: "Signed in successfully",
            description: "You have been signed in successfully",
          });
        }

        set({ loading: false });
        return result;
      },

      async signup(data) {
        console.log("Signup Data", data);

        set({ loading: true, temp_signup_data: data });

        const dto = omit(data, ["tempAccountType"]);
        const promise = post(`/providers/auths/init-register`, dto);
        const result = await tryCatch(promise);

        if (!result.ok) {
          setContextError("auth/signup", result?.error, { payload: dto });
          toaster.create({
            type: "error",
            title: result.error?.msg || result.error?.message || "Failed to sign up",
            description: "Please try again",
          });
        }

        set({ loading: false });
        return result;
      },

      async verifyEmail(data) {
        set({ loading: true });

        const dto = { ...data, otp: data.otp.join("") };
        const promise = post(`/providers/auths/confirm-otp`, dto);
        const result = await tryCatch(promise);

        if (!result.ok) {
          setContextError("auth/verify-email", result?.error, { payload: dto });
          toaster.create({
            type: "error",
            title: result.error?.msg || result.error?.message || "Failed to verify email",
            description: "Please try again",
          });
        }

        set({ loading: false });
        return result;
      },

      async completeSignup(data) {
        set({ loading: true });

        const dto = { ...data, otp: data.otp.join("") };
        const promise = post<SigninRo, typeof dto>(`/providers/auths/complete-register`, dto);
        const result = await tryCatch(promise);

        if (!result.ok) {
          setContextError("auth/complete-signup", result?.error, {
            payload: dto,
          });
          toaster.create({
            type: "error",
            title: result.error?.msg || result.error?.message || "Failed to complete signup",
            description: "Please try again",
          });
        }

        if (result.ok) {
          const { token, ...rest } = result?.data?.data || {};
          const user = rest as UserDataRo;

          set({
            user,
            auth: {
              token,
              is_signed_in: true,
              email_verified: Boolean(user?.email_status),
            },
          });
        }

        set({ loading: false, temp_signup_data: undefined });
        return result;
      },

      async resendOtp(data) {
        get().setTimer(data.request_type, {
          loading: true,
        });

        const dto = omit(data, ["request_type"]);
        const promise = networkAwarePost(`/providers/auths/resend-otp`, dto);
        const result = await tryCatch(promise);

        if (!result.ok) {
          setContextError("auth/resend-otp", result?.error, { payload: dto });
          toaster.create({
            type: "error",
            title: result.error?.msg || result.error?.message || "Failed to resend otp",
            description: "Please try again",
          });
        }

        get().setTimer(data.request_type, {
          loading: false,
        });
        return result;
      },

      async forgotPassword(email) {
        set({ loading: true, temp_forgotPassword_data: { email } });

        const dto = { email };
        const promise = post(`/providers/auths/forgot-password`, dto);
        const result = await tryCatch(promise);

        if (!result.ok) {
          setContextError("auth/forgot-password", result?.error, {
            payload: dto,
          });
          toaster.create({
            type: "error",
            title: result.error?.msg || result.error?.message || "Failed to send OTP",
            description: "Please try again",
          });
        }

        set({ loading: false });
        return result;
      },

      async setPassword(data) {
        set({ loading: true });

        const promise = post(`/providers/auths/reset-password`, data);
        const result = await tryCatch(promise);

        if (!result.ok) {
          setContextError("auth/set-password", result?.error, {
            payload: data,
          });
          toaster.create({
            type: "error",
            title: result.error?.msg || result.error?.message || "Failed to set password",
            description: "Please try again",
          });
        }

        set({ loading: false, temp_forgotPassword_data: undefined });
        return result;
      },

      logout() {
        set({
          auth: { is_signed_in: false, email_verified: false },
          user: undefined,
        });
      },

      async getUser() {
        set({ loading: true });

        const promise = networkAwareGet(`/providers/accounts`);
        const result = await tryCatch(promise);
        if (!result.ok) {
          setContextError("auth/get-user", result?.error, {
            payload: undefined,
          });

          toaster.create({
            type: "error",
            title: result.error?.msg || result.error?.message || "Failed to get user",
            description: "Please try again",
          });
        }

        if (result.ok) {
          console.log("User data", result.data);
          // set({ user: result.data.data });
        }

        set({ loading: false });
        return result;
      },
    }),
    {
      name: configs.AUTH_KEY,
      storage: createJSONStorage(() => zustandSecureStoragePrototype),
      partialize: (state) => ({
        auth: state?.auth,
        timer: state?.timer,
        user: state?.user,
        // pending_action: state.pending_action,
      }),
      onRehydrateStorage() {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        return (_state) => {
          // console.log("Auth Store Rehydrated", state);
          // console.log("Config", configs);
        };
      },
    }
  )
);
