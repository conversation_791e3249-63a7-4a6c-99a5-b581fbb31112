/* eslint-disable @typescript-eslint/no-explicit-any */
import { useStore } from "zustand";
import { produce } from "immer";
import { nanoid } from "nanoid";

import { createStore } from "zustand/vanilla";
import { StoreApi } from "zustand";
import omit from "lodash.omit";

export type ErrorType = {
  id?: string | null;
  title?: string;
  message?: string | null;
  status?: number | string | null;
  action?: {
    type: string;
    payload: any;
  } | null;
  showUser?: boolean;
  [key: string]: any;
};

interface ErrorStateActions {
  setError: (error: Partial<ErrorType>) => void;
  clearError: (type: ClearErrorType) => void;
}

interface ErrorState {
  previous: ErrorType;
  next: ErrorType;

  actions?: ErrorStateActions;
}

type ClearErrorType = "next" | "previous" | "both";

export interface AppError {
  msg: string;
  code: number;
  status: string | number;
  error: string;
  message: string;
}

const initialState: ErrorState = {
  previous: {
    id: null,
    message: null,
    status: null,
    action: null,
    showUser: false,
  },
  next: {
    id: null,
    message: null,
    status: null,
    action: null,
    showUser: false,
  },
};

export const errorStore = createStore<ErrorState>((set) => ({
  ...initialState,

  actions: {
    setError: (error: Partial<ErrorType>) =>
      set(
        produce((state: ErrorState) => {
          console.log("error state", error);
          const id = `${error.action?.type ?? "error"}-${nanoid(7)}`;
          if (state?.previous?.id === null && state?.next?.id === null) {
            state.previous = { ...error, id };
            state.next = { ...error, id };

            return;
          } else if (state?.previous?.id !== null && state?.next?.id === null) {
            state.previous = { ...state.previous };
            state.next = { ...error, id };
            return;
          } else {
            state.previous = { ...state?.next };
            state.next = { ...error, id };
            return;
          }
        })
      ),

    clearError: (type: ClearErrorType) =>
      set(
        produce((state: ErrorState) => {
          const mapActions = (type: ClearErrorType) => {
            const map = {
              next: () => {
                state.previous = { ...state.next };
                state.next = { ...initialState.next };
              },
              previous: () => {
                state.previous = { ...state.next };
                state.next = { ...initialState.next };
              },
              both: () => {
                state = { ...initialState };
              },
            };

            return map[type]();
          };

          mapActions(type);
        })
      ),
  },
}));

type SelectorType = Parameters<typeof useStore<StoreApi<ErrorState>, ErrorState>>[1];
export const useErrorStore = (selector: SelectorType) => useStore<StoreApi<ErrorState>, ErrorState>(errorStore, selector);

export function setContextError<P = any>(id: string, error: AppError, extras: { payload?: P }) {
  const actions = errorStore.getState().actions!;

  console.log("Error", error);
  actions.setError({
    status: error?.code ?? error?.status,
    message: error?.msg ?? error?.message ?? error?.error,
    action: {
      type: id,
      payload: omit(extras?.payload || {}, ["password", "confirm_password"]),
    },
    showUser: false,
  });
}
